import { FilterService } from '../FilterService';
import { SmartEduClient } from '../SmartEduClient';
import { FilterOption, CourseFilters, ApiError } from '../../types';

// Mock SmartEduClient
jest.mock('../SmartEduClient');

describe('FilterService', () => {
  let filterService: FilterService;
  let mockClient: jest.Mocked<SmartEduClient>;

  const mockStages: FilterOption[] = [
    { value: 'primary', label: '小学' },
    { value: 'middle', label: '初中' },
    { value: 'high', label: '高中' }
  ];

  const mockGrades: FilterOption[] = [
    { value: 'grade1', label: '一年级' },
    { value: 'grade2', label: '二年级' },
    { value: 'grade3', label: '三年级' }
  ];

  const mockSubjects: FilterOption[] = [
    { value: 'chinese', label: '语文' },
    { value: 'math', label: '数学' },
    { value: 'english', label: '英语' }
  ];

  const mockVersions: FilterOption[] = [
    { value: 'rjb', label: '人教版' },
    { value: 'bsb', label: '北师版' },
    { value: 'sj', label: '苏教版' }
  ];

  const mockVolumes: FilterOption[] = [
    { value: 'vol1', label: '上册' },
    { value: 'vol2', label: '下册' }
  ];

  beforeEach(() => {
    // 重置所有mock
    jest.clearAllMocks();
    
    // 创建mock客户端
    mockClient = new SmartEduClient() as jest.Mocked<SmartEduClient>;
    
    // 设置mock返回值
    mockClient.getStages.mockResolvedValue(mockStages);
    mockClient.getGradesByStage.mockResolvedValue(mockGrades);
    mockClient.getSubjectsByStageAndGrade.mockResolvedValue(mockSubjects);
    mockClient.getVersionsByStageGradeSubject.mockResolvedValue(mockVersions);
    mockClient.getVolumesByStageGradeSubjectVersion.mockResolvedValue(mockVolumes);
    
    // 创建FilterService实例
    filterService = new FilterService(mockClient, {
      ttl: 1000, // 1秒缓存用于测试
      maxSize: 10,
      cleanupInterval: 500
    });
  });

  afterEach(() => {
    filterService.destroy();
  });

  describe('getStages', () => {
    it('应该成功获取学段列表', async () => {
      const result = await filterService.getStages();
      
      expect(result).toEqual(mockStages);
      expect(mockClient.getStages).toHaveBeenCalledTimes(1);
    });

    it('应该缓存学段列表', async () => {
      // 第一次调用
      await filterService.getStages();
      // 第二次调用
      await filterService.getStages();
      
      // 应该只调用一次API
      expect(mockClient.getStages).toHaveBeenCalledTimes(1);
    });

    it('应该处理API错误', async () => {
      const error = new Error('网络错误');
      mockClient.getStages.mockRejectedValue(error);
      
      await expect(filterService.getStages()).rejects.toThrow(ApiError);
    });
  });

  describe('getGradesByStage', () => {
    it('应该成功获取年级列表', async () => {
      const result = await filterService.getGradesByStage('primary');
      
      expect(result).toEqual(mockGrades);
      expect(mockClient.getGradesByStage).toHaveBeenCalledWith('primary');
    });

    it('当学段为空时应该返回空数组', async () => {
      const result = await filterService.getGradesByStage('');
      
      expect(result).toEqual([]);
      expect(mockClient.getGradesByStage).not.toHaveBeenCalled();
    });

    it('应该缓存年级列表', async () => {
      await filterService.getGradesByStage('primary');
      await filterService.getGradesByStage('primary');
      
      expect(mockClient.getGradesByStage).toHaveBeenCalledTimes(1);
    });
  });

  describe('getSubjectsByStageAndGrade', () => {
    it('应该成功获取学科列表', async () => {
      const result = await filterService.getSubjectsByStageAndGrade('primary', 'grade1');
      
      expect(result).toEqual(mockSubjects);
      expect(mockClient.getSubjectsByStageAndGrade).toHaveBeenCalledWith('primary', 'grade1');
    });

    it('当参数不完整时应该返回空数组', async () => {
      let result = await filterService.getSubjectsByStageAndGrade('', 'grade1');
      expect(result).toEqual([]);
      
      result = await filterService.getSubjectsByStageAndGrade('primary', '');
      expect(result).toEqual([]);
      
      expect(mockClient.getSubjectsByStageAndGrade).not.toHaveBeenCalled();
    });
  });

  describe('getVersionsByStageGradeSubject', () => {
    it('应该成功获取版本列表', async () => {
      const result = await filterService.getVersionsByStageGradeSubject('primary', 'grade1', 'chinese');
      
      expect(result).toEqual(mockVersions);
      expect(mockClient.getVersionsByStageGradeSubject).toHaveBeenCalledWith('primary', 'grade1', 'chinese');
    });

    it('当参数不完整时应该返回空数组', async () => {
      const result = await filterService.getVersionsByStageGradeSubject('primary', '', 'chinese');
      expect(result).toEqual([]);
      expect(mockClient.getVersionsByStageGradeSubject).not.toHaveBeenCalled();
    });
  });

  describe('getVolumesByStageGradeSubjectVersion', () => {
    it('应该成功获取册次列表', async () => {
      const result = await filterService.getVolumesByStageGradeSubjectVersion(
        'primary', 'grade1', 'chinese', 'rjb'
      );
      
      expect(result).toEqual(mockVolumes);
      expect(mockClient.getVolumesByStageGradeSubjectVersion).toHaveBeenCalledWith(
        'primary', 'grade1', 'chinese', 'rjb'
      );
    });

    it('当参数不完整时应该返回空数组', async () => {
      const result = await filterService.getVolumesByStageGradeSubjectVersion(
        'primary', 'grade1', '', 'rjb'
      );
      expect(result).toEqual([]);
      expect(mockClient.getVolumesByStageGradeSubjectVersion).not.toHaveBeenCalled();
    });
  });

  describe('getFilterOptions', () => {
    it('应该获取完整的筛选选项', async () => {
      const result = await filterService.getFilterOptions();
      
      expect(result.stages).toEqual(mockStages);
      expect(result.grades).toEqual([]);
      expect(result.subjects).toEqual([]);
      expect(result.versions).toEqual([]);
      expect(result.volumes).toEqual([]);
    });

    it('应该根据父级筛选条件获取级联选项', async () => {
      const parentFilter: Partial<CourseFilters> = {
        stage: 'primary',
        grade: 'grade1',
        subject: 'chinese'
      };
      
      const result = await filterService.getFilterOptions(parentFilter);
      
      expect(result.stages).toEqual(mockStages);
      expect(result.grades).toEqual(mockGrades);
      expect(result.subjects).toEqual(mockSubjects);
      expect(result.versions).toEqual(mockVersions);
      expect(result.volumes).toEqual([]);
      
      expect(mockClient.getStages).toHaveBeenCalledTimes(1);
      expect(mockClient.getGradesByStage).toHaveBeenCalledWith('primary');
      expect(mockClient.getSubjectsByStageAndGrade).toHaveBeenCalledWith('primary', 'grade1');
      expect(mockClient.getVersionsByStageGradeSubject).toHaveBeenCalledWith('primary', 'grade1', 'chinese');
    });
  });

  describe('validateFilters', () => {
    it('应该验证完整的筛选条件', () => {
      const filters: CourseFilters = {
        stage: 'primary',
        grade: 'grade1',
        subject: 'chinese',
        version: 'rjb',
        volume: 'vol1'
      };
      
      const result = filterService.validateFilters(filters);
      
      expect(result.isValid).toBe(true);
      expect(result.missingFields).toEqual([]);
      expect(result.errors).toEqual([]);
    });

    it('应该检测缺失的字段', () => {
      const filters: Partial<CourseFilters> = {
        stage: 'primary',
        grade: 'grade1'
      };
      
      const result = filterService.validateFilters(filters);
      
      expect(result.isValid).toBe(false);
      expect(result.missingFields).toEqual(['subject', 'version', 'volume']);
    });

    it('应该检测字段类型错误', () => {
      const filters = {
        stage: 123, // 错误类型
        grade: 'grade1',
        subject: 'chinese',
        version: 'rjb',
        volume: 'vol1'
      } as any;
      
      const result = filterService.validateFilters(filters);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('学段必须是字符串类型');
    });
  });

  describe('缓存功能', () => {
    it('应该在TTL过期后重新获取数据', async () => {
      // 第一次调用
      await filterService.getStages();
      expect(mockClient.getStages).toHaveBeenCalledTimes(1);
      
      // 手动清理过期缓存来模拟TTL过期
      filterService.clearCache();
      
      // 第二次调用应该重新请求API
      await filterService.getStages();
      expect(mockClient.getStages).toHaveBeenCalledTimes(2);
    });

    it('应该清空缓存', async () => {
      await filterService.getStages();
      expect(mockClient.getStages).toHaveBeenCalledTimes(1);
      
      filterService.clearCache();
      
      await filterService.getStages();
      expect(mockClient.getStages).toHaveBeenCalledTimes(2);
    });

    it('应该提供缓存统计信息', async () => {
      await filterService.getStages();
      await filterService.getGradesByStage('primary');
      
      const stats = filterService.getCacheStats();
      
      expect(stats.size).toBe(2);
      expect(stats.maxSize).toBe(10);
      expect(stats.entries).toHaveLength(2);
    });
  });

  describe('错误处理', () => {
    it('应该处理网络错误', async () => {
      const networkError = new Error('网络连接失败');
      mockClient.getStages.mockRejectedValue(networkError);
      
      await expect(filterService.getStages()).rejects.toThrow(ApiError);
    });

    it('应该处理API错误', async () => {
      const apiError = new ApiError('API调用失败');
      mockClient.getGradesByStage.mockRejectedValue(apiError);
      
      await expect(filterService.getGradesByStage('primary')).rejects.toThrow(ApiError);
    });
  });

  describe('级联逻辑', () => {
    it('应该正确处理级联加载', async () => {
      const parentFilter: Partial<CourseFilters> = {
        stage: 'primary',
        grade: 'grade1',
        subject: 'chinese',
        version: 'rjb'
      };
      
      const result = await filterService.getFilterOptions(parentFilter);
      
      // 验证所有级联调用都被执行
      expect(mockClient.getStages).toHaveBeenCalledTimes(1);
      expect(mockClient.getGradesByStage).toHaveBeenCalledWith('primary');
      expect(mockClient.getSubjectsByStageAndGrade).toHaveBeenCalledWith('primary', 'grade1');
      expect(mockClient.getVersionsByStageGradeSubject).toHaveBeenCalledWith('primary', 'grade1', 'chinese');
      expect(mockClient.getVolumesByStageGradeSubjectVersion).toHaveBeenCalledWith('primary', 'grade1', 'chinese', 'rjb');
      
      // 验证返回的数据结构
      expect(result.stages).toEqual(mockStages);
      expect(result.grades).toEqual(mockGrades);
      expect(result.subjects).toEqual(mockSubjects);
      expect(result.versions).toEqual(mockVersions);
      expect(result.volumes).toEqual(mockVolumes);
    });

    it('应该在中间级别停止级联', async () => {
      const parentFilter: Partial<CourseFilters> = {
        stage: 'primary',
        grade: 'grade1'
      };
      
      const result = await filterService.getFilterOptions(parentFilter);
      
      // 只应该调用到学科级别
      expect(mockClient.getStages).toHaveBeenCalledTimes(1);
      expect(mockClient.getGradesByStage).toHaveBeenCalledWith('primary');
      expect(mockClient.getSubjectsByStageAndGrade).toHaveBeenCalledWith('primary', 'grade1');
      expect(mockClient.getVersionsByStageGradeSubject).not.toHaveBeenCalled();
      expect(mockClient.getVolumesByStageGradeSubjectVersion).not.toHaveBeenCalled();
      
      expect(result.versions).toEqual([]);
      expect(result.volumes).toEqual([]);
    });
  });
});