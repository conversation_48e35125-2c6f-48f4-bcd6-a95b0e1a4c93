import { EventEmitter } from 'events';
import * as dns from 'dns';
import { promisify } from 'util';

const dnsLookup = promisify(dns.lookup);

export interface NetworkStatus {
  isOnline: boolean;
  connectionType: 'wifi' | 'ethernet' | 'cellular' | 'unknown';
  speed: NetworkSpeed;
  latency: number;
  stability: number; // 0-1, 1表示最稳定
}

export interface NetworkSpeed {
  download: number; // Mbps
  upload: number; // Mbps
  ping: number; // ms
}

export interface DownloadStrategy {
  maxConcurrentDownloads: number;
  chunkSize: number;
  retryDelay: number;
  timeoutMs: number;
  useCompression: boolean;
}

export interface NetworkQualityMetrics {
  packetLoss: number;
  jitter: number;
  throughput: number;
  reliability: number;
}

export class NetworkMonitor extends EventEmitter {
  private monitorInterval: NodeJS.Timeout | null = null;
  private speedTestInterval: NodeJS.Timeout | null = null;
  private currentStatus: NetworkStatus;
  private speedHistory: NetworkSpeed[] = [];
  private readonly maxHistorySize = 50;
  private testUrls = [
    'https://www.baidu.com',
    'https://www.qq.com',
    'https://basic.smartedu.cn'
  ];

  constructor(private checkIntervalMs: number = 30000) {
    super();
    this.currentStatus = {
      isOnline: true,
      connectionType: 'unknown',
      speed: { download: 0, upload: 0, ping: 0 },
      latency: 0,
      stability: 1
    };
  }

  /**
   * 开始监控网络状态
   */
  async startMonitoring(): Promise<void> {
    if (this.monitorInterval) {
      this.stopMonitoring();
    }

    // 初始检查
    await this.checkNetworkStatus();

    this.monitorInterval = setInterval(async () => {
      try {
        await this.checkNetworkStatus();
      } catch (error) {
        this.emit('error', error);
      }
    }, this.checkIntervalMs);

    // 定期进行速度测试
    this.speedTestInterval = setInterval(async () => {
      try {
        await this.performSpeedTest();
      } catch (error) {
        this.emit('speedTestError', error);
      }
    }, this.checkIntervalMs * 2); // 速度测试频率较低
  }

  /**
   * 停止监控网络状态
   */
  stopMonitoring(): void {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
    if (this.speedTestInterval) {
      clearInterval(this.speedTestInterval);
      this.speedTestInterval = null;
    }
  }

  /**
   * 检查网络连接状态
   */
  async checkNetworkStatus(): Promise<NetworkStatus> {
    const isOnline = await this.checkConnectivity();
    const latency = isOnline ? await this.measureLatency() : 0;
    const stability = this.calculateStability();

    this.currentStatus = {
      ...this.currentStatus,
      isOnline,
      latency,
      stability
    };

    this.emit('networkStatusUpdate', this.currentStatus);
    return this.currentStatus;
  }

  /**
   * 检查网络连通性
   */
  private async checkConnectivity(): Promise<boolean> {
    try {
      // 尝试DNS解析多个域名
      const promises = this.testUrls.map(async (url) => {
        const hostname = new URL(url).hostname;
        await dnsLookup(hostname);
        return true;
      });

      const results = await Promise.allSettled(promises);
      const successCount = results.filter(result => result.status === 'fulfilled').length;
      
      return successCount > 0;
    } catch {
      return false;
    }
  }

  /**
   * 测量网络延迟
   */
  private async measureLatency(): Promise<number> {
    const latencies: number[] = [];

    for (const url of this.testUrls.slice(0, 2)) { // 只测试前两个URL
      try {
        const start = Date.now();
        const hostname = new URL(url).hostname;
        await dnsLookup(hostname);
        const latency = Date.now() - start;
        latencies.push(latency);
      } catch {
        // 忽略单个测试失败
      }
    }

    return latencies.length > 0 
      ? latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length 
      : 0;
  }

  /**
   * 执行网络速度测试
   */
  private async performSpeedTest(): Promise<NetworkSpeed> {
    try {
      const speed = await this.measureDownloadSpeed();
      
      this.speedHistory.push(speed);
      if (this.speedHistory.length > this.maxHistorySize) {
        this.speedHistory.shift();
      }

      this.currentStatus.speed = speed;
      this.emit('speedTestComplete', speed);
      
      return speed;
    } catch (error) {
      this.emit('speedTestError', error);
      return { download: 0, upload: 0, ping: 0 };
    }
  }

  /**
   * 测量下载速度
   */
  private async measureDownloadSpeed(): Promise<NetworkSpeed> {
    const testUrl = 'https://basic.smartedu.cn'; // 使用目标网站进行测试
    const testSize = 1024 * 100; // 100KB测试数据
    
    try {
      const start = Date.now();
      const response = await fetch(testUrl, {
        method: 'HEAD',
        cache: 'no-cache'
      });
      const ping = Date.now() - start;

      // 简化的速度估算，实际应用中可能需要更复杂的测试
      const estimatedSpeed = response.ok ? 10 : 1; // Mbps

      return {
        download: estimatedSpeed,
        upload: estimatedSpeed * 0.1, // 假设上传速度为下载速度的10%
        ping
      };
    } catch {
      return { download: 0, upload: 0, ping: 999 };
    }
  }

  /**
   * 计算网络稳定性
   */
  private calculateStability(): number {
    if (this.speedHistory.length < 5) {
      return 1; // 数据不足，假设稳定
    }

    const recentSpeeds = this.speedHistory.slice(-10);
    const avgSpeed = recentSpeeds.reduce((sum, speed) => sum + speed.download, 0) / recentSpeeds.length;
    
    if (avgSpeed === 0) {
      return 0;
    }

    const variance = recentSpeeds.reduce((sum, speed) => {
      const diff = speed.download - avgSpeed;
      return sum + diff * diff;
    }, 0) / recentSpeeds.length;

    const coefficient = Math.sqrt(variance) / avgSpeed;
    return Math.max(0, 1 - coefficient); // 变异系数越小，稳定性越高
  }

  /**
   * 根据网络状态获取下载策略
   */
  getDownloadStrategy(): DownloadStrategy {
    const { speed, latency, stability, isOnline } = this.currentStatus;

    if (!isOnline) {
      return {
        maxConcurrentDownloads: 0,
        chunkSize: 0,
        retryDelay: 5000,
        timeoutMs: 30000,
        useCompression: false
      };
    }

    // 根据网络质量调整策略
    let maxConcurrentDownloads = 3;
    let chunkSize = 1024 * 1024; // 1MB
    let retryDelay = 1000;
    let timeoutMs = 30000;

    // 高速网络
    if (speed.download > 50 && latency < 50 && stability > 0.8) {
      maxConcurrentDownloads = 8;
      chunkSize = 5 * 1024 * 1024; // 5MB
      retryDelay = 500;
      timeoutMs = 60000;
    }
    // 中速网络
    else if (speed.download > 10 && latency < 200 && stability > 0.6) {
      maxConcurrentDownloads = 5;
      chunkSize = 2 * 1024 * 1024; // 2MB
      retryDelay = 1000;
      timeoutMs = 45000;
    }
    // 低速或不稳定网络
    else if (speed.download < 5 || latency > 500 || stability < 0.4) {
      maxConcurrentDownloads = 1;
      chunkSize = 512 * 1024; // 512KB
      retryDelay = 3000;
      timeoutMs = 60000;
    }

    return {
      maxConcurrentDownloads,
      chunkSize,
      retryDelay,
      timeoutMs,
      useCompression: speed.download < 10 // 低速网络使用压缩
    };
  }

  /**
   * 获取网络质量评分
   */
  getNetworkQuality(): 'excellent' | 'good' | 'fair' | 'poor' | 'offline' {
    if (!this.currentStatus.isOnline) {
      return 'offline';
    }

    const { speed, latency, stability } = this.currentStatus;
    const score = (speed.download / 100) * 0.4 + 
                  (1 - Math.min(latency / 1000, 1)) * 0.3 + 
                  stability * 0.3;

    if (score > 0.8) return 'excellent';
    if (score > 0.6) return 'good';
    if (score > 0.4) return 'fair';
    return 'poor';
  }

  /**
   * 获取网络建议
   */
  getNetworkSuggestions(): string[] {
    const suggestions: string[] = [];
    const { speed, latency, stability, isOnline } = this.currentStatus;

    if (!isOnline) {
      suggestions.push('网络连接已断开，请检查网络设置');
      return suggestions;
    }

    if (speed.download < 5) {
      suggestions.push('网络速度较慢，建议减少并发下载数量');
    }

    if (latency > 500) {
      suggestions.push('网络延迟较高，可能影响下载稳定性');
    }

    if (stability < 0.5) {
      suggestions.push('网络不稳定，建议启用断点续传功能');
    }

    if (suggestions.length === 0) {
      suggestions.push('网络状态良好');
    }

    return suggestions;
  }

  /**
   * 获取当前网络状态
   */
  getCurrentStatus(): NetworkStatus {
    return { ...this.currentStatus };
  }

  /**
   * 获取速度历史记录
   */
  getSpeedHistory(): NetworkSpeed[] {
    return [...this.speedHistory];
  }

  /**
   * 检查是否应该暂停下载
   */
  shouldPauseDownloads(): boolean {
    const quality = this.getNetworkQuality();
    return quality === 'offline' || quality === 'poor';
  }

  /**
   * 获取网络质量指标
   */
  getQualityMetrics(): NetworkQualityMetrics {
    const { speed, latency, stability } = this.currentStatus;
    
    return {
      packetLoss: Math.max(0, 1 - stability), // 简化的丢包率估算
      jitter: latency * (1 - stability), // 简化的抖动估算
      throughput: speed.download,
      reliability: stability
    };
  }
}