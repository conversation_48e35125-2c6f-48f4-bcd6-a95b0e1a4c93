import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  InputNumber,
  Switch,
  Select,
  Button,
  Tabs,
  Card,
  Space,
  Divider,
  Alert,
  Typography,
  Row,
  Col,
  Slider,
  Radio,
  message
} from 'antd';
import {
  SettingOutlined,
  FolderOpenOutlined,
  DownloadOutlined,
  GlobalOutlined,
  SecurityScanOutlined,
  ExperimentOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { AppConfig, FileOrganizationConfig, ProxySettings } from '../../shared/types';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

interface SettingsPanelProps {
  visible: boolean;
  onClose: () => void;
  config: AppConfig;
  onConfigChange: (config: AppConfig) => void;
}

/**
 * 设置面板组件
 */
export const SettingsPanel: React.FC<SettingsPanelProps> = ({
  visible,
  onClose,
  config,
  onConfigChange
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  const [tempConfig, setTempConfig] = useState<AppConfig>(config);

  useEffect(() => {
    if (visible) {
      setTempConfig(config);
      form.setFieldsValue(config);
    }
  }, [visible, config, form]);

  /**
   * 处理表单值变更
   */
  const handleFormChange = (changedValues: any, allValues: any) => {
    setTempConfig({ ...tempConfig, ...allValues });
  };

  /**
   * 保存设置
   */
  const handleSave = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      
      // 构建完整的配置对象
      const newConfig: AppConfig = {
        ...tempConfig,
        ...values,
        fileOrganization: {
          ...tempConfig.fileOrganization,
          ...values.fileOrganization
        },
        proxySettings: values.proxyEnabled ? {
          ...tempConfig.proxySettings,
          ...values.proxySettings,
          enabled: values.proxyEnabled
        } : undefined
      };

      onConfigChange(newConfig);
      message.success('设置已保存');
      onClose();
    } catch (error) {
      console.error('保存设置失败:', error);
      message.error('保存设置失败，请检查输入');
    } finally {
      setLoading(false);
    }
  };

  /**
   * 重置设置
   */
  const handleReset = () => {
    Modal.confirm({
      title: '重置设置',
      content: '确定要重置所有设置到默认值吗？此操作不可撤销。',
      onOk: () => {
        const defaultConfig: AppConfig = {
          downloadPath: './downloads',
          maxConcurrentDownloads: 3,
          requestTimeout: 30000,
          retryAttempts: 3,
          userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          fileOrganization: {
            basePath: './downloads',
            namingPattern: '{subject}/{grade}/{volume}/{title}',
            createSubfolders: true,
            groupBySubject: true,
            groupByGrade: true
          }
        };
        
        setTempConfig(defaultConfig);
        form.setFieldsValue(defaultConfig);
        message.success('设置已重置为默认值');
      }
    });
  };

  /**
   * 选择下载目录
   */
  const handleSelectDownloadPath = async () => {
    try {
      const path = await window.electronAPI.selectDownloadPath();
      
      if (path) {
        form.setFieldValue('downloadPath', path);
        setTempConfig({ ...tempConfig, downloadPath: path });
      }
    } catch (error) {
      console.error('选择目录失败:', error);
      message.error('选择目录失败');
    }
  };

  /**
   * 渲染通用设置标签页
   */
  const renderGeneralTab = () => (
    <div>
      <Title level={5}>下载设置</Title>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            label="下载目录"
            name="downloadPath"
            rules={[{ required: true, message: '请选择下载目录' }]}
          >
            <Input
              placeholder="选择下载目录"
              addonAfter={
                <Button
                  type="text"
                  icon={<FolderOpenOutlined />}
                  onClick={handleSelectDownloadPath}
                >
                  浏览
                </Button>
              }
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="最大并发下载数"
            name="maxConcurrentDownloads"
            rules={[{ required: true, message: '请输入最大并发下载数' }]}
          >
            <Slider
              min={1}
              max={10}
              marks={{
                1: '1',
                3: '3',
                5: '5',
                10: '10'
              }}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="重试次数"
            name="retryAttempts"
            rules={[{ required: true, message: '请输入重试次数' }]}
          >
            <InputNumber min={0} max={10} style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            label="请求超时时间 (秒)"
            name="requestTimeout"
            rules={[{ required: true, message: '请输入请求超时时间' }]}
          >
            <InputNumber
              min={5}
              max={300}
              style={{ width: '100%' }}
              formatter={value => `${value}秒`}
              parser={value => parseInt(value!.replace('秒', ''), 10) as 5 | 300}
            />
          </Form.Item>
        </Col>
      </Row>

      <Divider />

      <Title level={5}>用户代理</Title>
      <Form.Item
        label="User-Agent"
        name="userAgent"
        rules={[{ required: true, message: '请输入User-Agent' }]}
      >
        <Input.TextArea
          rows={3}
          placeholder="浏览器用户代理字符串"
        />
      </Form.Item>

      <Alert
        message="提示"
        description="修改User-Agent可能影响下载成功率，建议使用默认值或常见浏览器的User-Agent。"
        type="info"
        showIcon
        style={{ marginTop: 16 }}
      />
    </div>
  );

  /**
   * 渲染文件组织设置标签页
   */
  const renderFileOrganizationTab = () => (
    <div>
      <Title level={5}>文件组织规则</Title>
      
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="按学科分组"
            name={['fileOrganization', 'groupBySubject']}
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="按年级分组"
            name={['fileOrganization', 'groupByGrade']}
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item
        label="创建子文件夹"
        name={['fileOrganization', 'createSubfolders']}
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item
        label="文件命名模式"
        name={['fileOrganization', 'namingPattern']}
        rules={[{ required: true, message: '请输入文件命名模式' }]}
      >
        <Select placeholder="选择命名模式">
          <Option value="{subject}/{grade}/{volume}/{title}">
            学科/年级/册次/标题
          </Option>
          <Option value="{grade}/{subject}/{volume}/{title}">
            年级/学科/册次/标题
          </Option>
          <Option value="{volume}/{subject}/{grade}/{title}">
            册次/学科/年级/标题
          </Option>
          <Option value="{title}">
            仅标题
          </Option>
        </Select>
      </Form.Item>

      <Alert
        message="命名模式说明"
        description={
          <div>
            <Paragraph>
              可用的变量：
            </Paragraph>
            <ul>
              <li><code>{'{subject}'}</code> - 学科名称</li>
              <li><code>{'{grade}'}</code> - 年级</li>
              <li><code>{'{volume}'}</code> - 册次</li>
              <li><code>{'{title}'}</code> - 资源标题</li>
              <li><code>{'{chapter}'}</code> - 章节（如果有）</li>
              <li><code>{'{lesson}'}</code> - 课时（如果有）</li>
            </ul>
          </div>
        }
        type="info"
        showIcon
      />

      <Divider />

      <Title level={5}>预览</Title>
      <Card size="small" style={{ backgroundColor: '#fafafa' }}>
        <Text type="secondary">
          示例路径: downloads/数学/三年级/上册/第一章 数与代数.pdf
        </Text>
      </Card>
    </div>
  );

  /**
   * 渲染网络设置标签页
   */
  const renderNetworkTab = () => (
    <div>
      <Title level={5}>代理设置</Title>
      
      <Form.Item
        label="启用代理"
        name="proxyEnabled"
        valuePropName="checked"
      >
        <Switch />
      </Form.Item>

      <Form.Item
        noStyle
        shouldUpdate={(prevValues, currentValues) =>
          prevValues.proxyEnabled !== currentValues.proxyEnabled
        }
      >
        {({ getFieldValue }) => {
          const proxyEnabled = getFieldValue('proxyEnabled');
          
          return proxyEnabled ? (
            <div>
              <Row gutter={16}>
                <Col span={16}>
                  <Form.Item
                    label="代理服务器"
                    name={['proxySettings', 'host']}
                    rules={[{ required: true, message: '请输入代理服务器地址' }]}
                  >
                    <Input placeholder="例如: 127.0.0.1" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    label="端口"
                    name={['proxySettings', 'port']}
                    rules={[{ required: true, message: '请输入端口号' }]}
                  >
                    <InputNumber
                      min={1}
                      max={65535}
                      style={{ width: '100%' }}
                      placeholder="8080"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="用户名"
                    name={['proxySettings', 'username']}
                  >
                    <Input placeholder="可选" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="密码"
                    name={['proxySettings', 'password']}
                  >
                    <Input.Password placeholder="可选" />
                  </Form.Item>
                </Col>
              </Row>
            </div>
          ) : null;
        }}
      </Form.Item>

      <Alert
        message="代理设置说明"
        description="启用代理后，所有网络请求将通过指定的代理服务器。如果遇到网络问题，可以尝试禁用代理。"
        type="info"
        showIcon
      />
    </div>
  );

  /**
   * 渲染高级设置标签页
   */
  const renderAdvancedTab = () => (
    <div>
      <Title level={5}>实验性功能</Title>
      
      <Alert
        message="警告"
        description="以下功能为实验性功能，可能不稳定或影响应用性能。请谨慎使用。"
        type="warning"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <Space direction="vertical" style={{ width: '100%' }}>
        <Card size="small">
          <Row justify="space-between" align="middle">
            <Col>
              <Text strong>智能重试机制</Text>
              <br />
              <Text type="secondary">根据网络状况自动调整重试策略</Text>
            </Col>
            <Col>
              <Switch defaultChecked={false} />
            </Col>
          </Row>
        </Card>

        <Card size="small">
          <Row justify="space-between" align="middle">
            <Col>
              <Text strong>并发优化</Text>
              <br />
              <Text type="secondary">动态调整并发下载数以优化性能</Text>
            </Col>
            <Col>
              <Switch defaultChecked={false} />
            </Col>
          </Row>
        </Card>

        <Card size="small">
          <Row justify="space-between" align="middle">
            <Col>
              <Text strong>预加载资源信息</Text>
              <br />
              <Text type="secondary">提前获取资源详情以提升用户体验</Text>
            </Col>
            <Col>
              <Switch defaultChecked={false} />
            </Col>
          </Row>
        </Card>

        <Card size="small">
          <Row justify="space-between" align="middle">
            <Col>
              <Text strong>详细日志记录</Text>
              <br />
              <Text type="secondary">记录详细的操作日志用于问题诊断</Text>
            </Col>
            <Col>
              <Switch defaultChecked={false} />
            </Col>
          </Row>
        </Card>
      </Space>

      <Divider />

      <Title level={5}>性能调优</Title>
      
      <Form.Item label="内存使用限制 (MB)">
        <Slider
          min={512}
          max={4096}
          step={256}
          defaultValue={2048}
          marks={{
            512: '512MB',
            1024: '1GB',
            2048: '2GB',
            4096: '4GB'
          }}
        />
      </Form.Item>

      <Form.Item label="磁盘缓存大小 (MB)">
        <Slider
          min={100}
          max={1000}
          step={100}
          defaultValue={500}
          marks={{
            100: '100MB',
            500: '500MB',
            1000: '1GB'
          }}
        />
      </Form.Item>
    </div>
  );

  return (
    <Modal
      title={
        <Space>
          <SettingOutlined />
          应用设置
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={
        <Space>
          <Button onClick={handleReset} icon={<ReloadOutlined />}>
            重置默认
          </Button>
          <Button onClick={onClose}>
            取消
          </Button>
          <Button
            type="primary"
            onClick={handleSave}
            loading={loading}
            icon={<SaveOutlined />}
          >
            保存设置
          </Button>
        </Space>
      }
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onValuesChange={handleFormChange}
        initialValues={config}
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <span>
                <DownloadOutlined />
                通用设置
              </span>
            }
            key="general"
          >
            {renderGeneralTab()}
          </TabPane>

          <TabPane
            tab={
              <span>
                <FolderOpenOutlined />
                文件组织
              </span>
            }
            key="fileOrganization"
          >
            {renderFileOrganizationTab()}
          </TabPane>

          <TabPane
            tab={
              <span>
                <GlobalOutlined />
                网络设置
              </span>
            }
            key="network"
          >
            {renderNetworkTab()}
          </TabPane>

          <TabPane
            tab={
              <span>
                <ExperimentOutlined />
                高级设置
              </span>
            }
            key="advanced"
          >
            {renderAdvancedTab()}
          </TabPane>
        </Tabs>
      </Form>
    </Modal>
  );
};

export default SettingsPanel;