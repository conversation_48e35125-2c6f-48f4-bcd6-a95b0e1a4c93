import { AuthManager, AuthState } from '../AuthManager';
import { SmartEduClient } from '../SmartEduClient';
import { 
  AuthResult, 
  UserInfo, 
  CaptchaInfo,
  AuthError,
  ValidationError 
} from '../../types';

// Mock SmartEduClient
jest.mock('../SmartEduClient');
const MockedSmartEduClient = SmartEduClient as jest.MockedClass<typeof SmartEduClient>;

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

// Mock global objects for Node.js environment
Object.defineProperty(global, 'localStorage', {
  value: localStorageMock,
  writable: true
});

describe('AuthManager', () => {
  let authManager: AuthManager;
  let mockClient: jest.Mocked<SmartEduClient>;

  const mockUser: UserInfo = {
    id: '123',
    username: 'testuser',
    displayName: '测试用户',
    permissions: ['read', 'download']
  };

  const mockAuthResult: AuthResult = {
    success: true,
    token: 'mock-token',
    user: mockUser
  };

  const mockCaptchaInfo: CaptchaInfo = {
    image: 'base64-captcha-image',
    token: 'captcha-token'
  };

  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    
    mockClient = new MockedSmartEduClient() as jest.Mocked<SmartEduClient>;
    authManager = new AuthManager(mockClient);
  });

  afterEach(() => {
    authManager.destroy();
  });

  describe('初始状态', () => {
    it('应该初始化为访客状态', () => {
      expect(authManager.getCurrentState()).toBe(AuthState.GUEST);
      expect(authManager.isLoggedIn()).toBe(false);
      expect(authManager.isGuestMode()).toBe(true);
      expect(authManager.getCurrentUser()).toBeNull();
    });
  });

  describe('登录功能', () => {
    it('应该成功登录并设置用户信息', async () => {
      mockClient.login.mockResolvedValue(mockAuthResult);

      const stateChangeSpy = jest.fn();
      const loginSuccessSpy = jest.fn();
      authManager.on('stateChange', stateChangeSpy);
      authManager.on('loginSuccess', loginSuccessSpy);

      const result = await authManager.loginWithCredentials('testuser', 'password123');

      expect(result.success).toBe(true);
      expect(authManager.getCurrentState()).toBe(AuthState.LOGGED_IN);
      expect(authManager.isLoggedIn()).toBe(true);
      expect(authManager.getCurrentUser()).toEqual(mockUser);
      expect(stateChangeSpy).toHaveBeenCalledWith(AuthState.LOGGED_IN, mockUser);
      expect(loginSuccessSpy).toHaveBeenCalledWith(mockUser);
      expect(localStorageMock.setItem).toHaveBeenCalled();
    });

    it('应该处理登录失败', async () => {
      const failedResult: AuthResult = {
        success: false,
        error: '用户名或密码错误'
      };
      mockClient.login.mockResolvedValue(failedResult);

      const loginErrorSpy = jest.fn();
      authManager.on('loginError', loginErrorSpy);

      await expect(
        authManager.loginWithCredentials('testuser', 'wrongpassword')
      ).rejects.toThrow('用户名或密码错误');

      expect(authManager.getCurrentState()).toBe(AuthState.ERROR);
      expect(authManager.isLoggedIn()).toBe(false);
      expect(loginErrorSpy).toHaveBeenCalledWith('用户名或密码错误');
    });

    it('应该处理需要验证码的情况', async () => {
      const captchaResult: AuthResult = {
        success: false,
        requiresCaptcha: true,
        captchaImage: mockCaptchaInfo.image,
        error: '需要验证码'
      };
      mockClient.login.mockResolvedValue(captchaResult);

      const captchaRequiredSpy = jest.fn();
      authManager.on('captchaRequired', captchaRequiredSpy);

      const result = await authManager.loginWithCredentials('testuser', 'password123');

      expect(result.success).toBe(false);
      expect(result.requiresCaptcha).toBe(true);
      expect(captchaRequiredSpy).toHaveBeenCalledWith({
        image: mockCaptchaInfo.image,
        token: mockCaptchaInfo.image
      });
    });

    it('应该验证登录凭据', async () => {
      await expect(
        authManager.loginWithCredentials('', 'password123')
      ).rejects.toThrow(ValidationError);

      await expect(
        authManager.loginWithCredentials('testuser', '')
      ).rejects.toThrow(ValidationError);
    });
  });

  describe('访客模式', () => {
    it('应该成功切换到访客模式', async () => {
      await authManager.loginAsGuest();

      expect(authManager.getCurrentState()).toBe(AuthState.GUEST);
      expect(authManager.isGuestMode()).toBe(true);
      expect(authManager.getCurrentUser()).toBeNull();
      expect(localStorageMock.removeItem).toHaveBeenCalled();
    });
  });

  describe('登出功能', () => {
    it('应该成功登出并清除会话', async () => {
      // 先登录
      mockClient.login.mockResolvedValue(mockAuthResult);
      await authManager.loginWithCredentials('testuser', 'password123');

      // 然后登出
      mockClient.logout.mockResolvedValue();
      const logoutSpy = jest.fn();
      authManager.on('logout', logoutSpy);

      await authManager.logout();

      expect(authManager.getCurrentState()).toBe(AuthState.LOGGED_OUT);
      expect(authManager.isLoggedIn()).toBe(false);
      expect(authManager.getCurrentUser()).toBeNull();
      expect(logoutSpy).toHaveBeenCalled();
      expect(localStorageMock.removeItem).toHaveBeenCalled();
    });

    it('即使服务器登出失败也应该清除本地会话', async () => {
      // 先登录
      mockClient.login.mockResolvedValue(mockAuthResult);
      await authManager.loginWithCredentials('testuser', 'password123');

      // 模拟服务器登出失败
      mockClient.logout.mockRejectedValue(new Error('网络错误'));

      await authManager.logout();

      expect(authManager.getCurrentState()).toBe(AuthState.LOGGED_OUT);
      expect(authManager.getCurrentUser()).toBeNull();
    });
  });

  describe('会话管理', () => {
    it('应该从本地存储恢复有效会话', () => {
      const sessionData = {
        token: 'stored-token',
        user: mockUser,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      };
      localStorageMock.getItem.mockReturnValue(JSON.stringify(sessionData));

      const newAuthManager = new AuthManager(mockClient);

      expect(newAuthManager.getCurrentState()).toBe(AuthState.LOGGED_IN);
      expect(newAuthManager.getCurrentUser()).toEqual(mockUser);

      newAuthManager.destroy();
    });

    it('应该清除过期的会话', () => {
      const expiredSessionData = {
        token: 'expired-token',
        user: mockUser,
        expiresAt: new Date(Date.now() - 1000).toISOString() // 已过期
      };
      localStorageMock.getItem.mockReturnValue(JSON.stringify(expiredSessionData));

      const newAuthManager = new AuthManager(mockClient);

      expect(newAuthManager.getCurrentState()).toBe(AuthState.GUEST);
      expect(newAuthManager.getCurrentUser()).toBeNull();
      expect(localStorageMock.removeItem).toHaveBeenCalled();

      newAuthManager.destroy();
    });

    it('应该验证会话有效性', async () => {
      // 先登录
      mockClient.login.mockResolvedValue(mockAuthResult);
      await authManager.loginWithCredentials('testuser', 'password123');

      // 模拟服务器验证成功
      mockClient.checkAuthStatus.mockResolvedValue(true);

      const isValid = await authManager.validateSession();

      expect(isValid).toBe(true);
      expect(mockClient.checkAuthStatus).toHaveBeenCalled();
    });

    it('应该处理会话过期', async () => {
      // 先登录
      mockClient.login.mockResolvedValue(mockAuthResult);
      await authManager.loginWithCredentials('testuser', 'password123');

      // 模拟服务器验证失败
      mockClient.checkAuthStatus.mockResolvedValue(false);
      const sessionExpiredSpy = jest.fn();
      authManager.on('sessionExpired', sessionExpiredSpy);

      const isValid = await authManager.validateSession();

      expect(isValid).toBe(false);
      expect(authManager.getCurrentState()).toBe(AuthState.SESSION_EXPIRED);
      expect(sessionExpiredSpy).toHaveBeenCalled();
    });
  });

  describe('验证码功能', () => {
    it('应该获取验证码', async () => {
      mockClient.getCaptcha.mockResolvedValue(mockCaptchaInfo);

      const captchaInfo = await authManager.getCaptcha();

      expect(captchaInfo).toEqual(mockCaptchaInfo);
      expect(mockClient.getCaptcha).toHaveBeenCalled();
    });
  });

  describe('认证头信息', () => {
    it('应该返回带有token的认证头', async () => {
      // 先登录
      mockClient.login.mockResolvedValue(mockAuthResult);
      await authManager.loginWithCredentials('testuser', 'password123');

      const headers = await authManager.getAuthHeaders();

      expect(headers).toEqual({
        'Authorization': 'Bearer mock-token'
      });
    });

    it('未登录时应该返回空的认证头', async () => {
      const headers = await authManager.getAuthHeaders();

      expect(headers).toEqual({});
    });
  });

  describe('调试信息', () => {
    it('应该返回正确的调试信息', async () => {
      const debugInfo = authManager.getDebugInfo();

      expect(debugInfo).toEqual({
        state: AuthState.GUEST,
        hasSession: false,
        sessionExpiry: null,
        hasCaptcha: false,
        user: null
      });
    });
  });

  describe('错误处理', () => {
    it('应该处理网络错误', async () => {
      mockClient.login.mockRejectedValue(new Error('网络连接失败'));

      const loginErrorSpy = jest.fn();
      authManager.on('loginError', loginErrorSpy);

      await expect(
        authManager.loginWithCredentials('testuser', 'password123')
      ).rejects.toThrow('网络连接失败');

      expect(authManager.getCurrentState()).toBe(AuthState.ERROR);
      expect(loginErrorSpy).toHaveBeenCalledWith('网络连接失败');
    });
  });

  describe('资源清理', () => {
    it('应该正确清理资源', () => {
      const removeAllListenersSpy = jest.spyOn(authManager, 'removeAllListeners');

      authManager.destroy();

      expect(removeAllListenersSpy).toHaveBeenCalled();
    });
  });
});