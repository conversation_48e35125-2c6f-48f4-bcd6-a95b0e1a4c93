import { M3U8Parser } from '../M3U8Parser';
import { M3U8Error } from '../../types';
import axios from 'axios';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('M3U8Parser', () => {
  let parser: M3U8Parser;

  beforeEach(() => {
    parser = new M3U8Parser();
    jest.clearAllMocks();
  });

  describe('parsePlaylist', () => {
    it('应该成功解析简单的M3U8播放列表', async () => {
      const mockM3U8Content = `#EXTM3U
#EXT-X-VERSION:3
#EXT-X-TARGETDURATION:10
#EXTINF:10.0,
segment001.ts
#EXTINF:10.0,
segment002.ts
#EXTINF:8.0,
segment003.ts
#EXT-X-ENDLIST`;

      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        data: mockM3U8Content
      });

      const result = await parser.parsePlaylist('https://example.com/playlist.m3u8');

      expect(result).toEqual({
        baseUrl: 'https://example.com/',
        segments: [
          { url: 'https://example.com/segment001.ts', duration: 10.0, sequence: 0 },
          { url: 'https://example.com/segment002.ts', duration: 10.0, sequence: 1 },
          { url: 'https://example.com/segment003.ts', duration: 8.0, sequence: 2 }
        ],
        totalDuration: 28.0,
        isEncrypted: false,
        keyUrl: undefined
      });
    });

    it('应该处理主播放列表并选择最高质量', async () => {
      const masterPlaylist = `#EXTM3U
#EXT-X-VERSION:3
#EXT-X-STREAM-INF:BANDWIDTH=800000,RESOLUTION=640x360
low.m3u8
#EXT-X-STREAM-INF:BANDWIDTH=1400000,RESOLUTION=1280x720
high.m3u8`;

      const highQualityPlaylist = `#EXTM3U
#EXT-X-VERSION:3
#EXT-X-TARGETDURATION:10
#EXTINF:10.0,
hq_segment001.ts
#EXTINF:10.0,
hq_segment002.ts
#EXT-X-ENDLIST`;

      mockedAxios.get
        .mockResolvedValueOnce({
          status: 200,
          data: masterPlaylist
        })
        .mockResolvedValueOnce({
          status: 200,
          data: highQualityPlaylist
        });

      const result = await parser.parsePlaylist('https://example.com/master.m3u8');

      expect(mockedAxios.get).toHaveBeenCalledTimes(2);
      expect(mockedAxios.get).toHaveBeenNthCalledWith(2, 'https://example.com/high.m3u8', expect.any(Object));
      expect(result.segments).toHaveLength(2);
      expect(result.segments[0].url).toBe('https://example.com/hq_segment001.ts');
    });

    it('应该处理加密的播放列表', async () => {
      const encryptedPlaylist = `#EXTM3U
#EXT-X-VERSION:3
#EXT-X-TARGETDURATION:10
#EXT-X-KEY:METHOD=AES-128,URI="encryption.key"
#EXTINF:10.0,
segment001.ts
#EXTINF:10.0,
segment002.ts
#EXT-X-ENDLIST`;

      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        data: encryptedPlaylist
      });

      const result = await parser.parsePlaylist('https://example.com/encrypted.m3u8');

      expect(result.isEncrypted).toBe(true);
      expect(result.keyUrl).toBe('https://example.com/encryption.key');
    });

    it('应该处理相对URL', async () => {
      const playlistWithRelativeUrls = `#EXTM3U
#EXT-X-VERSION:3
#EXT-X-TARGETDURATION:10
#EXTINF:10.0,
../segments/segment001.ts
#EXTINF:10.0,
./segment002.ts
#EXTINF:10.0,
segment003.ts
#EXT-X-ENDLIST`;

      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        data: playlistWithRelativeUrls
      });

      const result = await parser.parsePlaylist('https://example.com/videos/playlist.m3u8');

      expect(result.segments[0].url).toBe('https://example.com/segments/segment001.ts');
      expect(result.segments[1].url).toBe('https://example.com/videos/segment002.ts');
      expect(result.segments[2].url).toBe('https://example.com/videos/segment003.ts');
    });

    it('应该在网络错误时重试', async () => {
      mockedAxios.get
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({
          status: 200,
          data: `#EXTM3U
#EXT-X-VERSION:3
#EXT-X-TARGETDURATION:10
#EXTINF:10.0,
segment001.ts
#EXT-X-ENDLIST`
        });

      const result = await parser.parsePlaylist('https://example.com/playlist.m3u8');

      expect(mockedAxios.get).toHaveBeenCalledTimes(3);
      expect(result.segments).toHaveLength(1);
    }, 10000); // 增加超时时间

    it('应该在达到最大重试次数后抛出错误', async () => {
      mockedAxios.get.mockRejectedValue(new Error('Network error'));

      await expect(parser.parsePlaylist('https://example.com/playlist.m3u8'))
        .rejects.toThrow('下载播放列表失败，已重试3次');

      expect(mockedAxios.get).toHaveBeenCalledTimes(3);
    }, 10000); // 增加超时时间

    it('应该在播放列表为空时抛出错误', async () => {
      const emptyPlaylist = `#EXTM3U
#EXT-X-VERSION:3
#EXT-X-TARGETDURATION:10
#EXT-X-ENDLIST`;

      mockedAxios.get.mockResolvedValueOnce({
        status: 200,
        data: emptyPlaylist
      });

      await expect(parser.parsePlaylist('https://example.com/empty.m3u8'))
        .rejects.toThrow('播放列表中没有找到视频片段');
    });

    it('应该在HTTP状态码非200时抛出错误', async () => {
      mockedAxios.get.mockResolvedValueOnce({
        status: 404,
        statusText: 'Not Found',
        data: ''
      });

      await expect(parser.parsePlaylist('https://example.com/notfound.m3u8'))
        .rejects.toThrow('解析M3U8播放列表失败');
    });
  });

  describe('静态方法', () => {
    describe('isValidM3U8Content', () => {
      it('应该识别有效的M3U8内容', () => {
        const validContent = '#EXTM3U\n#EXT-X-VERSION:3\n';
        expect(M3U8Parser.isValidM3U8Content(validContent)).toBe(true);
      });

      it('应该拒绝无效的M3U8内容', () => {
        const invalidContent = 'This is not M3U8 content';
        expect(M3U8Parser.isValidM3U8Content(invalidContent)).toBe(false);
      });

      it('应该处理带有前导空白的内容', () => {
        const contentWithWhitespace = '  \n\t#EXTM3U\n';
        expect(M3U8Parser.isValidM3U8Content(contentWithWhitespace)).toBe(true);
      });
    });

    describe('isM3U8Url', () => {
      it('应该识别M3U8 URL', () => {
        expect(M3U8Parser.isM3U8Url('https://example.com/playlist.m3u8')).toBe(true);
        expect(M3U8Parser.isM3U8Url('https://example.com/playlist.m3u')).toBe(true);
      });

      it('应该拒绝非M3U8 URL', () => {
        expect(M3U8Parser.isM3U8Url('https://example.com/video.mp4')).toBe(false);
        expect(M3U8Parser.isM3U8Url('https://example.com/playlist.txt')).toBe(false);
      });

      it('应该处理带查询参数的URL', () => {
        expect(M3U8Parser.isM3U8Url('https://example.com/playlist.m3u8?token=abc123')).toBe(true);
      });

      it('应该处理无效的URL', () => {
        expect(M3U8Parser.isM3U8Url('not-a-url')).toBe(false);
      });
    });
  });

  describe('配置', () => {
    it('应该使用自定义配置', () => {
      const customParser = new M3U8Parser({
        timeout: 5000,
        maxRetries: 5,
        retryDelay: 2000,
        userAgent: 'Custom User Agent'
      });

      expect(customParser).toBeInstanceOf(M3U8Parser);
    });
  });
});
