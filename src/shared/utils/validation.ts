import {
  CourseFilters,
  LoginCredentials,
  DownloadTask,
  M3U8Playlist,
  M3U8Segment,
  CourseResource,
  ResourceMetadata,
  UserInfo,
  AppConfig,
  ValidationError
} from '../types/index';

// ============================================================================
// 基础验证函数 (Basic Validation Functions)
// ============================================================================

export function isValidString(value: any, minLength: number = 1): boolean {
  return typeof value === 'string' && value.trim().length >= minLength;
}

export function isValidNumber(value: any, min?: number, max?: number): boolean {
  if (typeof value !== 'number' || isNaN(value)) {
    return false;
  }
  if (min !== undefined && value < min) {
    return false;
  }
  if (max !== undefined && value > max) {
    return false;
  }
  return true;
}

export function isValidUrl(value: any): boolean {
  if (!isValidString(value)) {
    return false;
  }
  try {
    new URL(value);
    return true;
  } catch {
    return false;
  }
}

export function isValidEmail(value: any): boolean {
  if (!isValidString(value)) {
    return false;
  }
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(value);
}

export function isValidDate(value: any): boolean {
  return value instanceof Date && !isNaN(value.getTime());
}

// ============================================================================
// 筛选条件验证 (Filter Validation)
// ============================================================================

export function validateCourseFilters(filters: any): CourseFilters {
  if (!filters || typeof filters !== 'object') {
    throw new ValidationError('筛选条件必须是一个对象');
  }

  const { stage, grade, subject, version, volume } = filters;

  if (!isValidString(stage)) {
    throw new ValidationError('学段不能为空');
  }

  if (!isValidString(grade)) {
    throw new ValidationError('年级不能为空');
  }

  if (!isValidString(subject)) {
    throw new ValidationError('学科不能为空');
  }

  if (!isValidString(version)) {
    throw new ValidationError('版本不能为空');
  }

  if (!isValidString(volume)) {
    throw new ValidationError('册次不能为空');
  }

  return {
    stage: stage.trim(),
    grade: grade.trim(),
    subject: subject.trim(),
    version: version.trim(),
    volume: volume.trim()
  };
}

// ============================================================================
// 用户认证验证 (Authentication Validation)
// ============================================================================

export interface ValidationResult<T> {
  isValid: boolean;
  data?: T;
  errors: string[];
}

export function validateLoginCredentials(credentials: any): ValidationResult<LoginCredentials> {
  const errors: string[] = [];

  if (!credentials || typeof credentials !== 'object') {
    return {
      isValid: false,
      errors: ['登录凭据必须是一个对象']
    };
  }

  const { username, password, captcha, captchaToken } = credentials;

  if (!isValidString(username, 3)) {
    errors.push('用户名长度至少为3个字符');
  }

  if (!isValidString(password, 6)) {
    errors.push('密码长度至少为6个字符');
  }

  if (captcha !== undefined && !isValidString(captcha)) {
    errors.push('验证码不能为空');
  }

  if (captchaToken !== undefined && !isValidString(captchaToken)) {
    errors.push('验证码令牌不能为空');
  }

  if (errors.length > 0) {
    return {
      isValid: false,
      errors
    };
  }

  const validatedCredentials: LoginCredentials = {
    username: username.trim(),
    password: password.trim()
  };

  if (captcha !== undefined) {
    validatedCredentials.captcha = captcha.trim();
  }

  if (captchaToken !== undefined) {
    validatedCredentials.captchaToken = captchaToken.trim();
  }

  return {
    isValid: true,
    data: validatedCredentials,
    errors: []
  };
}

// Keep the original throwing version for backward compatibility
export function validateLoginCredentialsStrict(credentials: any): LoginCredentials {
  if (!credentials || typeof credentials !== 'object') {
    throw new ValidationError('登录凭据必须是一个对象');
  }

  const { username, password, captcha, captchaToken } = credentials;

  if (!isValidString(username, 3)) {
    throw new ValidationError('用户名长度至少为3个字符');
  }

  if (!isValidString(password, 6)) {
    throw new ValidationError('密码长度至少为6个字符');
  }

  const validatedCredentials: LoginCredentials = {
    username: username.trim(),
    password: password.trim()
  };

  if (captcha !== undefined) {
    if (!isValidString(captcha)) {
      throw new ValidationError('验证码不能为空');
    }
    validatedCredentials.captcha = captcha.trim();
  }

  if (captchaToken !== undefined) {
    if (!isValidString(captchaToken)) {
      throw new ValidationError('验证码令牌不能为空');
    }
    validatedCredentials.captchaToken = captchaToken.trim();
  }

  return validatedCredentials;
}

export function validateUserInfo(userInfo: any): UserInfo {
  if (!userInfo || typeof userInfo !== 'object') {
    throw new ValidationError('用户信息必须是一个对象');
  }

  const { id, username, displayName, permissions } = userInfo;

  if (!isValidString(id)) {
    throw new ValidationError('用户ID不能为空');
  }

  if (!isValidString(username)) {
    throw new ValidationError('用户名不能为空');
  }

  if (!isValidString(displayName)) {
    throw new ValidationError('显示名称不能为空');
  }

  if (!Array.isArray(permissions)) {
    throw new ValidationError('权限必须是一个数组');
  }

  return {
    id: id.trim(),
    username: username.trim(),
    displayName: displayName.trim(),
    permissions: permissions.filter(p => isValidString(p)).map(p => p.trim())
  };
}

// ============================================================================
// 资源验证 (Resource Validation)
// ============================================================================

export function validateResourceMetadata(metadata: any): ResourceMetadata {
  if (!metadata || typeof metadata !== 'object') {
    throw new ValidationError('资源元数据必须是一个对象');
  }

  const { stage, grade, subject, version, volume, chapter, lesson, fileSize, duration } = metadata;

  if (!isValidString(stage)) {
    throw new ValidationError('学段不能为空');
  }

  if (!isValidString(grade)) {
    throw new ValidationError('年级不能为空');
  }

  if (!isValidString(subject)) {
    throw new ValidationError('学科不能为空');
  }

  if (!isValidString(version)) {
    throw new ValidationError('版本不能为空');
  }

  if (!isValidString(volume)) {
    throw new ValidationError('册次不能为空');
  }

  const validatedMetadata: ResourceMetadata = {
    stage: stage.trim(),
    grade: grade.trim(),
    subject: subject.trim(),
    version: version.trim(),
    volume: volume.trim()
  };

  if (chapter !== undefined && isValidString(chapter)) {
    validatedMetadata.chapter = chapter.trim();
  }

  if (lesson !== undefined && isValidString(lesson)) {
    validatedMetadata.lesson = lesson.trim();
  }

  if (fileSize !== undefined && isValidNumber(fileSize, 0)) {
    validatedMetadata.fileSize = fileSize;
  }

  if (duration !== undefined && isValidNumber(duration, 0)) {
    validatedMetadata.duration = duration;
  }

  return validatedMetadata;
}

export function validateCourseResource(resource: any): CourseResource {
  if (!resource || typeof resource !== 'object') {
    throw new ValidationError('课程资源必须是一个对象');
  }

  const { id, title, type, url, metadata, requiresAuth, accessLevel } = resource;

  if (!isValidString(id)) {
    throw new ValidationError('资源ID不能为空');
  }

  if (!isValidString(title)) {
    throw new ValidationError('资源标题不能为空');
  }

  if (!['textbook', 'video'].includes(type)) {
    throw new ValidationError('资源类型必须是 textbook 或 video');
  }

  if (!isValidUrl(url)) {
    throw new ValidationError('资源URL格式不正确');
  }

  if (typeof requiresAuth !== 'boolean') {
    throw new ValidationError('requiresAuth 必须是布尔值');
  }

  if (!['public', 'registered', 'premium'].includes(accessLevel)) {
    throw new ValidationError('访问级别必须是 public、registered 或 premium');
  }

  return {
    id: id.trim(),
    title: title.trim(),
    type,
    url: url.trim(),
    metadata: validateResourceMetadata(metadata),
    requiresAuth,
    accessLevel
  };
}

// ============================================================================
// 下载任务验证 (Download Task Validation)
// ============================================================================

export function validateDownloadTask(task: any): DownloadTask {
  if (!task || typeof task !== 'object') {
    throw new ValidationError('下载任务必须是一个对象');
  }

  const {
    id,
    resource,
    status,
    progress,
    speed,
    estimatedTime,
    error,
    requiresAuth,
    createdAt,
    updatedAt,
    outputPath,
    retryCount,
    maxRetries
  } = task;

  if (!isValidString(id)) {
    throw new ValidationError('任务ID不能为空');
  }

  const validStatuses = ['pending', 'downloading', 'paused', 'completed', 'failed', 'cancelled'];
  if (!validStatuses.includes(status)) {
    throw new ValidationError(`任务状态必须是以下之一: ${validStatuses.join(', ')}`);
  }

  if (!isValidNumber(progress, 0, 100)) {
    throw new ValidationError('进度必须是0-100之间的数字');
  }

  if (!isValidNumber(speed, 0)) {
    throw new ValidationError('下载速度必须是非负数');
  }

  if (!isValidNumber(estimatedTime, 0)) {
    throw new ValidationError('预估时间必须是非负数');
  }

  if (typeof requiresAuth !== 'boolean') {
    throw new ValidationError('requiresAuth 必须是布尔值');
  }

  if (!isValidDate(createdAt)) {
    throw new ValidationError('创建时间必须是有效的日期');
  }

  if (!isValidDate(updatedAt)) {
    throw new ValidationError('更新时间必须是有效的日期');
  }

  if (!isValidString(outputPath)) {
    throw new ValidationError('输出路径不能为空');
  }

  if (!isValidNumber(retryCount, 0)) {
    throw new ValidationError('重试次数必须是非负整数');
  }

  if (!isValidNumber(maxRetries, 0)) {
    throw new ValidationError('最大重试次数必须是非负整数');
  }

  const validatedTask: DownloadTask = {
    id: id.trim(),
    resource: validateCourseResource(resource),
    status,
    progress,
    speed,
    estimatedTime,
    requiresAuth,
    createdAt,
    updatedAt,
    outputPath: outputPath.trim(),
    retryCount,
    maxRetries
  };

  if (error !== undefined && isValidString(error)) {
    validatedTask.error = error.trim();
  }

  return validatedTask;
}

// ============================================================================
// M3U8 验证 (M3U8 Validation)
// ============================================================================

export function validateM3U8Segment(segment: any): M3U8Segment {
  if (!segment || typeof segment !== 'object') {
    throw new ValidationError('M3U8片段必须是一个对象');
  }

  const { url, duration, sequence, byteRange } = segment;

  if (!isValidUrl(url)) {
    throw new ValidationError('片段URL格式不正确');
  }

  if (!isValidNumber(duration, 0)) {
    throw new ValidationError('片段时长必须是正数');
  }

  if (!isValidNumber(sequence, 0)) {
    throw new ValidationError('片段序号必须是非负整数');
  }

  const validatedSegment: M3U8Segment = {
    url: url.trim(),
    duration,
    sequence
  };

  if (byteRange !== undefined) {
    if (!byteRange || typeof byteRange !== 'object') {
      throw new ValidationError('字节范围必须是一个对象');
    }

    const { start, length } = byteRange;

    if (!isValidNumber(start, 0)) {
      throw new ValidationError('字节范围起始位置必须是非负整数');
    }

    if (!isValidNumber(length, 1)) {
      throw new ValidationError('字节范围长度必须是正整数');
    }

    validatedSegment.byteRange = { start, length };
  }

  return validatedSegment;
}

export function validateM3U8Playlist(playlist: any): M3U8Playlist {
  if (!playlist || typeof playlist !== 'object') {
    throw new ValidationError('M3U8播放列表必须是一个对象');
  }

  const { baseUrl, segments, totalDuration, isEncrypted, keyUrl } = playlist;

  if (!isValidUrl(baseUrl)) {
    throw new ValidationError('基础URL格式不正确');
  }

  if (!Array.isArray(segments) || segments.length === 0) {
    throw new ValidationError('片段列表不能为空');
  }

  if (!isValidNumber(totalDuration, 0)) {
    throw new ValidationError('总时长必须是正数');
  }

  if (typeof isEncrypted !== 'boolean') {
    throw new ValidationError('isEncrypted 必须是布尔值');
  }

  const validatedPlaylist: M3U8Playlist = {
    baseUrl: baseUrl.trim(),
    segments: segments.map(validateM3U8Segment),
    totalDuration,
    isEncrypted
  };

  if (keyUrl !== undefined) {
    if (!isValidUrl(keyUrl)) {
      throw new ValidationError('密钥URL格式不正确');
    }
    validatedPlaylist.keyUrl = keyUrl.trim();
  }

  return validatedPlaylist;
}

// ============================================================================
// 应用配置验证 (App Configuration Validation)
// ============================================================================

export function validateAppConfig(config: any): AppConfig {
  if (!config || typeof config !== 'object') {
    throw new ValidationError('应用配置必须是一个对象');
  }

  const {
    downloadPath,
    maxConcurrentDownloads,
    requestTimeout,
    retryAttempts,
    userAgent,
    proxySettings,
    fileOrganization
  } = config;

  if (!isValidString(downloadPath)) {
    throw new ValidationError('下载路径不能为空');
  }

  if (!isValidNumber(maxConcurrentDownloads, 1, 10)) {
    throw new ValidationError('最大并发下载数必须在1-10之间');
  }

  if (!isValidNumber(requestTimeout, 1000)) {
    throw new ValidationError('请求超时时间必须大于1000毫秒');
  }

  if (!isValidNumber(retryAttempts, 0, 10)) {
    throw new ValidationError('重试次数必须在0-10之间');
  }

  if (!isValidString(userAgent)) {
    throw new ValidationError('User-Agent不能为空');
  }

  if (!fileOrganization || typeof fileOrganization !== 'object') {
    throw new ValidationError('文件组织配置必须是一个对象');
  }

  const validatedConfig: AppConfig = {
    downloadPath: downloadPath.trim(),
    maxConcurrentDownloads,
    requestTimeout,
    retryAttempts,
    userAgent: userAgent.trim(),
    fileOrganization: {
      basePath: fileOrganization.basePath?.trim() || downloadPath.trim(),
      namingPattern: fileOrganization.namingPattern?.trim() || '{subject}/{grade}/{title}',
      createSubfolders: Boolean(fileOrganization.createSubfolders),
      groupBySubject: Boolean(fileOrganization.groupBySubject),
      groupByGrade: Boolean(fileOrganization.groupByGrade)
    }
  };

  if (proxySettings !== undefined) {
    if (!proxySettings || typeof proxySettings !== 'object') {
      throw new ValidationError('代理设置必须是一个对象');
    }

    const { enabled, host, port, username, password } = proxySettings;

    if (typeof enabled !== 'boolean') {
      throw new ValidationError('代理启用状态必须是布尔值');
    }

    if (enabled) {
      if (!isValidString(host)) {
        throw new ValidationError('代理主机不能为空');
      }

      if (!isValidNumber(port, 1, 65535)) {
        throw new ValidationError('代理端口必须在1-65535之间');
      }

      validatedConfig.proxySettings = {
        enabled,
        host: host.trim(),
        port
      };

      if (username !== undefined && isValidString(username)) {
        validatedConfig.proxySettings.username = username.trim();
      }

      if (password !== undefined && isValidString(password)) {
        validatedConfig.proxySettings.password = password.trim();
      }
    } else {
      validatedConfig.proxySettings = { enabled: false, host: '', port: 0 };
    }
  }

  return validatedConfig;
}

// ============================================================================
// 通用验证辅助函数 (Generic Validation Helpers)
// ============================================================================

export function validateRequired<T>(value: T, fieldName: string): T {
  if (value === null || value === undefined) {
    throw new ValidationError(`${fieldName} 是必需的`);
  }
  return value;
}

export function validateOptional<T>(
  value: T | undefined,
  validator: (value: T) => T
): T | undefined {
  if (value === undefined) {
    return undefined;
  }
  return validator(value);
}

export function validateArray<T>(
  value: any,
  itemValidator: (item: any) => T,
  fieldName: string,
  minLength: number = 0
): T[] {
  if (!Array.isArray(value)) {
    throw new ValidationError(`${fieldName} 必须是一个数组`);
  }

  if (value.length < minLength) {
    throw new ValidationError(`${fieldName} 至少需要 ${minLength} 个元素`);
  }

  return value.map((item, index) => {
    try {
      return itemValidator(item);
    } catch (error) {
      throw new ValidationError(
        `${fieldName}[${index}] 验证失败: ${error instanceof Error ? error.message : '未知错误'}`
      );
    }
  });
}

// ============================================================================
// 文件路径和系统验证 (File Path and System Validation)
// ============================================================================

export function validateFilePath(path: any): string {
  if (!isValidString(path)) {
    throw new ValidationError('文件路径不能为空');
  }

  const trimmedPath = path.trim();
  
  // 检查路径中的非法字符
  const invalidChars = /[<>:"|?*]/;
  if (invalidChars.test(trimmedPath)) {
    throw new ValidationError('文件路径包含非法字符');
  }

  return trimmedPath;
}

export function validateFileSize(size: any, maxSize?: number): number {
  if (!isValidNumber(size, 0)) {
    throw new ValidationError('文件大小必须是非负数');
  }

  if (maxSize !== undefined && size > maxSize) {
    throw new ValidationError(`文件大小不能超过 ${maxSize} 字节`);
  }

  return size;
}

export function validateDiskSpace(available: number, required: number): boolean {
  if (!isValidNumber(available, 0)) {
    throw new ValidationError('可用磁盘空间必须是非负数');
  }

  if (!isValidNumber(required, 0)) {
    throw new ValidationError('所需磁盘空间必须是非负数');
  }

  return available >= required;
}

// ============================================================================
// 网络和请求验证 (Network and Request Validation)
// ============================================================================

export function validateUserAgent(userAgent: any): string {
  if (!isValidString(userAgent, 10)) {
    throw new ValidationError('User-Agent 长度至少为10个字符');
  }

  const trimmedUserAgent = userAgent.trim();
  
  // 基本的 User-Agent 格式检查
  if (!trimmedUserAgent.includes('/') || !trimmedUserAgent.includes('(')) {
    throw new ValidationError('User-Agent 格式不正确');
  }

  return trimmedUserAgent;
}

export function validateTimeout(timeout: any): number {
  if (!isValidNumber(timeout, 1000, 300000)) {
    throw new ValidationError('超时时间必须在1000-300000毫秒之间');
  }

  return timeout;
}

export function validateRetryCount(count: any, maxRetries: number = 10): number {
  if (!isValidNumber(count, 0, maxRetries)) {
    throw new ValidationError(`重试次数必须在0-${maxRetries}之间`);
  }

  return count;
}

// ============================================================================
// 批量操作验证 (Batch Operation Validation)
// ============================================================================

export function validateBatchSize(size: any, maxSize: number = 100): number {
  if (!isValidNumber(size, 1, maxSize)) {
    throw new ValidationError(`批量操作大小必须在1-${maxSize}之间`);
  }

  return size;
}

export function validateConcurrency(concurrency: any): number {
  if (!isValidNumber(concurrency, 1, 10)) {
    throw new ValidationError('并发数必须在1-10之间');
  }

  return concurrency;
}

// ============================================================================
// 数据完整性验证 (Data Integrity Validation)
// ============================================================================

export function validateChecksum(checksum: any): string {
  if (!isValidString(checksum)) {
    throw new ValidationError('校验和不能为空');
  }

  const trimmedChecksum = checksum.trim();
  
  // 检查是否为有效的十六进制字符串
  const hexRegex = /^[a-fA-F0-9]+$/;
  if (!hexRegex.test(trimmedChecksum)) {
    throw new ValidationError('校验和必须是有效的十六进制字符串');
  }

  // 检查常见的校验和长度 (MD5: 32, SHA1: 40, SHA256: 64)
  const validLengths = [32, 40, 64];
  if (!validLengths.includes(trimmedChecksum.length)) {
    throw new ValidationError('校验和长度不正确');
  }

  return trimmedChecksum;
}

export function validateProgress(progress: any): number {
  if (!isValidNumber(progress, 0, 100)) {
    throw new ValidationError('进度必须在0-100之间');
  }

  return Math.round(progress * 100) / 100; // 保留两位小数
}

export function validateSpeed(speed: any): number {
  if (!isValidNumber(speed, 0)) {
    throw new ValidationError('速度必须是非负数');
  }

  return Math.round(speed * 100) / 100; // 保留两位小数
}

// ============================================================================
// 重试机制验证 (Retry Mechanism Validation)
// ============================================================================

export function validateRetryOptions(options: any): import('../types/index').RetryOptions {
  if (!options || typeof options !== 'object') {
    throw new ValidationError('重试选项必须是一个对象');
  }

  const { maxRetries, baseDelay, maxDelay, backoffFactor, retryableErrors } = options;

  if (!isValidNumber(maxRetries, 0, 10)) {
    throw new ValidationError('最大重试次数必须在0-10之间');
  }

  if (!isValidNumber(baseDelay, 100, 60000)) {
    throw new ValidationError('基础延迟必须在100-60000毫秒之间');
  }

  if (!isValidNumber(maxDelay, baseDelay, 300000)) {
    throw new ValidationError('最大延迟必须大于基础延迟且不超过300000毫秒');
  }

  if (!isValidNumber(backoffFactor, 1, 10)) {
    throw new ValidationError('退避因子必须在1-10之间');
  }

  if (!Array.isArray(retryableErrors)) {
    throw new ValidationError('可重试错误类型必须是一个数组');
  }

  return {
    maxRetries,
    baseDelay,
    maxDelay,
    backoffFactor,
    retryableErrors
  };
}

// ============================================================================
// 缓存验证 (Cache Validation)
// ============================================================================

export function validateCacheOptions(options: any): import('../types/index').CacheOptions {
  if (!options || typeof options !== 'object') {
    throw new ValidationError('缓存选项必须是一个对象');
  }

  const { ttl, maxSize, cleanupInterval } = options;

  if (!isValidNumber(ttl, 1000, 86400000)) { // 1秒到1天
    throw new ValidationError('TTL必须在1000-86400000毫秒之间');
  }

  if (!isValidNumber(maxSize, 1, 10000)) {
    throw new ValidationError('最大缓存大小必须在1-10000之间');
  }

  if (!isValidNumber(cleanupInterval, 1000, 3600000)) { // 1秒到1小时
    throw new ValidationError('清理间隔必须在1000-3600000毫秒之间');
  }

  return {
    ttl,
    maxSize,
    cleanupInterval
  };
}

// ============================================================================
// 日志验证 (Logging Validation)
// ============================================================================

export function validateLogLevel(level: any): import('../types/index').LogLevel {
  const validLevels = ['debug', 'info', 'warn', 'error'];
  
  if (!validLevels.includes(level)) {
    throw new ValidationError(`日志级别必须是以下之一: ${validLevels.join(', ')}`);
  }

  return level as import('../types/index').LogLevel;
}

export function validateLogEntry(entry: any): import('../types/index').LogEntry {
  if (!entry || typeof entry !== 'object') {
    throw new ValidationError('日志条目必须是一个对象');
  }

  const { level, message, timestamp, context, error } = entry;

  if (!isValidString(message)) {
    throw new ValidationError('日志消息不能为空');
  }

  if (!isValidDate(timestamp)) {
    throw new ValidationError('日志时间戳必须是有效的日期');
  }

  const validatedEntry: import('../types/index').LogEntry = {
    level: validateLogLevel(level),
    message: message.trim(),
    timestamp
  };

  if (context !== undefined) {
    if (typeof context !== 'object' || context === null) {
      throw new ValidationError('日志上下文必须是一个对象');
    }
    validatedEntry.context = context;
  }

  if (error !== undefined) {
    if (!(error instanceof Error)) {
      throw new ValidationError('错误对象必须是Error实例');
    }
    validatedEntry.error = error;
  }

  return validatedEntry;
}