import ffmpeg from 'fluent-ffmpeg';
import * as ffmpegInstaller from '@ffmpeg-installer/ffmpeg';
import * as fs from 'fs-extra';
import * as crypto from 'crypto';
import { 
  FileError, 
  FFmpegError,
  ValidationError 
} from '../types';

// 设置FFmpeg路径
ffmpeg.setFfmpegPath(ffmpegInstaller.path);

/**
 * 视频验证结果
 */
export interface VideoValidationResult {
  isValid: boolean;
  fileSize: number;
  duration: number;
  format: string;
  videoCodec: string;
  audioCodec: string;
  resolution: {
    width: number;
    height: number;
  };
  bitrate: number;
  frameRate: number;
  checksum: string;
  errors: string[];
  warnings: string[];
}

/**
 * 验证配置
 */
export interface VideoValidatorConfig {
  minDuration: number; // 最小时长（秒）
  maxDuration: number; // 最大时长（秒）
  minFileSize: number; // 最小文件大小（字节）
  maxFileSize: number; // 最大文件大小（字节）
  allowedFormats: string[]; // 允许的格式
  allowedVideoCodecs: string[]; // 允许的视频编码
  allowedAudioCodecs: string[]; // 允许的音频编码
  minResolution: { width: number; height: number }; // 最小分辨率
  maxResolution: { width: number; height: number }; // 最大分辨率
  checksumAlgorithm: 'md5' | 'sha1' | 'sha256'; // 校验和算法
}

/**
 * 视频文件完整性验证器
 * 验证视频文件的完整性、格式和质量
 */
export class VideoValidator {
  private config: VideoValidatorConfig;

  private readonly defaultConfig: VideoValidatorConfig = {
    minDuration: 1, // 1秒
    maxDuration: 7200, // 2小时
    minFileSize: 1024, // 1KB
    maxFileSize: 5 * 1024 * 1024 * 1024, // 5GB
    allowedFormats: ['mp4', 'avi', 'mkv', 'mov', 'wmv', 'flv'],
    allowedVideoCodecs: ['h264', 'h265', 'hevc', 'vp8', 'vp9', 'mpeg4'],
    allowedAudioCodecs: ['aac', 'mp3', 'ac3', 'opus', 'vorbis'],
    minResolution: { width: 320, height: 240 },
    maxResolution: { width: 7680, height: 4320 }, // 8K
    checksumAlgorithm: 'md5'
  };

  constructor(config?: Partial<VideoValidatorConfig>) {
    this.config = { ...this.defaultConfig, ...config };
  }

  /**
   * 验证视频文件
   * @param filePath 视频文件路径
   * @returns 验证结果
   */
  async validateVideo(filePath: string): Promise<VideoValidationResult> {
    const result: VideoValidationResult = {
      isValid: false,
      fileSize: 0,
      duration: 0,
      format: '',
      videoCodec: '',
      audioCodec: '',
      resolution: { width: 0, height: 0 },
      bitrate: 0,
      frameRate: 0,
      checksum: '',
      errors: [],
      warnings: []
    };

    try {
      // 检查文件是否存在
      if (!(await fs.pathExists(filePath))) {
        result.errors.push('文件不存在');
        return result;
      }

      // 获取文件基本信息
      const stats = await fs.stat(filePath);
      result.fileSize = stats.size;

      // 验证文件大小
      if (result.fileSize < this.config.minFileSize) {
        result.errors.push(`文件大小过小: ${result.fileSize} bytes < ${this.config.minFileSize} bytes`);
      }
      if (result.fileSize > this.config.maxFileSize) {
        result.errors.push(`文件大小过大: ${result.fileSize} bytes > ${this.config.maxFileSize} bytes`);
      }

      // 计算文件校验和
      result.checksum = await this.calculateChecksum(filePath);

      // 获取视频元数据
      const metadata = await this.getVideoMetadata(filePath);
      
      // 提取视频信息
      this.extractVideoInfo(metadata, result);

      // 验证视频格式
      this.validateFormat(result);

      // 验证视频编码
      this.validateCodecs(result);

      // 验证时长
      this.validateDuration(result);

      // 验证分辨率
      this.validateResolution(result);

      // 验证视频流完整性
      await this.validateVideoIntegrity(filePath, result);

      // 如果没有错误，则认为文件有效
      result.isValid = result.errors.length === 0;

    } catch (error) {
      result.errors.push(`验证过程中发生错误: ${error instanceof Error ? error.message : '未知错误'}`);
    }

    return result;
  }

  /**
   * 获取视频元数据
   */
  private async getVideoMetadata(filePath: string): Promise<any> {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (error, metadata) => {
        if (error) {
          reject(new FFmpegError(`获取视频元数据失败: ${error.message}`));
        } else {
          resolve(metadata);
        }
      });
    });
  }

  /**
   * 提取视频信息
   */
  private extractVideoInfo(metadata: any, result: VideoValidationResult): void {
    try {
      // 基本信息
      result.duration = metadata.format?.duration || 0;
      result.format = metadata.format?.format_name?.split(',')[0] || '';
      result.bitrate = metadata.format?.bit_rate ? parseInt(metadata.format.bit_rate, 10) : 0;

      // 查找视频流
      const videoStream = metadata.streams?.find((stream: any) => stream.codec_type === 'video');
      if (videoStream) {
        result.videoCodec = videoStream.codec_name || '';
        result.resolution.width = videoStream.width || 0;
        result.resolution.height = videoStream.height || 0;
        
        // 计算帧率
        if (videoStream.r_frame_rate) {
          const [num, den] = videoStream.r_frame_rate.split('/').map(Number);
          result.frameRate = den > 0 ? num / den : 0;
        }
      }

      // 查找音频流
      const audioStream = metadata.streams?.find((stream: any) => stream.codec_type === 'audio');
      if (audioStream) {
        result.audioCodec = audioStream.codec_name || '';
      }
    } catch (error) {
      result.warnings.push(`提取视频信息时发生错误: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 验证视频格式
   */
  private validateFormat(result: VideoValidationResult): void {
    if (!result.format) {
      result.errors.push('无法识别视频格式');
      return;
    }

    const normalizedFormat = result.format.toLowerCase();
    if (!this.config.allowedFormats.some(format => normalizedFormat.includes(format))) {
      result.errors.push(`不支持的视频格式: ${result.format}`);
    }
  }

  /**
   * 验证编码格式
   */
  private validateCodecs(result: VideoValidationResult): void {
    // 验证视频编码
    if (result.videoCodec) {
      const normalizedVideoCodec = result.videoCodec.toLowerCase();
      if (!this.config.allowedVideoCodecs.some(codec => normalizedVideoCodec.includes(codec))) {
        result.warnings.push(`不常见的视频编码: ${result.videoCodec}`);
      }
    } else {
      result.errors.push('未找到视频流');
    }

    // 验证音频编码（可选）
    if (result.audioCodec) {
      const normalizedAudioCodec = result.audioCodec.toLowerCase();
      if (!this.config.allowedAudioCodecs.some(codec => normalizedAudioCodec.includes(codec))) {
        result.warnings.push(`不常见的音频编码: ${result.audioCodec}`);
      }
    }
  }

  /**
   * 验证视频时长
   */
  private validateDuration(result: VideoValidationResult): void {
    if (result.duration < this.config.minDuration) {
      result.errors.push(`视频时长过短: ${result.duration}s < ${this.config.minDuration}s`);
    }
    if (result.duration > this.config.maxDuration) {
      result.warnings.push(`视频时长较长: ${result.duration}s > ${this.config.maxDuration}s`);
    }
  }

  /**
   * 验证视频分辨率
   */
  private validateResolution(result: VideoValidationResult): void {
    const { width, height } = result.resolution;
    
    if (width < this.config.minResolution.width || height < this.config.minResolution.height) {
      result.warnings.push(`分辨率较低: ${width}x${height} < ${this.config.minResolution.width}x${this.config.minResolution.height}`);
    }
    
    if (width > this.config.maxResolution.width || height > this.config.maxResolution.height) {
      result.warnings.push(`分辨率较高: ${width}x${height} > ${this.config.maxResolution.width}x${this.config.maxResolution.height}`);
    }
  }

  /**
   * 验证视频流完整性
   */
  private async validateVideoIntegrity(filePath: string, result: VideoValidationResult): Promise<void> {
    return new Promise((resolve) => {
      // 使用FFmpeg检查视频流的完整性
      ffmpeg(filePath)
        .videoFilters('blackdetect=d=0.1:pix_th=0.1')
        .format('null')
        .output('-')
        .on('error', (error) => {
          result.warnings.push(`视频完整性检查警告: ${error.message}`);
          resolve();
        })
        .on('end', () => {
          resolve();
        })
        .run();
    });
  }

  /**
   * 计算文件校验和
   */
  private async calculateChecksum(filePath: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const hash = crypto.createHash(this.config.checksumAlgorithm);
      const stream = fs.createReadStream(filePath);

      stream.on('data', (data) => {
        hash.update(data);
      });

      stream.on('end', () => {
        resolve(hash.digest('hex'));
      });

      stream.on('error', (error) => {
        reject(new FileError(`计算校验和失败: ${error.message}`));
      });
    });
  }

  /**
   * 快速验证（仅检查基本信息）
   */
  async quickValidate(filePath: string): Promise<boolean> {
    try {
      // 检查文件是否存在
      if (!(await fs.pathExists(filePath))) {
        return false;
      }

      // 检查文件大小
      const stats = await fs.stat(filePath);
      if (stats.size < this.config.minFileSize) {
        return false;
      }

      // 简单的格式检查
      const metadata = await this.getVideoMetadata(filePath);
      const duration = metadata.format?.duration || 0;
      
      return duration >= this.config.minDuration;
    } catch {
      return false;
    }
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<VideoValidatorConfig>): void {
    this.config = { ...this.config, ...config };
  }
}

export default VideoValidator;
