import axios, { AxiosResponse } from 'axios';
import * as fs from 'fs-extra';
import * as path from 'path';
import { 
  CourseResource, 
  DownloadProgress, 
  FileError, 
  NetworkError,
  ApiError 
} from '../types';
import { FileOrganizer } from './FileOrganizer';

/**
 * 教材下载器配置
 */
export interface TextbookDownloaderConfig {
  maxConcurrentDownloads: number;
  chunkSize: number;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
}

/**
 * 下载任务状态
 */
export interface DownloadTaskStatus {
  id: string;
  resource: CourseResource;
  status: 'pending' | 'downloading' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  downloadedBytes: number;
  totalBytes: number;
  speed: number;
  estimatedTime: number;
  error?: string;
  startTime?: Date;
  endTime?: Date;
}

/**
 * 电子教材下载器
 * 负责处理教材资源的下载、格式转换和保存
 */
export class TextbookDownloader {
  private config: TextbookDownloaderConfig;
  private fileOrganizer: FileOrganizer;
  private activeTasks: Map<string, DownloadTaskStatus> = new Map();
  private progressCallbacks: Map<string, (progress: DownloadProgress) => void> = new Map();

  private readonly defaultConfig: TextbookDownloaderConfig = {
    maxConcurrentDownloads: 3,
    chunkSize: 1024 * 1024, // 1MB chunks
    timeout: 30000, // 30 seconds
    retryAttempts: 3,
    retryDelay: 1000 // 1 second
  };

  constructor(
    fileOrganizer: FileOrganizer,
    config?: Partial<TextbookDownloaderConfig>
  ) {
    this.fileOrganizer = fileOrganizer;
    this.config = { ...this.defaultConfig, ...config };
  }

  /**
   * 下载教材资源
   * @param resource 教材资源
   * @param onProgress 进度回调函数
   * @returns 下载任务状态
   */
  async downloadTextbook(
    resource: CourseResource,
    onProgress?: (progress: DownloadProgress) => void
  ): Promise<DownloadTaskStatus> {
    const taskId = this.generateTaskId(resource);
    
    // 检查是否已有相同任务在进行
    if (this.activeTasks.has(taskId)) {
      const existingTask = this.activeTasks.get(taskId)!;
      if (existingTask.status === 'downloading') {
        throw new Error('该资源正在下载中');
      }
    }

    // 创建下载任务
    const task: DownloadTaskStatus = {
      id: taskId,
      resource,
      status: 'pending',
      progress: 0,
      downloadedBytes: 0,
      totalBytes: 0,
      speed: 0,
      estimatedTime: 0,
      startTime: new Date()
    };

    this.activeTasks.set(taskId, task);
    
    if (onProgress) {
      this.progressCallbacks.set(taskId, onProgress);
    }

    try {
      // 生成文件路径
      const outputPath = this.fileOrganizer.generatePath(resource);
      
      // 检查文件是否已存在且需要更新
      const needsUpdate = await this.fileOrganizer.checkVersionUpdate(outputPath, resource);
      if (!needsUpdate) {
        task.status = 'completed';
        task.progress = 100;
        task.endTime = new Date();
        this.notifyProgress(taskId, task);
        return task;
      }

      // 创建目录结构
      await this.fileOrganizer.createDirectoryStructure(outputPath);

      // 开始下载
      task.status = 'downloading';
      this.notifyProgress(taskId, task);

      await this.performDownload(task, outputPath);

      // 验证下载的文件
      await this.validateDownloadedFile(outputPath, resource);

      // 注册文件到文件组织器
      await this.fileOrganizer.registerFile(resource, outputPath);

      task.status = 'completed';
      task.progress = 100;
      task.endTime = new Date();
      this.notifyProgress(taskId, task);

      return task;
    } catch (error) {
      task.status = 'failed';
      task.error = error instanceof Error ? error.message : '下载失败';
      task.endTime = new Date();
      this.notifyProgress(taskId, task);
      throw error;
    } finally {
      // 清理回调
      this.progressCallbacks.delete(taskId);
    }
  }

  /**
   * 执行实际的下载操作
   */
  private async performDownload(task: DownloadTaskStatus, outputPath: string): Promise<void> {
    const { resource } = task;
    let attempt = 0;

    while (attempt < this.config.retryAttempts) {
      try {
        // 获取文件信息
        const headResponse = await axios.head(resource.url, {
          timeout: this.config.timeout
        });

        const totalBytes = parseInt(headResponse.headers['content-length'] || '0', 10);
        task.totalBytes = totalBytes;

        // 检查是否支持断点续传
        const supportsRangeRequests = headResponse.headers['accept-ranges'] === 'bytes';

        if (supportsRangeRequests && totalBytes > this.config.chunkSize) {
          // 分块下载
          await this.downloadInChunks(task, outputPath, totalBytes);
        } else {
          // 单次下载
          await this.downloadComplete(task, outputPath);
        }

        return; // 下载成功，退出重试循环
      } catch (error) {
        attempt++;
        
        if (attempt >= this.config.retryAttempts) {
          throw new NetworkError(`下载失败，已重试${this.config.retryAttempts}次`, {
            originalError: error as Error,
            context: { resource: resource.id, attempt }
          });
        }

        // 等待后重试
        await this.delay(this.config.retryDelay * attempt);
      }
    }
  }

  /**
   * 分块下载
   */
  private async downloadInChunks(
    task: DownloadTaskStatus, 
    outputPath: string, 
    totalBytes: number
  ): Promise<void> {
    const tempPath = `${outputPath}.tmp`;
    const writeStream = fs.createWriteStream(tempPath);
    
    try {
      let downloadedBytes = 0;
      const startTime = Date.now();

      for (let start = 0; start < totalBytes; start += this.config.chunkSize) {
        const end = Math.min(start + this.config.chunkSize - 1, totalBytes - 1);
        
        const response = await axios.get(task.resource.url, {
          headers: {
            'Range': `bytes=${start}-${end}`
          },
          responseType: 'stream',
          timeout: this.config.timeout
        });

        // 写入数据块
        await this.writeStreamChunk(response.data, writeStream);
        
        downloadedBytes += (end - start + 1);
        
        // 更新进度
        this.updateTaskProgress(task, downloadedBytes, startTime);
        this.notifyProgress(task.id, task);

        // 检查任务是否被取消
        if (task.status === 'cancelled') {
          throw new Error('下载已取消');
        }
      }

      writeStream.end();
      
      // 移动临时文件到最终位置
      await fs.move(tempPath, outputPath, { overwrite: true });
    } catch (error) {
      writeStream.destroy();
      // 清理临时文件
      if (await fs.pathExists(tempPath)) {
        await fs.remove(tempPath);
      }
      throw error;
    }
  }

  /**
   * 完整下载（不分块）
   */
  private async downloadComplete(task: DownloadTaskStatus, outputPath: string): Promise<void> {
    const response = await axios.get(task.resource.url, {
      responseType: 'stream',
      timeout: this.config.timeout,
      onDownloadProgress: (progressEvent) => {
        if (progressEvent.total) {
          task.totalBytes = progressEvent.total;
          task.downloadedBytes = progressEvent.loaded;
          this.updateTaskProgress(task, progressEvent.loaded, Date.now());
          this.notifyProgress(task.id, task);
        }
      }
    });

    const tempPath = `${outputPath}.tmp`;
    const writeStream = fs.createWriteStream(tempPath);

    try {
      response.data.pipe(writeStream);
      
      await new Promise<void>((resolve, reject) => {
        writeStream.on('finish', resolve);
        writeStream.on('error', reject);
        response.data.on('error', reject);
      });

      // 移动临时文件到最终位置
      await fs.move(tempPath, outputPath, { overwrite: true });
    } catch (error) {
      writeStream.destroy();
      if (await fs.pathExists(tempPath)) {
        await fs.remove(tempPath);
      }
      throw error;
    }
  }

  /**
   * 写入数据流块
   */
  private writeStreamChunk(dataStream: any, writeStream: fs.WriteStream): Promise<void> {
    return new Promise((resolve, reject) => {
      dataStream.pipe(writeStream, { end: false });
      dataStream.on('end', resolve);
      dataStream.on('error', reject);
    });
  }

  /**
   * 更新任务进度
   */
  private updateTaskProgress(task: DownloadTaskStatus, downloadedBytes: number, startTime: number): void {
    task.downloadedBytes = downloadedBytes;
    task.progress = task.totalBytes > 0 ? (downloadedBytes / task.totalBytes) * 100 : 0;
    
    const elapsedTime = (Date.now() - startTime) / 1000; // 秒
    task.speed = elapsedTime > 0 ? downloadedBytes / elapsedTime : 0;
    
    const remainingBytes = task.totalBytes - downloadedBytes;
    task.estimatedTime = task.speed > 0 ? remainingBytes / task.speed : 0;
  }

  /**
   * 通知进度更新
   */
  private notifyProgress(taskId: string, task: DownloadTaskStatus): void {
    const callback = this.progressCallbacks.get(taskId);
    if (callback) {
      callback({
        taskId,
        progress: task.progress,
        downloadedBytes: task.downloadedBytes,
        totalBytes: task.totalBytes,
        speed: task.speed,
        estimatedTime: task.estimatedTime,
        status: task.status as any
      });
    }
  }

  /**
   * 验证下载的文件
   */
  private async validateDownloadedFile(filePath: string, resource: CourseResource): Promise<void> {
    try {
      // 检查文件是否存在
      if (!(await fs.pathExists(filePath))) {
        throw new FileError('下载的文件不存在');
      }

      // 检查文件大小
      const stats = await fs.stat(filePath);
      if (resource.metadata?.fileSize && stats.size !== resource.metadata.fileSize) {
        throw new FileError(`文件大小不匹配，期望: ${resource.metadata.fileSize}, 实际: ${stats.size}`);
      }

      // 检查文件格式（基于扩展名）
      const extension = path.extname(filePath).toLowerCase();
      if (resource.type === 'textbook' && !['.pdf', '.epub', '.mobi'].includes(extension)) {
        console.warn(`教材文件格式可能不正确: ${extension}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new FileError(`文件验证失败: ${errorMessage}`, {
        originalError: error as Error,
        context: { filePath, resource: resource.id }
      });
    }
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(resource: CourseResource): string {
    return `textbook_${resource.id}_${Date.now()}`;
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 取消下载任务
   */
  cancelDownload(taskId: string): void {
    const task = this.activeTasks.get(taskId);
    if (task && task.status === 'downloading') {
      task.status = 'cancelled';
      this.notifyProgress(taskId, task);
    }
  }

  /**
   * 获取任务状态
   */
  getTaskStatus(taskId: string): DownloadTaskStatus | undefined {
    return this.activeTasks.get(taskId);
  }

  /**
   * 获取所有活动任务
   */
  getActiveTasks(): DownloadTaskStatus[] {
    return Array.from(this.activeTasks.values());
  }

  /**
   * 清理已完成的任务
   */
  clearCompletedTasks(): void {
    for (const [taskId, task] of this.activeTasks.entries()) {
      if (task.status === 'completed' || task.status === 'failed' || task.status === 'cancelled') {
        this.activeTasks.delete(taskId);
        this.progressCallbacks.delete(taskId);
      }
    }
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<TextbookDownloaderConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 获取当前配置
   */
  getConfig(): TextbookDownloaderConfig {
    return { ...this.config };
  }
}

export default TextbookDownloader;
