import { ResourceService } from '../ResourceService';
import { SmartEduClient } from '../SmartEduClient';
import { 
  CourseResource, 
  ResourceDetail, 
  CourseFilters,
  ApiError,
  ValidationError
} from '../../types';

// Mock SmartEduClient
jest.mock('../SmartEduClient');

describe('ResourceService', () => {
  let resourceService: ResourceService;
  let mockClient: jest.Mocked<SmartEduClient>;

  const mockFilters: CourseFilters = {
    stage: 'primary',
    grade: 'grade1',
    subject: 'chinese',
    version: 'renjiao',
    volume: 'volume1'
  };

  const mockResources: CourseResource[] = [
    {
      id: 'resource1',
      title: '语文教材',
      type: 'textbook',
      url: 'https://example.com/textbook1',
      metadata: {
        stage: 'primary',
        grade: 'grade1',
        subject: 'chinese',
        version: 'renjiao',
        volume: 'volume1',
        fileSize: 1024000
      },
      requiresAuth: false,
      accessLevel: 'public'
    },
    {
      id: 'resource2',
      title: '语文视频课程',
      type: 'video',
      url: 'https://example.com/video1',
      metadata: {
        stage: 'primary',
        grade: 'grade1',
        subject: 'chinese',
        version: 'renjiao',
        volume: 'volume1',
        duration: 1800
      },
      requiresAuth: true,
      accessLevel: 'registered'
    }
  ];

  const mockResourceDetail: ResourceDetail = {
    ...mockResources[0],
    description: '小学一年级语文教材',
    thumbnailUrl: 'https://example.com/thumb1.jpg',
    tags: ['教材', '语文', '小学'],
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-15')
  };

  beforeEach(() => {
    mockClient = new SmartEduClient() as jest.Mocked<SmartEduClient>;
    resourceService = new ResourceService(mockClient);
    
    // 清除所有mock调用记录
    jest.clearAllMocks();
  });

  describe('searchResources', () => {
    it('should search resources successfully', async () => {
      mockClient.searchResources.mockResolvedValue(mockResources);

      const result = await resourceService.searchResources(mockFilters);

      expect(result).toEqual(mockResources);
      expect(mockClient.searchResources).toHaveBeenCalledWith(mockFilters);
    });

    it('should validate filters before searching', async () => {
      const incompleteFilters = { stage: 'primary' } as CourseFilters;

      await expect(resourceService.searchResources(incompleteFilters))
        .rejects.toThrow(ValidationError);
      
      expect(mockClient.searchResources).not.toHaveBeenCalled();
    });

    it('should cache search results', async () => {
      mockClient.searchResources.mockResolvedValue(mockResources);

      // 第一次调用
      await resourceService.searchResources(mockFilters);
      // 第二次调用应该使用缓存
      const result = await resourceService.searchResources(mockFilters);

      expect(result).toEqual(mockResources);
      expect(mockClient.searchResources).toHaveBeenCalledTimes(1);
    });

    it('should handle search errors', async () => {
      const error = new Error('Network error');
      mockClient.searchResources.mockRejectedValue(error);

      await expect(resourceService.searchResources(mockFilters))
        .rejects.toThrow(ApiError);
    });
  });

  describe('getResourceDetail', () => {
    it('should get resource detail successfully', async () => {
      mockClient.getResourceDetail.mockResolvedValue(mockResourceDetail);

      const result = await resourceService.getResourceDetail('resource1');

      expect(result).toEqual(mockResourceDetail);
      expect(mockClient.getResourceDetail).toHaveBeenCalledWith('resource1');
    });

    it('should validate resource ID', async () => {
      await expect(resourceService.getResourceDetail(''))
        .rejects.toThrow(ValidationError);
      
      expect(mockClient.getResourceDetail).not.toHaveBeenCalled();
    });

    it('should cache resource details', async () => {
      mockClient.getResourceDetail.mockResolvedValue(mockResourceDetail);

      // 第一次调用
      await resourceService.getResourceDetail('resource1');
      // 第二次调用应该使用缓存
      const result = await resourceService.getResourceDetail('resource1');

      expect(result).toEqual(mockResourceDetail);
      expect(mockClient.getResourceDetail).toHaveBeenCalledTimes(1);
    });

    it('should handle detail fetch errors', async () => {
      const error = new Error('Resource not found');
      mockClient.getResourceDetail.mockRejectedValue(error);

      await expect(resourceService.getResourceDetail('resource1'))
        .rejects.toThrow(ApiError);
    });
  });

  describe('checkResourceAccess', () => {
    const mockAccessInfo = {
      hasAccess: true,
      requiresAuth: false,
      accessLevel: 'public' as const,
      message: undefined
    };

    it('should check resource access successfully', async () => {
      mockClient.checkResourceAccess.mockResolvedValue(mockAccessInfo);

      const result = await resourceService.checkResourceAccess('resource1');

      expect(result).toEqual(mockAccessInfo);
      expect(mockClient.checkResourceAccess).toHaveBeenCalledWith('resource1');
    });

    it('should validate resource ID', async () => {
      await expect(resourceService.checkResourceAccess(''))
        .rejects.toThrow(ValidationError);
      
      expect(mockClient.checkResourceAccess).not.toHaveBeenCalled();
    });

    it('should cache access information', async () => {
      mockClient.checkResourceAccess.mockResolvedValue(mockAccessInfo);

      // 第一次调用
      await resourceService.checkResourceAccess('resource1');
      // 第二次调用应该使用缓存
      const result = await resourceService.checkResourceAccess('resource1');

      expect(result).toEqual(mockAccessInfo);
      expect(mockClient.checkResourceAccess).toHaveBeenCalledTimes(1);
    });
  });

  describe('batchCheckResourceAccess', () => {
    it('should handle empty resource IDs', async () => {
      const result = await resourceService.batchCheckResourceAccess([]);
      expect(result).toEqual({});
    });

    it('should batch check resource access', async () => {
      const mockAccessInfo1 = {
        hasAccess: true,
        requiresAuth: false,
        accessLevel: 'public' as const
      };
      const mockAccessInfo2 = {
        hasAccess: false,
        requiresAuth: true,
        accessLevel: 'registered' as const
      };

      mockClient.checkResourceAccess
        .mockResolvedValueOnce(mockAccessInfo1)
        .mockResolvedValueOnce(mockAccessInfo2);

      const result = await resourceService.batchCheckResourceAccess(['resource1', 'resource2']);

      expect(result).toEqual({
        resource1: mockAccessInfo1,
        resource2: mockAccessInfo2
      });
    });

    it('should handle errors gracefully in batch check', async () => {
      mockClient.checkResourceAccess
        .mockResolvedValueOnce({
          hasAccess: true,
          requiresAuth: false,
          accessLevel: 'public' as const
        })
        .mockRejectedValueOnce(new Error('Access check failed'));

      const result = await resourceService.batchCheckResourceAccess(['resource1', 'resource2']);

      expect(result.resource1.hasAccess).toBe(true);
      expect(result.resource2.hasAccess).toBe(false);
      expect(result.resource2.message).toBe('权限检查失败');
    });
  });

  describe('filterDownloadableResources', () => {
    it('should filter downloadable resources for guest user', async () => {
      mockClient.checkResourceAccess
        .mockResolvedValueOnce({
          hasAccess: true,
          requiresAuth: false,
          accessLevel: 'public' as const
        })
        .mockResolvedValueOnce({
          hasAccess: false,
          requiresAuth: true,
          accessLevel: 'registered' as const
        });

      const result = await resourceService.filterDownloadableResources(mockResources, false);

      expect(result.downloadable).toHaveLength(1);
      expect(result.downloadable[0].id).toBe('resource1');
      expect(result.restricted).toHaveLength(1);
      expect(result.restricted[0].id).toBe('resource2');
    });

    it('should filter downloadable resources for logged-in user', async () => {
      mockClient.checkResourceAccess
        .mockResolvedValueOnce({
          hasAccess: true,
          requiresAuth: false,
          accessLevel: 'public' as const
        })
        .mockResolvedValueOnce({
          hasAccess: true,
          requiresAuth: true,
          accessLevel: 'registered' as const
        });

      const result = await resourceService.filterDownloadableResources(mockResources, true);

      expect(result.downloadable).toHaveLength(2);
      expect(result.restricted).toHaveLength(0);
    });
  });

  describe('getResourceStats', () => {
    it('should calculate resource statistics', () => {
      const stats = resourceService.getResourceStats(mockResources);

      expect(stats.total).toBe(2);
      expect(stats.byType.textbook).toBe(1);
      expect(stats.byType.video).toBe(1);
      expect(stats.byAccessLevel.public).toBe(1);
      expect(stats.byAccessLevel.registered).toBe(1);
      expect(stats.requiresAuth).toBe(1);
      expect(stats.public).toBe(1);
    });

    it('should handle empty resource list', () => {
      const stats = resourceService.getResourceStats([]);

      expect(stats.total).toBe(0);
      expect(stats.byType).toEqual({});
      expect(stats.byAccessLevel).toEqual({});
      expect(stats.requiresAuth).toBe(0);
      expect(stats.public).toBe(0);
    });
  });

  describe('cache management', () => {
    it('should clear all cache', async () => {
      mockClient.searchResources.mockResolvedValue(mockResources);
      
      // 缓存一些数据
      await resourceService.searchResources(mockFilters);
      
      // 清除缓存
      resourceService.clearCache();
      
      // 再次调用应该重新请求
      await resourceService.searchResources(mockFilters);
      
      expect(mockClient.searchResources).toHaveBeenCalledTimes(2);
    });

    it('should clear cache by pattern', async () => {
      mockClient.searchResources.mockResolvedValue(mockResources);
      mockClient.getResourceDetail.mockResolvedValue(mockResourceDetail);
      
      // 缓存搜索和详情数据
      await resourceService.searchResources(mockFilters);
      await resourceService.getResourceDetail('resource1');
      
      // 只清除搜索缓存
      resourceService.clearCache('search');
      
      // 搜索应该重新请求，详情应该使用缓存
      await resourceService.searchResources(mockFilters);
      await resourceService.getResourceDetail('resource1');
      
      expect(mockClient.searchResources).toHaveBeenCalledTimes(2);
      expect(mockClient.getResourceDetail).toHaveBeenCalledTimes(1);
    });

    it('should provide cache statistics', async () => {
      mockClient.searchResources.mockResolvedValue(mockResources);
      
      await resourceService.searchResources(mockFilters);
      
      const stats = resourceService.getCacheStats();
      
      expect(stats.size).toBeGreaterThan(0);
      expect(stats.keys.length).toBeGreaterThan(0);
      expect(stats.memoryUsage).toBeGreaterThan(0);
    });
  });
});