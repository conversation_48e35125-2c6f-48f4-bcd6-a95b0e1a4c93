/**
 * 智慧教育平台配置文件
 * 集中管理API端点、标签维度等配置信息
 * 基于FlyEduDownloader项目的最新API信息更新
 */

export interface SmartEduConfig {
  api: {
    baseURL: string;
    fileBaseURL: string;
    endpoints: {
      // 教材相关API
      materials: string;
      materialDetails: string;
      materialResources: string;
      materialParts: string;
      materialTree: string;
      materialAudios: string; // 英语听力音频

      // 课程资源API
      nationalLessonDetails: string; // 教育部资源
      qualityCourseDetails: string;  // 学校网课
      prepareLessonDetails: string;  // 备课资源

      // 标签和版本API
      tags: string;
      version: string;
    };
  };
  tagDimensions: {
    stage: string;
    grade: string;
    subject: string;
    version: string;
    volume: string;
  };
  unitMapping: {
    patterns: {
      unitKeyword: RegExp;
      gardenKeyword: RegExp;
      oralKeyword: RegExp;
      lessonNumber: RegExp;
    };
    // 移除硬编码的关键词，改为从API数据中动态提取
    fallbackUnitOrder: string[];
  };
  // 新增：认证和请求配置
  auth: {
    userAgent: string;
    defaultHeaders: Record<string, string>;
    timeout: number;
    retryAttempts: number;
  };
}

/**
 * 默认配置 - 基于FlyEduDownloader项目的最新API信息
 */
export const defaultSmartEduConfig: SmartEduConfig = {
  api: {
    baseURL: 'https://s-file-1.ykt.cbern.com.cn',
    fileBaseURL: 'https://s-file-2.ykt.cbern.com.cn',
    endpoints: {
      // 教材相关API（基于FlyEduDownloader的API分析）
      materials: '/zxx/ndrs/national_lesson/teachingmaterials/part_100.json',
      materialDetails: '/zxx/ndrv2/resources/tch_material/details/{materialId}.json',
      materialResources: '/zxx/ndrs/special_edu/thematic_course/{materialId}/resources/list.json',
      materialParts: '/zxx/ndrs/national_lesson/teachingmaterials/{materialId}/resources/parts.json',
      materialTree: '/zxx/ndrv2/national_lesson/trees/{materialId}.json',
      materialAudios: '/zxx/ndrs/resources/{materialId}/relation_audios.json',

      // 课程资源API（基于FlyEduDownloader的解析接口）
      nationalLessonDetails: '/zxx/ndrv2/national_lesson/resources/details/{activityId}.json',
      qualityCourseDetails: '/zxx/ndrv2/resources/{courseId}.json',
      prepareLessonDetails: '/zxx/ndrv2/prepare_lesson/resources/details/{lessonId}.json',

      // 标签和版本API
      tags: '/zxx/ndrs/tags/national_lesson_tag.json',
      version: '/zxx/ndrs/national_lesson/teachingmaterials/version/data_version.json'
    }
  },
  tagDimensions: {
    stage: 'zxxxd',    // 学段
    grade: 'zxxnj',    // 年级
    subject: 'zxxxk',  // 学科
    version: 'zxxbb',  // 版本
    volume: 'zxxcc'    // 册次
  },
  unitMapping: {
    patterns: {
      unitKeyword: /第([一二三四五六七八九十\d]+)[单元]/,
      gardenKeyword: /语文园地([一二三四五六七八九十\d]*)/,
      oralKeyword: /口语交际|习作/,
      lessonNumber: /^(\d+)[.*·\s]/
    },
    // 移除硬编码关键词，改为从API数据中动态提取单元信息
    // 只保留回退排序，当API数据不完整时使用
    fallbackUnitOrder: ['第一单元', '第二单元', '第三单元', '第四单元', '第五单元', '第六单元', '第七单元', '第八单元', '其他']
  },
  // 新增：认证和请求配置（基于FlyEduDownloader的请求头分析）
  auth: {
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    defaultHeaders: {
      'Accept': 'application/json, text/plain, */*',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache',
      'Referer': 'https://basic.smartedu.cn/',
      'Origin': 'https://basic.smartedu.cn'
    },
    timeout: 30000,
    retryAttempts: 3
  }
};

/**
 * 获取配置
 * 支持环境变量覆盖默认配置
 */
export function getSmartEduConfig(): SmartEduConfig {
  const config = { ...defaultSmartEduConfig };
  
  // 支持通过环境变量覆盖API基础URL
  if (process.env.SMARTEDU_API_BASE_URL) {
    config.api.baseURL = process.env.SMARTEDU_API_BASE_URL;
  }
  
  return config;
}
