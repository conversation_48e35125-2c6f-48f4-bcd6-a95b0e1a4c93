{"name": "smart-edu-downloader", "version": "1.0.0", "description": "智慧教育资源下载器 - 从国家中小学智慧平台下载教育资源", "main": "dist/main/main.js", "homepage": "./", "author": "Smart Edu Downloader Team", "license": "MIT", "private": true, "scripts": {"dev": "concurrently \"npm run dev:main\" \"npm run dev:renderer\"", "dev:main": "webpack --config webpack.main.config.js --mode development --watch", "dev:renderer": "webpack serve --config webpack.renderer.config.js --mode development", "build": "npm run build:main && npm run build:renderer", "build:main": "webpack --config webpack.main.config.js --mode production", "build:renderer": "webpack --config webpack.renderer.config.js --mode production", "start": "electron .", "pack": "electron-builder --dir", "dist": "npm run build && electron-builder", "dist:mac": "npm run build && electron-builder --mac", "dist:win": "npm run build && electron-builder --win", "dist:linux": "npm run build && electron-builder --linux", "dist:all": "npm run build && electron-builder --mac --win --linux", "publish": "npm run build && electron-builder --publish=always", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,scss,md}\"", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:build": "node scripts/build-test.js", "test:packaging": "node scripts/packaging-test.js", "test:install": "node scripts/install-test.js", "test:deployment": "node scripts/deployment-test.js"}, "dependencies": {"@ffmpeg-installer/ffmpeg": "^1.1.0", "antd": "^5.12.8", "axios": "^1.6.2", "electron-log": "^5.4.1", "electron-updater": "^6.6.2", "fluent-ffmpeg": "^2.1.3", "fs-extra": "^11.2.0", "m3u8-parser": "^7.2.0", "react": "^18.2.0", "react-dom": "^18.2.0", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/fluent-ffmpeg": "^2.1.27", "@types/fs-extra": "^11.0.4", "@types/jest": "^30.0.0", "@types/m3u8-parser": "^7.2.3", "@types/node": "^20.10.5", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "assert": "^2.1.0", "buffer": "^6.0.3", "concurrently": "^8.2.2", "constants-browserify": "^1.0.0", "crypto-browserify": "^3.12.1", "css-loader": "^6.8.1", "electron": "^28.1.0", "electron-builder": "^24.9.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "global": "^4.4.0", "html-webpack-plugin": "^5.6.0", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "os-browserify": "^0.3.0", "path-browserify": "^1.0.1", "prettier": "^3.1.1", "process": "^0.11.10", "sass": "^1.69.5", "sass-loader": "^13.3.2", "stream-browserify": "^3.0.0", "style-loader": "^3.3.3", "ts-jest": "^29.4.0", "ts-loader": "^9.5.1", "typescript": "^5.3.3", "url": "^0.11.4", "util": "^0.12.5", "webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "build": {"appId": "com.smartedu.downloader", "productName": "智慧教育下载器", "copyright": "Copyright © 2024 Smart Edu Downloader Team", "directories": {"output": "release", "buildResources": "build"}, "files": ["dist/**/*", "node_modules/**/*", "!node_modules/**/*.{md,txt,LICENSE}", "!node_modules/**/test/**", "!node_modules/**/__tests__/**"], "extraResources": [{"from": "resources", "to": "resources", "filter": ["**/*"]}], "mac": {"category": "public.app-category.education", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "icon": "build/icon.icns", "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist"}, "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64"]}], "icon": "build/icon.ico", "publisherName": "Smart Edu Downloader Team", "verifyUpdateCodeSignature": false}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}], "icon": "build/icon.png", "category": "Education", "maintainer": "Smart Edu Downloader Team <<EMAIL>>", "vendor": "Smart Edu Downloader Team"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "智慧教育下载器", "include": "build/installer.nsh", "artifactName": "${productName}-${version}-${arch}-setup.${ext}"}, "dmg": {"title": "${productName} ${version}", "icon": "build/icon.icns", "iconSize": 100, "contents": [{"x": 380, "y": 280, "type": "link", "path": "/Applications"}, {"x": 110, "y": 280, "type": "file"}], "window": {"width": 540, "height": 380}}, "publish": {"provider": "github", "owner": "smart-edu-downloader", "repo": "smart-edu-downloader"}}}