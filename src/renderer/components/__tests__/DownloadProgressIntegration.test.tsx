import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { notification } from 'antd';
import { DownloadManager } from '../DownloadManager';
import { DownloadTask, CourseResource } from '../../../shared/types';

// Mock antd notification
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  notification: {
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
  },
}));

describe('DownloadProgressIntegration', () => {
  const mockResource1: CourseResource = {
    id: 'resource-1',
    title: '数学教材第一册',
    type: 'textbook',
    url: 'https://example.com/math1.pdf',
    metadata: {
      stage: '小学',
      grade: '三年级',
      subject: '数学',
      version: '人教版',
      volume: '上册',
      fileSize: 2048000 // 2MB
    },
    requiresAuth: false,
    accessLevel: 'public'
  };

  const mockResource2: CourseResource = {
    id: 'resource-2',
    title: '语文教材第一册',
    type: 'textbook',
    url: 'https://example.com/chinese1.pdf',
    metadata: {
      stage: '小学',
      grade: '三年级',
      subject: '语文',
      version: '人教版',
      volume: '上册',
      fileSize: 3072000 // 3MB
    },
    requiresAuth: false,
    accessLevel: 'public'
  };

  const createMockTask = (resource: CourseResource, overrides: Partial<DownloadTask> = {}): DownloadTask => ({
    id: `task-${resource.id}`,
    resource,
    status: 'pending',
    progress: 0,
    speed: 0,
    estimatedTime: 0,
    requiresAuth: false,
    createdAt: new Date(),
    updatedAt: new Date(),
    outputPath: `/test/output/${resource.title}`,
    retryCount: 0,
    maxRetries: 3,
    ...overrides
  });

  const mockProps = {
    onPause: jest.fn(),
    onResume: jest.fn(),
    onCancel: jest.fn(),
    onRetry: jest.fn(),
    onClear: jest.fn(),
    onPauseAll: jest.fn(),
    onResumeAll: jest.fn(),
    onCancelAll: jest.fn(),
    onClearCompleted: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('完整下载流程监控', () => {
    it('应该监控从开始到完成的完整下载流程', async () => {
      const task1 = createMockTask(mockResource1, { status: 'pending' });
      const task2 = createMockTask(mockResource2, { status: 'pending' });
      
      const { rerender } = render(
        <DownloadManager 
          {...mockProps} 
          tasks={[task1, task2]} 
          enableNotifications={true}
          showOverallProgress={true}
        />
      );

      // 初始状态：显示2个待处理任务
      expect(screen.getByText('2')).toBeInTheDocument(); // 总任务
      expect(screen.getByText('0')).toBeInTheDocument(); // 进行中
      expect(screen.getByText(/任务完成/)).toBeInTheDocument();

      // 模拟第一个任务开始下载
      const downloadingTask1 = { ...task1, status: 'downloading' as const, progress: 25, speed: 1024000 };
      rerender(
        <DownloadManager 
          {...mockProps} 
          tasks={[downloadingTask1, task2]} 
          enableNotifications={true}
          showOverallProgress={true}
        />
      );

      // 验证下载中状态
      expect(screen.getByText('1')).toBeInTheDocument(); // 进行中任务
      expect(screen.getByText(/MB\/s/)).toBeInTheDocument(); // 平均速度

      // 模拟第二个任务也开始下载
      const downloadingTask2 = { ...task2, status: 'downloading' as const, progress: 50, speed: 2048000 };
      rerender(
        <DownloadManager 
          {...mockProps} 
          tasks={[downloadingTask1, downloadingTask2]} 
          enableNotifications={true}
          showOverallProgress={true}
        />
      );

      // 验证两个任务都在下载
      expect(screen.getByText('2')).toBeInTheDocument(); // 进行中任务
      expect(screen.getByText(/MB\/s/)).toBeInTheDocument(); // 平均速度

      // 模拟第一个任务完成
      const completedTask1 = { ...downloadingTask1, status: 'completed' as const, progress: 100, speed: 0 };
      rerender(
        <DownloadManager 
          {...mockProps} 
          tasks={[completedTask1, downloadingTask2]} 
          enableNotifications={true}
          showOverallProgress={true}
        />
      );

      // 验证完成通知
      await waitFor(() => {
        expect(notification.success).toHaveBeenCalledWith({
          message: '下载完成',
          description: '数学教材第一册 已成功下载',
          duration: 4.5,
          placement: 'topRight'
        });
      });

      // 验证统计更新
      expect(screen.getByText('1')).toBeInTheDocument(); // 已完成任务
      expect(screen.getByText(/任务完成/)).toBeInTheDocument();

      // 模拟第二个任务也完成
      const completedTask2 = { ...downloadingTask2, status: 'completed' as const, progress: 100, speed: 0 };
      rerender(
        <DownloadManager 
          {...mockProps} 
          tasks={[completedTask1, completedTask2]} 
          enableNotifications={true}
          showOverallProgress={true}
        />
      );

      // 验证全部完成通知
      await waitFor(() => {
        expect(notification.success).toHaveBeenCalledWith({
          message: '全部下载完成！',
          description: '所有 2 个任务已成功下载完成',
          duration: 6,
          placement: 'topRight'
        });
      });

      // 验证最终统计
      expect(screen.getByText('2')).toBeInTheDocument(); // 已完成任务
      expect(screen.getByText('0')).toBeInTheDocument(); // 进行中任务
      expect(screen.getByText(/任务完成/)).toBeInTheDocument();
      expect(screen.getByText('本次会话统计')).toBeInTheDocument();
    });

    it('应该正确处理下载失败和重试流程', async () => {
      const task = createMockTask(mockResource1, { status: 'downloading', progress: 30 });
      
      const { rerender } = render(
        <DownloadManager 
          {...mockProps} 
          tasks={[task]} 
          enableNotifications={true}
        />
      );

      // 模拟下载失败
      const failedTask = { 
        ...task, 
        status: 'failed' as const, 
        error: '网络连接超时',
        retryCount: 1
      };
      rerender(
        <DownloadManager 
          {...mockProps} 
          tasks={[failedTask]} 
          enableNotifications={true}
        />
      );

      // 验证失败状态显示
      expect(screen.getByText('1')).toBeInTheDocument(); // 失败任务
      expect(screen.getByText(/网络连接超时/)).toBeInTheDocument();

      // 点击重试按钮
      fireEvent.click(screen.getByLabelText('重试下载'));
      expect(mockProps.onRetry).toHaveBeenCalledWith(failedTask.id);

      // 模拟重试后成功
      const retriedTask = { 
        ...failedTask, 
        status: 'downloading' as const, 
        error: undefined,
        retryCount: 0,
        progress: 0
      };
      rerender(
        <DownloadManager 
          {...mockProps} 
          tasks={[retriedTask]} 
          enableNotifications={true}
        />
      );

      // 验证重试后状态
      expect(screen.getByText('0')).toBeInTheDocument(); // 失败任务清零
      expect(screen.getByText('1')).toBeInTheDocument(); // 进行中任务
    });

    it('应该正确处理暂停和恢复流程', async () => {
      const downloadingTask = createMockTask(mockResource1, { 
        status: 'downloading', 
        progress: 60,
        speed: 1024000
      });
      
      const { rerender } = render(
        <DownloadManager 
          {...mockProps} 
          tasks={[downloadingTask]} 
          enableNotifications={true}
        />
      );

      // 验证下载中状态
      expect(screen.getByText('下载中')).toBeInTheDocument();
      expect(screen.getByText(/MB\/s/)).toBeInTheDocument();

      // 点击暂停按钮
      fireEvent.click(screen.getByLabelText('暂停下载'));
      expect(mockProps.onPause).toHaveBeenCalledWith(downloadingTask.id);

      // 模拟暂停状态
      const pausedTask = { ...downloadingTask, status: 'paused' as const, speed: 0 };
      rerender(
        <DownloadManager 
          {...mockProps} 
          tasks={[pausedTask]} 
          enableNotifications={true}
        />
      );

      // 验证暂停状态
      expect(screen.getByText('已暂停')).toBeInTheDocument();
      expect(screen.queryByText(/MB\/s/)).not.toBeInTheDocument();

      // 点击恢复按钮
      fireEvent.click(screen.getByLabelText('继续下载'));
      expect(mockProps.onResume).toHaveBeenCalledWith(pausedTask.id);

      // 模拟恢复下载
      const resumedTask = { ...pausedTask, status: 'downloading' as const, speed: 1024000 };
      rerender(
        <DownloadManager 
          {...mockProps} 
          tasks={[resumedTask]} 
          enableNotifications={true}
        />
      );

      // 验证恢复状态
      expect(screen.getByText('下载中')).toBeInTheDocument();
      expect(screen.getByText(/MB\/s/)).toBeInTheDocument();
    });
  });

  describe('批量操作流程', () => {
    it('应该正确处理批量暂停和恢复', async () => {
      const task1 = createMockTask(mockResource1, { status: 'downloading', progress: 30 });
      const task2 = createMockTask(mockResource2, { status: 'downloading', progress: 50 });
      
      const { rerender } = render(
        <DownloadManager 
          {...mockProps} 
          tasks={[task1, task2]} 
          enableNotifications={true}
        />
      );

      // 验证两个任务都在下载
      expect(screen.getByText('2')).toBeInTheDocument(); // 进行中任务

      // 点击暂停全部
      fireEvent.click(screen.getByText('暂停全部'));
      expect(mockProps.onPauseAll).toHaveBeenCalled();

      // 模拟全部暂停
      const pausedTask1 = { ...task1, status: 'paused' as const };
      const pausedTask2 = { ...task2, status: 'paused' as const };
      rerender(
        <DownloadManager 
          {...mockProps} 
          tasks={[pausedTask1, pausedTask2]} 
          enableNotifications={true}
        />
      );

      // 验证全部暂停
      expect(screen.getByText('0')).toBeInTheDocument(); // 进行中任务
      expect(screen.getByText('2')).toBeInTheDocument(); // 暂停任务

      // 点击恢复全部
      fireEvent.click(screen.getByText('恢复全部'));
      expect(mockProps.onResumeAll).toHaveBeenCalled();
    });

    it('应该正确处理批量取消', async () => {
      const task1 = createMockTask(mockResource1, { status: 'downloading' });
      const task2 = createMockTask(mockResource2, { status: 'pending' });
      
      render(
        <DownloadManager 
          {...mockProps} 
          tasks={[task1, task2]} 
          enableNotifications={true}
        />
      );

      // 点击取消全部
      fireEvent.click(screen.getByText('取消全部'));
      expect(mockProps.onCancelAll).toHaveBeenCalled();
    });

    it('应该正确处理清除已完成任务', async () => {
      const completedTask1 = createMockTask(mockResource1, { status: 'completed' });
      const completedTask2 = createMockTask(mockResource2, { status: 'completed' });
      const downloadingTask = createMockTask(mockResource1, { 
        id: 'task-3', 
        status: 'downloading' 
      });
      
      render(
        <DownloadManager 
          {...mockProps} 
          tasks={[completedTask1, completedTask2, downloadingTask]} 
          enableNotifications={true}
        />
      );

      // 验证有已完成任务
      expect(screen.getByText('2')).toBeInTheDocument(); // 已完成任务

      // 点击清除已完成
      fireEvent.click(screen.getByText('清除已完成'));
      expect(mockProps.onClearCompleted).toHaveBeenCalled();
    });
  });

  describe('实时统计更新', () => {
    it('应该实时更新下载统计信息', async () => {
      const task1 = createMockTask(mockResource1, { 
        status: 'downloading', 
        progress: 25,
        speed: 1024000
      });
      const task2 = createMockTask(mockResource2, { 
        status: 'downloading', 
        progress: 75,
        speed: 2048000
      });
      
      const { rerender } = render(
        <DownloadManager 
          {...mockProps} 
          tasks={[task1, task2]} 
          enableNotifications={true}
          showOverallProgress={true}
        />
      );

      // 验证初始统计
      expect(screen.getByText('2')).toBeInTheDocument(); // 总任务
      expect(screen.getByText('2')).toBeInTheDocument(); // 进行中任务
      expect(screen.getByText(/MB\/s/)).toBeInTheDocument(); // 平均速度

      // 模拟进度更新
      const updatedTask1 = { ...task1, progress: 50, speed: 1536000 };
      const updatedTask2 = { ...task2, progress: 90, speed: 2560000 };
      rerender(
        <DownloadManager 
          {...mockProps} 
          tasks={[updatedTask1, updatedTask2]} 
          enableNotifications={true}
          showOverallProgress={true}
        />
      );

      // 验证统计更新
      expect(screen.getByText(/MB\/s/)).toBeInTheDocument(); // 更新的平均速度
      
      // 总体进度应该是 (50 + 90) / 2 = 70%
      const progressBar = document.querySelector('.ant-progress-bg');
      expect(progressBar).toHaveStyle('width: 70%');
    });

    it('应该正确计算和显示已下载大小', async () => {
      const stats = {
        totalTasks: 2,
        completedTasks: 1,
        failedTasks: 0,
        activeTasks: 1,
        totalDownloadedBytes: 2048000, // 2MB
        averageSpeed: 1024000
      };
      
      render(
        <DownloadManager 
          {...mockProps} 
          tasks={[]} 
          stats={stats}
          showOverallProgress={true}
        />
      );

      expect(screen.getByText(/已下载:/)).toBeInTheDocument();
    });
  });

  describe('紧凑模式切换', () => {
    it('应该在紧凑模式和完整模式之间切换', async () => {
      const task = createMockTask(mockResource1, { status: 'downloading', progress: 50 });
      
      render(
        <DownloadManager 
          {...mockProps} 
          tasks={[task]} 
          enableNotifications={true}
        />
      );

      // 默认应该显示元数据标签（完整模式）
      expect(screen.getByText('小学')).toBeInTheDocument();
      expect(screen.getByText('数学')).toBeInTheDocument();

      // 切换到紧凑模式
      const compactSwitch = screen.getByRole('switch');
      fireEvent.click(compactSwitch);

      // 紧凑模式下不应该显示元数据标签
      await waitFor(() => {
        expect(screen.queryByText('小学')).not.toBeInTheDocument();
        expect(screen.queryByText('数学')).not.toBeInTheDocument();
      });
    });
  });

  describe('标签页过滤', () => {
    it('应该正确过滤不同状态的任务', async () => {
      const downloadingTask = createMockTask(mockResource1, { status: 'downloading' });
      const completedTask = createMockTask(mockResource2, { status: 'completed' });
      const failedTask = createMockTask(mockResource1, { 
        id: 'task-3',
        status: 'failed',
        resource: { ...mockResource1, id: 'resource-3', title: '英语教材' }
      });
      
      render(
        <DownloadManager 
          {...mockProps} 
          tasks={[downloadingTask, completedTask, failedTask]} 
          enableNotifications={true}
        />
      );

      // 默认显示全部任务
      expect(screen.getByText('数学教材第一册')).toBeInTheDocument();
      expect(screen.getByText('语文教材第一册')).toBeInTheDocument();
      expect(screen.getByText('英语教材')).toBeInTheDocument();

      // 切换到下载中标签页
      fireEvent.click(screen.getByText(/下载中/));
      expect(screen.getByText('数学教材第一册')).toBeInTheDocument();
      expect(screen.queryByText('语文教材第一册')).not.toBeInTheDocument();
      expect(screen.queryByText('英语教材')).not.toBeInTheDocument();

      // 切换到已完成标签页
      fireEvent.click(screen.getByText(/已完成/));
      expect(screen.queryByText('数学教材第一册')).not.toBeInTheDocument();
      expect(screen.getByText('语文教材第一册')).toBeInTheDocument();
      expect(screen.queryByText('英语教材')).not.toBeInTheDocument();

      // 切换到失败标签页
      fireEvent.click(screen.getByText(/失败/));
      expect(screen.queryByText('数学教材第一册')).not.toBeInTheDocument();
      expect(screen.queryByText('语文教材第一册')).not.toBeInTheDocument();
      expect(screen.getByText('英语教材')).toBeInTheDocument();
    });
  });
});