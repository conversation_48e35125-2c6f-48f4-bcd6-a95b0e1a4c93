import * as fs from 'fs-extra';
import * as path from 'path';
import { EventEmitter } from 'events';
import { CourseResource, M3U8Segment } from '../types';

/**
 * 断点续传状态信息
 */
export interface ResumeState {
  taskId: string;
  resource: CourseResource;
  outputPath: string;
  tempDir: string;
  totalSegments: number;
  downloadedSegments: SegmentState[];
  failedSegments: number[];
  lastUpdateTime: Date;
  playlistUrl: string;
  baseUrl: string;
}

/**
 * 片段下载状态
 */
export interface SegmentState {
  index: number;
  url: string;
  filePath: string;
  size: number;
  checksum?: string;
  downloadedAt: Date;
  isComplete: boolean;
}

/**
 * 断点续传配置
 */
export interface ResumeConfig {
  stateDir: string;
  enableChecksum: boolean;
  maxStateAge: number; // 状态文件最大保存时间（毫秒）
  autoCleanup: boolean;
  compressionEnabled: boolean;
}

/**
 * 断点续传状态管理器
 * 负责保存和恢复视频下载的断点续传状态
 */
export class ResumeStateManager extends EventEmitter {
  private config: ResumeConfig;
  private activeStates: Map<string, ResumeState> = new Map();

  private readonly defaultConfig: ResumeConfig = {
    stateDir: path.join(process.cwd(), '.download-states'),
    enableChecksum: true,
    maxStateAge: 7 * 24 * 60 * 60 * 1000, // 7天
    autoCleanup: true,
    compressionEnabled: false
  };

  constructor(config?: Partial<ResumeConfig>) {
    super();
    this.config = { ...this.defaultConfig, ...config };
    this.initializeStateDir();
    
    if (this.config.autoCleanup) {
      this.scheduleCleanup();
    }
  }

  /**
   * 初始化状态目录
   */
  private async initializeStateDir(): Promise<void> {
    try {
      await fs.ensureDir(this.config.stateDir);
    } catch (error) {
      this.emit('error', new Error(`无法创建状态目录: ${error}`));
    }
  }

  /**
   * 保存断点续传状态
   */
  async saveState(state: ResumeState): Promise<void> {
    try {
      const stateFilePath = this.getStateFilePath(state.taskId);
      const stateData = {
        ...state,
        lastUpdateTime: new Date()
      };

      await fs.writeJson(stateFilePath, stateData, { spaces: 2 });
      this.activeStates.set(state.taskId, stateData);
      
      this.emit('state-saved', state.taskId, stateData);
    } catch (error) {
      this.emit('error', new Error(`保存状态失败: ${error}`));
      throw error;
    }
  }

  /**
   * 加载断点续传状态
   */
  async loadState(taskId: string): Promise<ResumeState | null> {
    try {
      const stateFilePath = this.getStateFilePath(taskId);
      
      if (!await fs.pathExists(stateFilePath)) {
        return null;
      }

      const stateData = await fs.readJson(stateFilePath);
      
      // 检查状态文件是否过期
      const lastUpdateTime = new Date(stateData.lastUpdateTime);
      const now = new Date();
      if (now.getTime() - lastUpdateTime.getTime() > this.config.maxStateAge) {
        await this.removeState(taskId);
        return null;
      }

      // 验证状态数据完整性
      if (!this.validateStateData(stateData)) {
        await this.removeState(taskId);
        return null;
      }

      this.activeStates.set(taskId, stateData);
      this.emit('state-loaded', taskId, stateData);
      
      return stateData;
    } catch (error) {
      this.emit('error', new Error(`加载状态失败: ${error}`));
      return null;
    }
  }

  /**
   * 更新片段下载状态
   */
  async updateSegmentState(
    taskId: string, 
    segmentIndex: number, 
    segmentState: Partial<SegmentState>
  ): Promise<void> {
    const state = this.activeStates.get(taskId);
    if (!state) {
      throw new Error(`未找到任务状态: ${taskId}`);
    }

    // 查找或创建片段状态
    let existingSegment = state.downloadedSegments.find(s => s.index === segmentIndex);
    if (existingSegment) {
      Object.assign(existingSegment, segmentState);
    } else {
      const newSegment: SegmentState = {
        index: segmentIndex,
        url: segmentState.url || '',
        filePath: segmentState.filePath || '',
        size: segmentState.size || 0,
        checksum: segmentState.checksum,
        downloadedAt: segmentState.downloadedAt || new Date(),
        isComplete: segmentState.isComplete || false
      };
      state.downloadedSegments.push(newSegment);
    }

    // 保存更新后的状态
    await this.saveState(state);
    this.emit('segment-updated', taskId, segmentIndex, segmentState);
  }

  /**
   * 获取已下载的片段列表
   */
  getDownloadedSegments(taskId: string): SegmentState[] {
    const state = this.activeStates.get(taskId);
    return state ? state.downloadedSegments.filter(s => s.isComplete) : [];
  }

  /**
   * 获取失败的片段列表
   */
  getFailedSegments(taskId: string): number[] {
    const state = this.activeStates.get(taskId);
    return state ? state.failedSegments : [];
  }

  /**
   * 标记片段为失败
   */
  async markSegmentFailed(taskId: string, segmentIndex: number): Promise<void> {
    const state = this.activeStates.get(taskId);
    if (!state) {
      throw new Error(`未找到任务状态: ${taskId}`);
    }

    if (!state.failedSegments.includes(segmentIndex)) {
      state.failedSegments.push(segmentIndex);
      await this.saveState(state);
      this.emit('segment-failed', taskId, segmentIndex);
    }
  }

  /**
   * 检查任务是否可以续传
   */
  async canResume(taskId: string): Promise<boolean> {
    const state = await this.loadState(taskId);
    if (!state) {
      return false;
    }

    // 检查临时目录是否存在
    if (!await fs.pathExists(state.tempDir)) {
      return false;
    }

    // 检查已下载的片段文件是否存在
    const downloadedSegments = state.downloadedSegments.filter(s => s.isComplete);
    for (const segment of downloadedSegments) {
      if (!await fs.pathExists(segment.filePath)) {
        return false;
      }
    }

    return downloadedSegments.length > 0;
  }

  /**
   * 计算下载进度
   */
  calculateProgress(taskId: string): number {
    const state = this.activeStates.get(taskId);
    if (!state || state.totalSegments === 0) {
      return 0;
    }

    const completedSegments = state.downloadedSegments.filter(s => s.isComplete).length;
    return Math.round((completedSegments / state.totalSegments) * 100);
  }

  /**
   * 移除断点续传状态
   */
  async removeState(taskId: string): Promise<void> {
    try {
      const stateFilePath = this.getStateFilePath(taskId);
      
      if (await fs.pathExists(stateFilePath)) {
        await fs.remove(stateFilePath);
      }

      this.activeStates.delete(taskId);
      this.emit('state-removed', taskId);
    } catch (error) {
      this.emit('error', new Error(`移除状态失败: ${error}`));
    }
  }

  /**
   * 清理过期的状态文件
   */
  async cleanupExpiredStates(): Promise<void> {
    try {
      const stateFiles = await fs.readdir(this.config.stateDir);
      const now = new Date();

      for (const fileName of stateFiles) {
        if (!fileName.endsWith('.json')) {
          continue;
        }

        const filePath = path.join(this.config.stateDir, fileName);
        const stats = await fs.stat(filePath);
        
        if (now.getTime() - stats.mtime.getTime() > this.config.maxStateAge) {
          await fs.remove(filePath);
          this.emit('state-expired', fileName);
        }
      }
    } catch (error) {
      this.emit('error', new Error(`清理过期状态失败: ${error}`));
    }
  }

  /**
   * 获取状态文件路径
   */
  private getStateFilePath(taskId: string): string {
    return path.join(this.config.stateDir, `${taskId}.json`);
  }

  /**
   * 验证状态数据完整性
   */
  private validateStateData(data: any): boolean {
    return (
      data &&
      typeof data.taskId === 'string' &&
      typeof data.totalSegments === 'number' &&
      Array.isArray(data.downloadedSegments) &&
      Array.isArray(data.failedSegments) &&
      data.resource &&
      data.outputPath &&
      data.tempDir
    );
  }

  /**
   * 定期清理过期状态
   */
  private scheduleCleanup(): void {
    // 每小时清理一次过期状态
    setInterval(() => {
      this.cleanupExpiredStates();
    }, 60 * 60 * 1000);
  }

  /**
   * 获取所有活跃状态
   */
  getAllActiveStates(): Map<string, ResumeState> {
    return new Map(this.activeStates);
  }

  /**
   * 生成片段校验和
   */
  private async generateChecksum(filePath: string): Promise<string> {
    if (!this.config.enableChecksum) {
      return '';
    }

    try {
      const crypto = require('crypto');
      const fileBuffer = await fs.readFile(filePath);
      return crypto.createHash('md5').update(fileBuffer).digest('hex');
    } catch (error) {
      return '';
    }
  }

  /**
   * 验证片段文件完整性
   */
  async validateSegmentFile(segmentState: SegmentState): Promise<boolean> {
    try {
      if (!await fs.pathExists(segmentState.filePath)) {
        return false;
      }

      const stats = await fs.stat(segmentState.filePath);
      if (stats.size !== segmentState.size) {
        return false;
      }

      if (this.config.enableChecksum && segmentState.checksum) {
        const currentChecksum = await this.generateChecksum(segmentState.filePath);
        return currentChecksum === segmentState.checksum;
      }

      return true;
    } catch (error) {
      return false;
    }
  }
}

export default ResumeStateManager;
