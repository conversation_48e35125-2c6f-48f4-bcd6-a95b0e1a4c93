/**
 * 数据模型和验证功能演示
 * Demonstration of data models and validation functionality
 */

import {
  CourseFilters,
  LoginCredentials,
  CourseResource,
  DownloadTask,
  M3U8Playlist,
  AppConfig,
  SmartEduError,
  NetworkError,
  ValidationError,
  ErrorType
} from '../types/index';

import {
  validateCourseFilters,
  validateLoginCredentials,
  validateCourseResource,
  validateM3U8Playlist,
  validateAppConfig,
  validateFilePath,
  validateProgress,
  validateSpeed,
  isValidString,
  isValidNumber,
  isValidUrl
} from '../utils/validation';

// ============================================================================
// 类型定义演示 (Type Definition Examples)
// ============================================================================

// 课程筛选条件示例
const sampleFilters: CourseFilters = {
  stage: '小学',
  grade: '一年级',
  subject: '语文',
  version: '人教版',
  volume: '上册'
};

// 登录凭据示例
const sampleCredentials: LoginCredentials = {
  username: 'testuser',
  password: 'password123',
  captcha: '1234',
  captchaToken: 'abc123'
};

// 课程资源示例
const sampleResource: CourseResource = {
  id: 'resource_001',
  title: '第一课：认识汉字',
  type: 'video',
  url: 'https://basic.smartedu.cn/video/123',
  metadata: {
    stage: '小学',
    grade: '一年级',
    subject: '语文',
    version: '人教版',
    volume: '上册',
    chapter: '第一单元',
    lesson: '第一课',
    duration: 1800 // 30分钟
  },
  requiresAuth: false,
  accessLevel: 'public'
};

// M3U8播放列表示例
const samplePlaylist: M3U8Playlist = {
  baseUrl: 'https://video.smartedu.cn/segments/',
  segments: [
    { url: 'segment001.ts', duration: 10, sequence: 0 },
    { url: 'segment002.ts', duration: 10, sequence: 1 },
    { url: 'segment003.ts', duration: 8, sequence: 2 }
  ],
  totalDuration: 28,
  isEncrypted: false
};

// 应用配置示例
const sampleConfig: AppConfig = {
  downloadPath: '/Users/<USER>/Downloads/SmartEdu',
  maxConcurrentDownloads: 3,
  requestTimeout: 30000,
  retryAttempts: 3,
  userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
  fileOrganization: {
    basePath: '/Users/<USER>/Downloads/SmartEdu',
    namingPattern: '{subject}/{grade}/{title}',
    createSubfolders: true,
    groupBySubject: true,
    groupByGrade: true
  }
};

// ============================================================================
// 错误处理演示 (Error Handling Examples)
// ============================================================================

// 创建自定义错误
function demonstrateErrorHandling() {
  try {
    // 网络错误示例
    throw new NetworkError('连接超时', {
      code: 'TIMEOUT',
      statusCode: 408,
      context: { url: 'https://basic.smartedu.cn/api/resources' }
    });
  } catch (error) {
    if (error instanceof SmartEduError) {
      console.log('错误类型:', error.type);
      console.log('是否可重试:', error.isRetryable);
      console.log('错误详情:', error.details);
    }
  }

  try {
    // 验证错误示例
    throw new ValidationError('用户名不能为空', {
      context: { field: 'username', value: '' }
    });
  } catch (error) {
    if (error instanceof ValidationError) {
      console.log('验证失败:', error.message);
    }
  }
}

// ============================================================================
// 验证功能演示 (Validation Function Examples)
// ============================================================================

function demonstrateValidation() {
  console.log('=== 基础验证函数演示 ===');
  
  // 字符串验证
  console.log('字符串验证:');
  console.log('  "hello" 是否有效:', isValidString('hello'));
  console.log('  空字符串是否有效:', isValidString(''));
  console.log('  null 是否有效:', isValidString(null));

  // 数字验证
  console.log('数字验证:');
  console.log('  42 是否有效:', isValidNumber(42));
  console.log('  -1 在0-100范围内是否有效:', isValidNumber(-1, 0, 100));
  console.log('  50 在0-100范围内是否有效:', isValidNumber(50, 0, 100));

  // URL验证
  console.log('URL验证:');
  console.log('  "https://example.com" 是否有效:', isValidUrl('https://example.com'));
  console.log('  "not-a-url" 是否有效:', isValidUrl('not-a-url'));

  console.log('\n=== 复杂对象验证演示 ===');

  try {
    // 验证课程筛选条件
    const validatedFilters = validateCourseFilters(sampleFilters);
    console.log('筛选条件验证成功:', validatedFilters);
  } catch (error) {
    console.error('筛选条件验证失败:', error);
  }

  try {
    // 验证登录凭据
    const validatedCredentials = validateLoginCredentials(sampleCredentials);
    console.log('登录凭据验证成功:', validatedCredentials);
  } catch (error) {
    console.error('登录凭据验证失败:', error);
  }

  try {
    // 验证课程资源
    const validatedResource = validateCourseResource(sampleResource);
    console.log('课程资源验证成功:', validatedResource.title);
  } catch (error) {
    console.error('课程资源验证失败:', error);
  }

  try {
    // 验证M3U8播放列表
    const validatedPlaylist = validateM3U8Playlist(samplePlaylist);
    console.log('M3U8播放列表验证成功，片段数量:', validatedPlaylist.segments.length);
  } catch (error) {
    console.error('M3U8播放列表验证失败:', error);
  }

  try {
    // 验证应用配置
    const validatedConfig = validateAppConfig(sampleConfig);
    console.log('应用配置验证成功，下载路径:', validatedConfig.downloadPath);
  } catch (error) {
    console.error('应用配置验证失败:', error);
  }

  console.log('\n=== 文件和进度验证演示 ===');

  try {
    // 文件路径验证
    const validPath = validateFilePath('/path/to/file.txt');
    console.log('文件路径验证成功:', validPath);
  } catch (error) {
    console.error('文件路径验证失败:', error);
  }

  try {
    // 进度验证
    const validProgress = validateProgress(75.5);
    console.log('进度验证成功:', validProgress + '%');
  } catch (error) {
    console.error('进度验证失败:', error);
  }

  try {
    // 速度验证
    const validSpeed = validateSpeed(1024.567);
    console.log('速度验证成功:', validSpeed + ' bytes/s');
  } catch (error) {
    console.error('速度验证失败:', error);
  }
}

// ============================================================================
// 导出演示函数 (Export Demo Functions)
// ============================================================================

export {
  sampleFilters,
  sampleCredentials,
  sampleResource,
  samplePlaylist,
  sampleConfig,
  demonstrateErrorHandling,
  demonstrateValidation
};

// 如果直接运行此文件，执行演示
if (require.main === module) {
  console.log('智慧教育下载器 - 数据模型和验证功能演示');
  console.log('='.repeat(50));
  
  demonstrateValidation();
  
  console.log('\n=== 错误处理演示 ===');
  demonstrateErrorHandling();
  
  console.log('\n演示完成！');
}