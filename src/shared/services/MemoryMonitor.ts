import { EventEmitter } from 'events';
import * as os from 'os';

export interface MemoryInfo {
  totalMemory: number;
  freeMemory: number;
  usedMemory: number;
  usagePercentage: number;
  processMemory: NodeJS.MemoryUsage;
}

export interface MemoryWarning {
  level: 'warning' | 'critical';
  message: string;
  currentUsage: number;
  processMemory: NodeJS.MemoryUsage;
}

export interface MemoryOptimizationSuggestion {
  action: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
}

export class MemoryMonitor extends EventEmitter {
  private monitorInterval: NodeJS.Timeout | null = null;
  private readonly warningThreshold = 0.8; // 80%内存使用率警告
  private readonly criticalThreshold = 0.9; // 90%内存使用率严重警告
  private readonly processWarningThreshold = 500 * 1024 * 1024; // 500MB进程内存警告
  private memoryHistory: MemoryInfo[] = [];
  private readonly maxHistorySize = 100;

  constructor(private checkIntervalMs: number = 10000) {
    super();
  }

  /**
   * 获取当前内存信息
   */
  getMemoryInfo(): MemoryInfo {
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const usagePercentage = usedMemory / totalMemory;
    const processMemory = process.memoryUsage();

    return {
      totalMemory,
      freeMemory,
      usedMemory,
      usagePercentage,
      processMemory
    };
  }

  /**
   * 检查内存使用情况并发出警告
   */
  checkMemoryUsage(): MemoryWarning | null {
    const memoryInfo = this.getMemoryInfo();

    // 检查进程内存使用
    if (memoryInfo.processMemory.heapUsed > this.processWarningThreshold) {
      return {
        level: 'warning',
        message: `应用程序内存使用过高: ${this.formatBytes(memoryInfo.processMemory.heapUsed)}`,
        currentUsage: memoryInfo.usagePercentage,
        processMemory: memoryInfo.processMemory
      };
    }

    // 检查系统内存使用
    if (memoryInfo.usagePercentage >= this.criticalThreshold) {
      return {
        level: 'critical',
        message: `系统内存严重不足: ${(memoryInfo.usagePercentage * 100).toFixed(1)}% 已使用`,
        currentUsage: memoryInfo.usagePercentage,
        processMemory: memoryInfo.processMemory
      };
    }

    if (memoryInfo.usagePercentage >= this.warningThreshold) {
      return {
        level: 'warning',
        message: `系统内存使用率较高: ${(memoryInfo.usagePercentage * 100).toFixed(1)}% 已使用`,
        currentUsage: memoryInfo.usagePercentage,
        processMemory: memoryInfo.processMemory
      };
    }

    return null;
  }

  /**
   * 开始监控内存使用
   */
  startMonitoring(): void {
    if (this.monitorInterval) {
      this.stopMonitoring();
    }

    this.monitorInterval = setInterval(() => {
      try {
        const memoryInfo = this.getMemoryInfo();
        
        // 更新历史记录
        this.memoryHistory.push(memoryInfo);
        if (this.memoryHistory.length > this.maxHistorySize) {
          this.memoryHistory.shift();
        }

        // 检查警告
        const warning = this.checkMemoryUsage();
        if (warning) {
          this.emit('memoryWarning', warning);
        }

        this.emit('memoryUpdate', memoryInfo);

        // 检查内存泄漏
        this.checkMemoryLeak();
      } catch (error) {
        this.emit('error', error);
      }
    }, this.checkIntervalMs);
  }

  /**
   * 停止监控内存使用
   */
  stopMonitoring(): void {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
  }

  /**
   * 执行垃圾回收（如果可用）
   */
  forceGarbageCollection(): boolean {
    if (global.gc) {
      global.gc();
      return true;
    }
    return false;
  }

  /**
   * 获取内存优化建议
   */
  getOptimizationSuggestions(): MemoryOptimizationSuggestion[] {
    const suggestions: MemoryOptimizationSuggestion[] = [];
    const currentMemory = this.getMemoryInfo();

    if (currentMemory.usagePercentage > 0.8) {
      suggestions.push({
        action: 'reduce_concurrent_downloads',
        description: '减少并发下载数量以降低内存使用',
        priority: 'high'
      });
    }

    if (currentMemory.processMemory.heapUsed > this.processWarningThreshold) {
      suggestions.push({
        action: 'force_garbage_collection',
        description: '执行垃圾回收以释放内存',
        priority: 'medium'
      });
    }

    if (this.detectMemoryLeak()) {
      suggestions.push({
        action: 'restart_application',
        description: '检测到可能的内存泄漏，建议重启应用',
        priority: 'high'
      });
    }

    return suggestions;
  }

  /**
   * 检查内存泄漏
   */
  private checkMemoryLeak(): void {
    if (this.detectMemoryLeak()) {
      this.emit('memoryLeak', {
        message: '检测到可能的内存泄漏',
        history: this.memoryHistory.slice(-10)
      });
    }
  }

  /**
   * 检测内存泄漏
   */
  private detectMemoryLeak(): boolean {
    if (this.memoryHistory.length < 10) {
      return false;
    }

    const recent = this.memoryHistory.slice(-10);
    const trend = this.calculateMemoryTrend(recent);
    
    // 如果内存持续增长且增长率超过阈值，认为可能存在内存泄漏
    return trend > 0.1; // 10%的增长率
  }

  /**
   * 计算内存使用趋势
   */
  private calculateMemoryTrend(history: MemoryInfo[]): number {
    if (history.length < 2) {
      return 0;
    }

    const first = history[0].processMemory.heapUsed;
    const last = history[history.length - 1].processMemory.heapUsed;
    
    return (last - first) / first;
  }

  /**
   * 格式化字节数为可读格式
   */
  private formatBytes(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * 获取内存历史记录
   */
  getMemoryHistory(): MemoryInfo[] {
    return [...this.memoryHistory];
  }

  /**
   * 清理内存历史记录
   */
  clearHistory(): void {
    this.memoryHistory = [];
  }

  /**
   * 获取内存统计信息
   */
  getMemoryStats(): {
    averageUsage: number;
    peakUsage: number;
    currentUsage: number;
  } {
    if (this.memoryHistory.length === 0) {
      const current = this.getMemoryInfo();
      return {
        averageUsage: current.usagePercentage,
        peakUsage: current.usagePercentage,
        currentUsage: current.usagePercentage
      };
    }

    const usages = this.memoryHistory.map(info => info.usagePercentage);
    const averageUsage = usages.reduce((sum, usage) => sum + usage, 0) / usages.length;
    const peakUsage = Math.max(...usages);
    const currentUsage = usages[usages.length - 1];

    return {
      averageUsage,
      peakUsage,
      currentUsage
    };
  }
}