import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { notification } from 'antd';
import { DownloadManager } from '../DownloadManager';
import { DownloadTask, CourseResource } from '../../../shared/types';

// Mock antd notification
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  notification: {
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
  },
}));

describe('DownloadManager', () => {
  const mockResource: CourseResource = {
    id: 'resource-1',
    title: '测试教材',
    type: 'textbook',
    url: 'https://example.com/textbook.pdf',
    metadata: {
      stage: '小学',
      grade: '三年级',
      subject: '数学',
      version: '人教版',
      volume: '上册',
      fileSize: 1024000
    },
    requiresAuth: false,
    accessLevel: 'public'
  };

  const createMockTask = (overrides: Partial<DownloadTask> = {}): DownloadTask => ({
    id: 'task-1',
    resource: mockResource,
    status: 'pending',
    progress: 0,
    speed: 0,
    estimatedTime: 0,
    requiresAuth: false,
    createdAt: new Date(),
    updatedAt: new Date(),
    outputPath: '/test/output',
    retryCount: 0,
    maxRetries: 3,
    ...overrides
  });

  const mockProps = {
    tasks: [],
    onPause: jest.fn(),
    onResume: jest.fn(),
    onCancel: jest.fn(),
    onRetry: jest.fn(),
    onClear: jest.fn(),
    onPauseAll: jest.fn(),
    onResumeAll: jest.fn(),
    onCancelAll: jest.fn(),
    onClearCompleted: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('基本渲染', () => {
    it('应该渲染下载管理器标题', () => {
      render(<DownloadManager {...mockProps} />);
      expect(screen.getByText('下载管理器')).toBeInTheDocument();
    });

    it('应该显示空状态当没有任务时', () => {
      render(<DownloadManager {...mockProps} />);
      expect(screen.getByText('暂无下载任务')).toBeInTheDocument();
    });

    it('应该渲染统计卡片', () => {
      render(<DownloadManager {...mockProps} />);
      expect(screen.getByText('总任务')).toBeInTheDocument();
      expect(screen.getByText('进行中')).toBeInTheDocument();
      expect(screen.getAllByText('已完成')[0]).toBeInTheDocument(); // 使用第一个匹配的元素
      expect(screen.getByText('失败')).toBeInTheDocument();
    });
  });

  describe('任务列表显示', () => {
    it('应该显示任务列表', () => {
      const tasks = [createMockTask()];
      render(<DownloadManager {...mockProps} tasks={tasks} />);
      
      expect(screen.getByText('测试教材')).toBeInTheDocument();
    });

    it('应该按状态分组显示任务', () => {
      const tasks = [
        createMockTask({ id: 'task-1', status: 'downloading' }),
        createMockTask({ id: 'task-2', status: 'completed' }),
        createMockTask({ id: 'task-3', status: 'failed' })
      ];
      
      render(<DownloadManager {...mockProps} tasks={tasks} />);
      
      // 检查标签页
      expect(screen.getByText(/全部/)).toBeInTheDocument();
      expect(screen.getByText(/下载中/)).toBeInTheDocument();
      expect(screen.getByText(/已完成/)).toBeInTheDocument();
      expect(screen.getByText(/失败/)).toBeInTheDocument();
    });

    it('应该显示任务数量徽章', () => {
      const tasks = [
        createMockTask({ id: 'task-1', status: 'downloading' }),
        createMockTask({ id: 'task-2', status: 'downloading' })
      ];
      
      render(<DownloadManager {...mockProps} tasks={tasks} />);
      
      // 应该显示下载中任务的数量
      const downloadingTab = screen.getByText(/下载中/);
      expect(downloadingTab.parentElement).toHaveTextContent('2');
    });
  });

  describe('总体进度显示', () => {
    it('应该显示总体进度条', () => {
      const tasks = [
        createMockTask({ id: 'task-1', progress: 50 }),
        createMockTask({ id: 'task-2', progress: 100 })
      ];
      
      render(<DownloadManager {...mockProps} tasks={tasks} showOverallProgress={true} />);
      
      expect(screen.getByText('总体进度')).toBeInTheDocument();
      expect(screen.getByText(/任务完成/)).toBeInTheDocument();
    });

    it('应该计算正确的总体进度百分比', () => {
      const tasks = [
        createMockTask({ id: 'task-1', progress: 25 }),
        createMockTask({ id: 'task-2', progress: 75 })
      ];
      
      render(<DownloadManager {...mockProps} tasks={tasks} showOverallProgress={true} />);
      
      // 总体进度应该是 (25 + 75) / 2 = 50%
      const progressBar = document.querySelector('.ant-progress-bg');
      expect(progressBar).toHaveStyle('width: 50%');
    });

    it('应该显示平均速度和已下载大小', () => {
      const tasks = [
        createMockTask({ 
          id: 'task-1', 
          status: 'downloading',
          speed: 1024000, // 1MB/s
          progress: 50
        })
      ];
      
      const stats = {
        totalTasks: 1,
        completedTasks: 0,
        failedTasks: 0,
        activeTasks: 1,
        totalDownloadedBytes: 512000, // 512KB
        averageSpeed: 1024000
      };
      
      render(<DownloadManager {...mockProps} tasks={tasks} stats={stats} showOverallProgress={true} />);
      
      expect(screen.getByText(/平均速度:/)).toBeInTheDocument();
      expect(screen.getByText(/已下载:/)).toBeInTheDocument();
    });
  });

  describe('统计信息', () => {
    it('应该显示正确的统计数据', () => {
      const tasks = [
        createMockTask({ id: 'task-1', status: 'completed' }),
        createMockTask({ id: 'task-2', status: 'downloading' }),
        createMockTask({ id: 'task-3', status: 'failed' })
      ];
      
      render(<DownloadManager {...mockProps} tasks={tasks} />);
      
      // 检查统计卡片的值 - 使用文本内容而不是display value
      expect(screen.getByText('3')).toBeInTheDocument(); // 总任务
      expect(screen.getByText('1')).toBeInTheDocument(); // 进行中
      expect(screen.getByText('1')).toBeInTheDocument(); // 已完成
      expect(screen.getByText('1')).toBeInTheDocument(); // 失败
    });
  });

  describe('批量操作', () => {
    it('应该显示批量操作按钮', () => {
      const tasks = [createMockTask({ status: 'downloading' })];
      render(<DownloadManager {...mockProps} tasks={tasks} />);
      
      expect(screen.getByText('暂停全部')).toBeInTheDocument();
      expect(screen.getByText('恢复全部')).toBeInTheDocument();
      expect(screen.getByText('取消全部')).toBeInTheDocument();
      expect(screen.getByText('清除已完成')).toBeInTheDocument();
    });

    it('应该调用暂停全部回调', () => {
      const tasks = [createMockTask({ status: 'downloading' })];
      render(<DownloadManager {...mockProps} tasks={tasks} />);
      
      fireEvent.click(screen.getByText('暂停全部'));
      expect(mockProps.onPauseAll).toHaveBeenCalled();
    });

    it('应该调用恢复全部回调', () => {
      const tasks = [createMockTask({ status: 'paused' })];
      render(<DownloadManager {...mockProps} tasks={tasks} />);
      
      fireEvent.click(screen.getByText('恢复全部'));
      expect(mockProps.onResumeAll).toHaveBeenCalled();
    });

    it('应该调用取消全部回调', () => {
      const tasks = [createMockTask({ status: 'downloading' })];
      render(<DownloadManager {...mockProps} tasks={tasks} />);
      
      fireEvent.click(screen.getByText('取消全部'));
      expect(mockProps.onCancelAll).toHaveBeenCalled();
    });

    it('应该调用清除已完成回调', () => {
      const tasks = [createMockTask({ status: 'completed' })];
      render(<DownloadManager {...mockProps} tasks={tasks} />);
      
      fireEvent.click(screen.getByText('清除已完成'));
      expect(mockProps.onClearCompleted).toHaveBeenCalled();
    });
  });

  describe('紧凑模式', () => {
    it('应该切换紧凑模式', () => {
      const tasks = [createMockTask()];
      render(<DownloadManager {...mockProps} tasks={tasks} />);
      
      const compactSwitch = screen.getByRole('switch');
      fireEvent.click(compactSwitch);
      
      // 验证紧凑模式已启用（通过检查样式变化）
      expect(compactSwitch).toBeChecked();
    });
  });

  describe('通知功能', () => {
    it('应该在任务完成时显示通知', async () => {
      const { rerender } = render(
        <DownloadManager {...mockProps} tasks={[]} enableNotifications={true} />
      );
      
      // 添加一个完成的任务
      const completedTask = createMockTask({ status: 'completed' });
      rerender(
        <DownloadManager {...mockProps} tasks={[completedTask]} enableNotifications={true} />
      );
      
      await waitFor(() => {
        expect(notification.success).toHaveBeenCalledWith({
          message: '下载完成',
          description: '测试教材 已成功下载',
          duration: 4.5,
          placement: 'topRight'
        });
      });
    });

    it('应该在多个任务完成时显示批量通知', async () => {
      const { rerender } = render(
        <DownloadManager {...mockProps} tasks={[]} enableNotifications={true} />
      );
      
      // 添加多个完成的任务
      const completedTasks = [
        createMockTask({ id: 'task-1', status: 'completed' }),
        createMockTask({ id: 'task-2', status: 'completed' })
      ];
      rerender(
        <DownloadManager {...mockProps} tasks={completedTasks} enableNotifications={true} />
      );
      
      await waitFor(() => {
        expect(notification.success).toHaveBeenCalledWith({
          message: '批量下载完成',
          description: '2 个任务已成功下载',
          duration: 4.5,
          placement: 'topRight'
        });
      });
    });

    it('应该在所有任务完成时显示全部完成通知', async () => {
      const completedTasks = [
        createMockTask({ id: 'task-1', status: 'completed' }),
        createMockTask({ id: 'task-2', status: 'completed' })
      ];
      
      render(
        <DownloadManager {...mockProps} tasks={completedTasks} enableNotifications={true} />
      );
      
      await waitFor(() => {
        expect(notification.success).toHaveBeenCalledWith({
          message: '全部下载完成！',
          description: '所有 2 个任务已成功下载完成',
          duration: 6,
          placement: 'topRight'
        });
      });
    });

    it('应该在禁用通知时不显示通知', async () => {
      const { rerender } = render(
        <DownloadManager {...mockProps} tasks={[]} enableNotifications={false} />
      );
      
      const completedTask = createMockTask({ status: 'completed' });
      rerender(
        <DownloadManager {...mockProps} tasks={[completedTask]} enableNotifications={false} />
      );
      
      await waitFor(() => {
        expect(notification.success).not.toHaveBeenCalled();
      });
    });
  });

  describe('会话统计', () => {
    it('应该显示会话统计信息', async () => {
      const { rerender } = render(
        <DownloadManager {...mockProps} tasks={[]} enableNotifications={true} />
      );
      
      // 添加完成的任务
      const completedTask = createMockTask({ 
        status: 'completed',
        resource: {
          ...mockResource,
          metadata: {
            ...mockResource.metadata,
            fileSize: 1024000 // 1MB
          }
        }
      });
      
      rerender(
        <DownloadManager {...mockProps} tasks={[completedTask]} enableNotifications={true} />
      );
      
      await waitFor(() => {
        expect(screen.getByText('本次会话统计')).toBeInTheDocument();
        expect(screen.getByText('1')).toBeInTheDocument(); // 已完成数量
      });
    });
  });

  describe('标签页切换', () => {
    it('应该能够切换不同的标签页', () => {
      const tasks = [
        createMockTask({ id: 'task-1', status: 'downloading' }),
        createMockTask({ id: 'task-2', status: 'completed' })
      ];
      
      render(<DownloadManager {...mockProps} tasks={tasks} />);
      
      // 点击下载中标签页 - 使用更具体的选择器
      const downloadingTab = screen.getAllByText(/下载中/)[0]; // 选择第一个匹配的元素（标签页）
      fireEvent.click(downloadingTab);
      
      // 应该只显示下载中的任务
      expect(screen.getAllByText('测试教材')).toHaveLength(1);
    });
  });

  describe('错误处理', () => {
    it('应该显示失败任务的错误信息', () => {
      const failedTask = createMockTask({ 
        status: 'failed',
        error: '网络连接失败'
      });
      
      render(<DownloadManager {...mockProps} tasks={[failedTask]} />);
      
      // 切换到失败标签页 - 使用更具体的选择器
      const failedTab = screen.getAllByText(/失败/)[1]; // 选择标签页中的失败文本
      fireEvent.click(failedTab);
      
      expect(screen.getByText(/网络连接失败/)).toBeInTheDocument();
    });
  });

  describe('响应式设计', () => {
    it('应该在小屏幕上正确显示', () => {
      // 模拟小屏幕
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768,
      });
      
      const tasks = [createMockTask()];
      render(<DownloadManager {...mockProps} tasks={tasks} />);
      
      // 验证组件在小屏幕上仍然可用
      expect(screen.getByText('下载管理器')).toBeInTheDocument();
      expect(screen.getByText('测试教材')).toBeInTheDocument();
    });
  });
});