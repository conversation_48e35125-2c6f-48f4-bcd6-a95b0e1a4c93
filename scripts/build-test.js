#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs-extra');
const path = require('path');

/**
 * 构建测试脚本
 * 用于验证应用打包和部署配置的正确性
 */
class BuildTester {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.releaseDir = path.join(this.projectRoot, 'release');
    this.testResults = [];
  }

  /**
   * 运行所有构建测试
   */
  async runAllTests() {
    console.log('🚀 开始构建测试...\n');

    try {
      await this.testProjectStructure();
      await this.testBuildConfiguration();
      await this.testBuildProcess();
      await this.testPackageOutput();
      
      this.printResults();
    } catch (error) {
      console.error('❌ 构建测试失败:', error.message);
      process.exit(1);
    }
  }

  /**
   * 测试项目结构
   */
  async testProjectStructure() {
    console.log('📁 测试项目结构...');
    
    const requiredFiles = [
      'package.json',
      'webpack.main.config.js',
      'webpack.renderer.config.js',
      'tsconfig.json',
      'build/entitlements.mac.plist',
      'build/installer.nsh',
      'build/app-metadata.json'
    ];

    const requiredDirs = [
      'src/main',
      'src/renderer',
      'src/shared',
      'build',
      'resources'
    ];

    for (const file of requiredFiles) {
      const filePath = path.join(this.projectRoot, file);
      if (await fs.pathExists(filePath)) {
        this.addResult('✅', `文件存在: ${file}`);
      } else {
        this.addResult('❌', `文件缺失: ${file}`);
      }
    }

    for (const dir of requiredDirs) {
      const dirPath = path.join(this.projectRoot, dir);
      if (await fs.pathExists(dirPath)) {
        this.addResult('✅', `目录存在: ${dir}`);
      } else {
        this.addResult('❌', `目录缺失: ${dir}`);
      }
    }
  }

  /**
   * 测试构建配置
   */
  async testBuildConfiguration() {
    console.log('⚙️  测试构建配置...');

    try {
      const packageJson = await fs.readJson(path.join(this.projectRoot, 'package.json'));
      
      // 检查基本配置
      if (packageJson.build) {
        this.addResult('✅', '存在 build 配置');
        
        // 检查必要的构建配置项
        const requiredBuildFields = ['appId', 'productName', 'directories', 'files'];
        for (const field of requiredBuildFields) {
          if (packageJson.build[field]) {
            this.addResult('✅', `build.${field} 配置正确`);
          } else {
            this.addResult('❌', `build.${field} 配置缺失`);
          }
        }

        // 检查平台配置
        const platforms = ['mac', 'win', 'linux'];
        for (const platform of platforms) {
          if (packageJson.build[platform]) {
            this.addResult('✅', `${platform} 平台配置存在`);
          } else {
            this.addResult('⚠️', `${platform} 平台配置缺失`);
          }
        }
      } else {
        this.addResult('❌', 'package.json 中缺少 build 配置');
      }

      // 检查构建脚本
      const requiredScripts = ['build', 'dist', 'pack'];
      for (const script of requiredScripts) {
        if (packageJson.scripts && packageJson.scripts[script]) {
          this.addResult('✅', `构建脚本存在: ${script}`);
        } else {
          this.addResult('❌', `构建脚本缺失: ${script}`);
        }
      }

    } catch (error) {
      this.addResult('❌', `读取 package.json 失败: ${error.message}`);
    }
  }

  /**
   * 测试构建过程
   */
  async testBuildProcess() {
    console.log('🔨 测试构建过程...');

    try {
      // 清理之前的构建
      if (await fs.pathExists(this.releaseDir)) {
        await fs.remove(this.releaseDir);
        this.addResult('✅', '清理旧的构建文件');
      }

      if (await fs.pathExists(path.join(this.projectRoot, 'dist'))) {
        await fs.remove(path.join(this.projectRoot, 'dist'));
        this.addResult('✅', '清理旧的 dist 目录');
      }

      // 运行构建
      console.log('   正在运行 npm run build...');
      execSync('npm run build', { 
        cwd: this.projectRoot, 
        stdio: 'pipe' 
      });
      this.addResult('✅', '源代码构建成功');

      // 检查构建输出
      const distDir = path.join(this.projectRoot, 'dist');
      if (await fs.pathExists(distDir)) {
        const mainExists = await fs.pathExists(path.join(distDir, 'main'));
        const rendererExists = await fs.pathExists(path.join(distDir, 'renderer'));
        
        if (mainExists) {
          this.addResult('✅', 'main 进程构建输出存在');
        } else {
          this.addResult('❌', 'main 进程构建输出缺失');
        }

        if (rendererExists) {
          this.addResult('✅', 'renderer 进程构建输出存在');
        } else {
          this.addResult('❌', 'renderer 进程构建输出缺失');
        }
      } else {
        this.addResult('❌', 'dist 目录不存在');
      }

    } catch (error) {
      this.addResult('❌', `构建过程失败: ${error.message}`);
    }
  }

  /**
   * 测试打包输出
   */
  async testPackageOutput() {
    console.log('📦 测试打包输出...');

    try {
      // 运行打包（仅目录模式，不生成安装包）
      console.log('   正在运行 npm run pack...');
      execSync('npm run pack', { 
        cwd: this.projectRoot, 
        stdio: 'pipe' 
      });
      this.addResult('✅', '应用打包成功');

      // 检查打包输出
      if (await fs.pathExists(this.releaseDir)) {
        const releaseContents = await fs.readdir(this.releaseDir);
        
        if (releaseContents.length > 0) {
          this.addResult('✅', `打包输出存在，包含 ${releaseContents.length} 个文件/目录`);
          
          // 列出打包内容
          for (const item of releaseContents) {
            const itemPath = path.join(this.releaseDir, item);
            const stats = await fs.stat(itemPath);
            const type = stats.isDirectory() ? '目录' : '文件';
            const size = stats.isFile() ? `(${this.formatBytes(stats.size)})` : '';
            this.addResult('ℹ️', `  ${type}: ${item} ${size}`);
          }
        } else {
          this.addResult('❌', '打包输出目录为空');
        }
      } else {
        this.addResult('❌', '打包输出目录不存在');
      }

    } catch (error) {
      this.addResult('❌', `打包过程失败: ${error.message}`);
    }
  }

  /**
   * 添加测试结果
   */
  addResult(status, message) {
    this.testResults.push({ status, message });
    console.log(`   ${status} ${message}`);
  }

  /**
   * 打印测试结果摘要
   */
  printResults() {
    console.log('\n📊 测试结果摘要:');
    console.log('='.repeat(50));

    const passed = this.testResults.filter(r => r.status === '✅').length;
    const failed = this.testResults.filter(r => r.status === '❌').length;
    const warnings = this.testResults.filter(r => r.status === '⚠️').length;
    const info = this.testResults.filter(r => r.status === 'ℹ️').length;

    console.log(`✅ 通过: ${passed}`);
    console.log(`❌ 失败: ${failed}`);
    console.log(`⚠️  警告: ${warnings}`);
    console.log(`ℹ️  信息: ${info}`);
    console.log(`📊 总计: ${this.testResults.length}`);

    if (failed > 0) {
      console.log('\n❌ 存在失败的测试项，请检查配置！');
      process.exit(1);
    } else {
      console.log('\n🎉 所有测试通过！应用已准备好进行打包和部署。');
    }
  }

  /**
   * 格式化字节大小
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// 运行测试
if (require.main === module) {
  const tester = new BuildTester();
  tester.runAllTests().catch(console.error);
}

module.exports = BuildTester;