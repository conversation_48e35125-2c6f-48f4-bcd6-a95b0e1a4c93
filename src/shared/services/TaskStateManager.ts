import { EventEmitter } from 'events';
import { TaskStatus, DownloadTask } from '../types';

/**
 * 状态转换规则
 */
export interface StateTransitionRule {
  from: TaskStatus;
  to: TaskStatus;
  condition?: (task: DownloadTask) => boolean;
  action?: (task: DownloadTask) => void;
}

/**
 * 状态历史记录
 */
export interface StateHistory {
  taskId: string;
  fromStatus: TaskStatus;
  toStatus: TaskStatus;
  timestamp: Date;
  reason?: string;
  metadata?: Record<string, any>;
}

/**
 * 状态管理器配置
 */
export interface StateManagerConfig {
  enableHistory: boolean;
  maxHistorySize: number;
  persistHistory: boolean;
  validateTransitions: boolean;
}

/**
 * 状态管理器事件
 */
export interface StateManagerEvents {
  'state-changed': (taskId: string, fromStatus: TaskStatus, toStatus: TaskStatus, reason?: string) => void;
  'invalid-transition': (taskId: string, fromStatus: TaskStatus, toStatus: TaskStatus) => void;
  'history-updated': (history: StateHistory) => void;
}

/**
 * 任务状态管理器
 * 负责管理任务状态转换、验证和历史记录
 */
export class TaskStateManager extends EventEmitter {
  private config: StateManagerConfig;
  private stateHistory: StateHistory[] = [];
  private transitionRules: StateTransitionRule[] = [];
  private taskStates: Map<string, TaskStatus> = new Map();

  private readonly defaultConfig: StateManagerConfig = {
    enableHistory: true,
    maxHistorySize: 1000,
    persistHistory: false,
    validateTransitions: true
  };

  // 默认状态转换规则
  private readonly defaultTransitionRules: StateTransitionRule[] = [
    // 从 pending 可以转换到的状态
    { from: 'pending', to: 'downloading' },
    { from: 'pending', to: 'cancelled' },
    { from: 'pending', to: 'failed' },

    // 从 downloading 可以转换到的状态
    { from: 'downloading', to: 'paused' },
    { from: 'downloading', to: 'completed' },
    { from: 'downloading', to: 'failed' },
    { from: 'downloading', to: 'cancelled' },

    // 从 paused 可以转换到的状态
    { from: 'paused', to: 'downloading' },
    { from: 'paused', to: 'resuming' },
    { from: 'paused', to: 'cancelled' },
    { from: 'paused', to: 'failed' },

    // 从 resuming 可以转换到的状态
    { from: 'resuming', to: 'downloading' },
    { from: 'resuming', to: 'failed' },
    { from: 'resuming', to: 'cancelled' },

    // 从 failed 可以转换到的状态
    { from: 'failed', to: 'pending' }, // 重试
    { from: 'failed', to: 'cancelled' },

    // 从 completed 可以转换到的状态（通常不允许）
    // { from: 'completed', to: 'pending' }, // 重新下载（可选）

    // 从 cancelled 可以转换到的状态
    { from: 'cancelled', to: 'pending' } // 重新开始
  ];

  constructor(config?: Partial<StateManagerConfig>) {
    super();
    this.config = { ...this.defaultConfig, ...config };
    this.transitionRules = [...this.defaultTransitionRules];
  }

  /**
   * 初始化任务状态
   */
  initializeTask(taskId: string, initialStatus: TaskStatus = 'pending'): void {
    this.taskStates.set(taskId, initialStatus);
    
    if (this.config.enableHistory) {
      this.addToHistory(taskId, initialStatus, initialStatus, '任务初始化');
    }
  }

  /**
   * 转换任务状态
   */
  transitionState(
    taskId: string, 
    toStatus: TaskStatus, 
    reason?: string,
    metadata?: Record<string, any>
  ): boolean {
    const currentStatus = this.taskStates.get(taskId);
    if (!currentStatus) {
      throw new Error(`任务 ${taskId} 未初始化`);
    }

    // 如果状态相同，不需要转换
    if (currentStatus === toStatus) {
      return true;
    }

    // 验证状态转换是否合法
    if (this.config.validateTransitions && !this.isValidTransition(currentStatus, toStatus)) {
      this.emit('invalid-transition', taskId, currentStatus, toStatus);
      return false;
    }

    // 执行状态转换
    this.taskStates.set(taskId, toStatus);

    // 记录历史
    if (this.config.enableHistory) {
      this.addToHistory(taskId, currentStatus, toStatus, reason, metadata);
    }

    // 发送状态变更事件
    this.emit('state-changed', taskId, currentStatus, toStatus, reason);

    return true;
  }

  /**
   * 获取任务当前状态
   */
  getTaskStatus(taskId: string): TaskStatus | undefined {
    return this.taskStates.get(taskId);
  }

  /**
   * 获取所有任务状态
   */
  getAllTaskStates(): Map<string, TaskStatus> {
    return new Map(this.taskStates);
  }

  /**
   * 根据状态获取任务列表
   */
  getTasksByStatus(status: TaskStatus): string[] {
    const tasks: string[] = [];
    for (const [taskId, taskStatus] of this.taskStates.entries()) {
      if (taskStatus === status) {
        tasks.push(taskId);
      }
    }
    return tasks;
  }

  /**
   * 获取任务状态历史
   */
  getTaskHistory(taskId: string): StateHistory[] {
    return this.stateHistory.filter(history => history.taskId === taskId);
  }

  /**
   * 获取所有状态历史
   */
  getAllHistory(): StateHistory[] {
    return [...this.stateHistory];
  }

  /**
   * 清除任务状态和历史
   */
  removeTask(taskId: string): void {
    this.taskStates.delete(taskId);
    
    if (this.config.enableHistory) {
      this.stateHistory = this.stateHistory.filter(history => history.taskId !== taskId);
    }
  }

  /**
   * 添加自定义状态转换规则
   */
  addTransitionRule(rule: StateTransitionRule): void {
    this.transitionRules.push(rule);
  }

  /**
   * 移除状态转换规则
   */
  removeTransitionRule(from: TaskStatus, to: TaskStatus): boolean {
    const index = this.transitionRules.findIndex(rule => rule.from === from && rule.to === to);
    if (index !== -1) {
      this.transitionRules.splice(index, 1);
      return true;
    }
    return false;
  }

  /**
   * 获取可能的状态转换
   */
  getPossibleTransitions(fromStatus: TaskStatus): TaskStatus[] {
    return this.transitionRules
      .filter(rule => rule.from === fromStatus)
      .map(rule => rule.to);
  }

  /**
   * 验证状态转换是否合法
   */
  isValidTransition(fromStatus: TaskStatus, toStatus: TaskStatus): boolean {
    return this.transitionRules.some(rule => 
      rule.from === fromStatus && 
      rule.to === toStatus &&
      (!rule.condition || rule.condition({ status: fromStatus } as DownloadTask))
    );
  }

  /**
   * 获取状态统计信息
   */
  getStatusStats(): Record<TaskStatus, number> {
    const stats: Record<TaskStatus, number> = {
      'pending': 0,
      'downloading': 0,
      'paused': 0,
      'completed': 0,
      'failed': 0,
      'cancelled': 0,
      'resuming': 0
    };

    for (const status of this.taskStates.values()) {
      stats[status]++;
    }

    return stats;
  }

  /**
   * 清除历史记录
   */
  clearHistory(): void {
    this.stateHistory = [];
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<StateManagerConfig>): void {
    this.config = { ...this.config, ...config };
    
    // 如果禁用历史记录，清除现有历史
    if (!this.config.enableHistory) {
      this.clearHistory();
    }
    
    // 如果历史记录大小限制变小，截断历史
    if (this.stateHistory.length > this.config.maxHistorySize) {
      this.stateHistory = this.stateHistory.slice(-this.config.maxHistorySize);
    }
  }

  /**
   * 获取当前配置
   */
  getConfig(): StateManagerConfig {
    return { ...this.config };
  }

  /**
   * 销毁状态管理器
   */
  destroy(): void {
    this.taskStates.clear();
    this.stateHistory = [];
    this.transitionRules = [];
    this.removeAllListeners();
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(
    taskId: string,
    fromStatus: TaskStatus,
    toStatus: TaskStatus,
    reason?: string,
    metadata?: Record<string, any>
  ): void {
    const historyEntry: StateHistory = {
      taskId,
      fromStatus,
      toStatus,
      timestamp: new Date(),
      reason,
      metadata
    };

    this.stateHistory.push(historyEntry);

    // 限制历史记录大小
    if (this.stateHistory.length > this.config.maxHistorySize) {
      this.stateHistory.shift();
    }

    this.emit('history-updated', historyEntry);
  }
}

export default TaskStateManager;
