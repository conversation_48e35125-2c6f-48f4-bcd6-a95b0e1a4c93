import { EventEmitter } from 'events';

/**
 * 并发控制配置
 */
export interface ConcurrencyConfig {
  maxConcurrent: number;
  enableDynamicAdjustment: boolean;
  resourceThresholds: ResourceThresholds;
  adaptiveStrategy: AdaptiveStrategy;
}

/**
 * 资源阈值配置
 */
export interface ResourceThresholds {
  maxMemoryUsage: number; // MB
  maxCpuUsage: number; // 百分比
  maxNetworkBandwidth: number; // MB/s
  maxDiskIO: number; // MB/s
}

/**
 * 自适应策略
 */
export type AdaptiveStrategy = 'conservative' | 'balanced' | 'aggressive';

/**
 * 资源使用情况
 */
export interface ResourceUsage {
  memoryUsage: number; // MB
  cpuUsage: number; // 百分比
  networkBandwidth: number; // MB/s
  diskIO: number; // MB/s
  timestamp: Date;
}

/**
 * 并发槽位
 */
export interface ConcurrencySlot {
  id: string;
  taskId: string | null;
  isActive: boolean;
  startTime?: Date;
  lastActivity?: Date;
}

/**
 * 并发统计信息
 */
export interface ConcurrencyStats {
  totalSlots: number;
  activeSlots: number;
  availableSlots: number;
  utilizationRate: number; // 利用率百分比
  averageTaskDuration: number; // 平均任务持续时间（毫秒）
  throughput: number; // 每分钟完成的任务数
}

/**
 * 并发控制器事件
 */
export interface ConcurrencyEvents {
  'slot-acquired': (slotId: string, taskId: string) => void;
  'slot-released': (slotId: string, taskId: string) => void;
  'concurrency-adjusted': (oldLimit: number, newLimit: number, reason: string) => void;
  'resource-threshold-exceeded': (resource: keyof ResourceThresholds, current: number, threshold: number) => void;
  'stats-updated': (stats: ConcurrencyStats) => void;
}

/**
 * 并发控制器
 * 负责管理下载任务的并发执行和资源使用
 */
export class ConcurrencyController extends EventEmitter {
  private config: ConcurrencyConfig;
  private slots: Map<string, ConcurrencySlot> = new Map();
  private waitingQueue: string[] = [];
  private completedTasks: Array<{ taskId: string, duration: number, completedAt: Date }> = [];
  private resourceMonitorInterval?: NodeJS.Timeout;
  private lastResourceCheck: Date = new Date();

  private readonly defaultConfig: ConcurrencyConfig = {
    maxConcurrent: 3,
    enableDynamicAdjustment: true,
    resourceThresholds: {
      maxMemoryUsage: 1024, // 1GB
      maxCpuUsage: 80, // 80%
      maxNetworkBandwidth: 50, // 50MB/s
      maxDiskIO: 100 // 100MB/s
    },
    adaptiveStrategy: 'balanced'
  };

  constructor(config?: Partial<ConcurrencyConfig>) {
    super();
    this.config = { ...this.defaultConfig, ...config };
    this.initializeSlots();
    
    if (this.config.enableDynamicAdjustment) {
      this.startResourceMonitoring();
    }
  }

  /**
   * 请求并发槽位
   */
  async acquireSlot(taskId: string): Promise<string | null> {
    // 查找可用槽位
    const availableSlot = this.findAvailableSlot();
    
    if (availableSlot) {
      // 分配槽位
      availableSlot.taskId = taskId;
      availableSlot.isActive = true;
      availableSlot.startTime = new Date();
      availableSlot.lastActivity = new Date();
      
      this.emit('slot-acquired', availableSlot.id, taskId);
      this.emitStatsUpdate();
      
      return availableSlot.id;
    }

    // 没有可用槽位，加入等待队列
    if (!this.waitingQueue.includes(taskId)) {
      this.waitingQueue.push(taskId);
    }
    
    return null;
  }

  /**
   * 释放并发槽位
   */
  releaseSlot(slotId: string): boolean {
    const slot = this.slots.get(slotId);
    if (!slot || !slot.isActive) {
      return false;
    }

    const taskId = slot.taskId!;
    const duration = slot.startTime ? Date.now() - slot.startTime.getTime() : 0;

    // 记录完成的任务
    this.completedTasks.push({
      taskId,
      duration,
      completedAt: new Date()
    });

    // 限制完成任务记录的数量
    if (this.completedTasks.length > 100) {
      this.completedTasks.shift();
    }

    // 重置槽位
    slot.taskId = null;
    slot.isActive = false;
    slot.startTime = undefined;
    slot.lastActivity = undefined;

    this.emit('slot-released', slotId, taskId);
    this.emitStatsUpdate();

    // 处理等待队列
    this.processWaitingQueue();

    return true;
  }

  /**
   * 更新槽位活动时间
   */
  updateSlotActivity(slotId: string): void {
    const slot = this.slots.get(slotId);
    if (slot && slot.isActive) {
      slot.lastActivity = new Date();
    }
  }

  /**
   * 获取任务的槽位ID
   */
  getSlotForTask(taskId: string): string | null {
    for (const [slotId, slot] of this.slots.entries()) {
      if (slot.taskId === taskId && slot.isActive) {
        return slotId;
      }
    }
    return null;
  }

  /**
   * 检查是否有可用槽位
   */
  hasAvailableSlot(): boolean {
    return this.findAvailableSlot() !== null;
  }

  /**
   * 获取等待队列长度
   */
  getWaitingQueueLength(): number {
    return this.waitingQueue.length;
  }

  /**
   * 获取等待队列中的任务
   */
  getWaitingTasks(): string[] {
    return [...this.waitingQueue];
  }

  /**
   * 获取当前活动任务的ID列表
   */
  getActiveTaskIds(): string[] {
    const activeTaskIds: string[] = [];
    for (const slot of this.slots.values()) {
      if (slot.isActive && slot.taskId) {
        activeTaskIds.push(slot.taskId);
      }
    }
    return activeTaskIds;
  }

  /**
   * 从等待队列中移除任务
   */
  removeFromWaitingQueue(taskId: string): boolean {
    const index = this.waitingQueue.indexOf(taskId);
    if (index !== -1) {
      this.waitingQueue.splice(index, 1);
      return true;
    }
    return false;
  }

  /**
   * 动态调整并发数
   */
  adjustConcurrency(newLimit: number, reason: string = '手动调整'): void {
    const oldLimit = this.config.maxConcurrent;
    
    if (newLimit === oldLimit) {
      return;
    }

    this.config.maxConcurrent = newLimit;

    if (newLimit > oldLimit) {
      // 增加槽位
      this.addSlots(newLimit - oldLimit);
      this.processWaitingQueue();
    } else {
      // 减少槽位（不会强制停止正在运行的任务）
      this.removeSlots(oldLimit - newLimit);
    }

    this.emit('concurrency-adjusted', oldLimit, newLimit, reason);
    this.emitStatsUpdate();
  }

  /**
   * 获取并发统计信息
   */
  getStats(): ConcurrencyStats {
    const totalSlots = this.slots.size;
    const activeSlots = Array.from(this.slots.values()).filter(slot => slot.isActive).length;
    const availableSlots = totalSlots - activeSlots;
    const utilizationRate = totalSlots > 0 ? (activeSlots / totalSlots) * 100 : 0;

    // 计算平均任务持续时间
    const averageTaskDuration = this.calculateAverageTaskDuration();

    // 计算吞吐量
    const throughput = this.calculateThroughput();

    return {
      totalSlots,
      activeSlots,
      availableSlots,
      utilizationRate,
      averageTaskDuration,
      throughput
    };
  }

  /**
   * 获取当前配置
   */
  getConfig(): ConcurrencyConfig {
    return { ...this.config };
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<ConcurrencyConfig>): void {
    const oldMaxConcurrent = this.config.maxConcurrent;
    this.config = { ...this.config, ...config };

    // 如果并发数改变，调整槽位
    if (config.maxConcurrent !== undefined && config.maxConcurrent !== oldMaxConcurrent) {
      this.adjustConcurrency(config.maxConcurrent, '配置更新');
    }

    // 如果启用/禁用动态调整
    if (config.enableDynamicAdjustment !== undefined) {
      if (config.enableDynamicAdjustment && !this.resourceMonitorInterval) {
        this.startResourceMonitoring();
      } else if (!config.enableDynamicAdjustment && this.resourceMonitorInterval) {
        this.stopResourceMonitoring();
      }
    }
  }

  /**
   * 销毁并发控制器
   */
  destroy(): void {
    this.stopResourceMonitoring();
    
    // 释放所有活动槽位
    for (const slot of this.slots.values()) {
      if (slot.isActive && slot.taskId) {
        this.emit('slot-released', slot.id, slot.taskId);
      }
    }
    
    this.slots.clear();
    this.waitingQueue = [];
    this.completedTasks = [];
    this.removeAllListeners();
  }

  /**
   * 初始化槽位
   */
  private initializeSlots(): void {
    this.slots.clear();
    for (let i = 0; i < this.config.maxConcurrent; i++) {
      const slotId = `slot_${i}`;
      this.slots.set(slotId, {
        id: slotId,
        taskId: null,
        isActive: false
      });
    }
  }

  /**
   * 查找可用槽位
   */
  private findAvailableSlot(): ConcurrencySlot | null {
    for (const slot of this.slots.values()) {
      if (!slot.isActive) {
        return slot;
      }
    }
    return null;
  }

  /**
   * 处理等待队列
   */
  private processWaitingQueue(): void {
    while (this.waitingQueue.length > 0 && this.hasAvailableSlot()) {
      const taskId = this.waitingQueue.shift()!;
      this.acquireSlot(taskId);
    }
  }

  /**
   * 添加槽位
   */
  private addSlots(count: number): void {
    const currentSize = this.slots.size;
    for (let i = 0; i < count; i++) {
      const slotId = `slot_${currentSize + i}`;
      this.slots.set(slotId, {
        id: slotId,
        taskId: null,
        isActive: false
      });
    }
  }

  /**
   * 移除槽位
   */
  private removeSlots(count: number): void {
    const slotsToRemove: string[] = [];

    // 优先移除非活动槽位
    for (const [slotId, slot] of this.slots.entries()) {
      if (!slot.isActive && slotsToRemove.length < count) {
        slotsToRemove.push(slotId);
      }
    }

    // 如果非活动槽位不够，标记活动槽位在完成后不再使用
    if (slotsToRemove.length < count) {
      for (const [slotId, slot] of this.slots.entries()) {
        if (slot.isActive && slotsToRemove.length < count) {
          slotsToRemove.push(slotId);
        }
      }
    }

    // 移除槽位
    slotsToRemove.forEach(slotId => {
      const slot = this.slots.get(slotId);
      if (slot && !slot.isActive) {
        this.slots.delete(slotId);
      }
    });
  }

  /**
   * 计算平均任务持续时间
   */
  private calculateAverageTaskDuration(): number {
    if (this.completedTasks.length === 0) {
      return 0;
    }

    const totalDuration = this.completedTasks.reduce((sum, task) => sum + task.duration, 0);
    return totalDuration / this.completedTasks.length;
  }

  /**
   * 计算吞吐量
   */
  private calculateThroughput(): number {
    const now = new Date();
    const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);

    const recentTasks = this.completedTasks.filter(task => task.completedAt >= oneMinuteAgo);
    return recentTasks.length;
  }

  /**
   * 开始资源监控
   */
  private startResourceMonitoring(): void {
    if (this.resourceMonitorInterval) {
      return;
    }

    this.resourceMonitorInterval = setInterval(() => {
      this.checkResourceUsage();
    }, 5000); // 每5秒检查一次
  }

  /**
   * 停止资源监控
   */
  private stopResourceMonitoring(): void {
    if (this.resourceMonitorInterval) {
      clearInterval(this.resourceMonitorInterval);
      this.resourceMonitorInterval = undefined;
    }
  }

  /**
   * 检查资源使用情况
   */
  private async checkResourceUsage(): Promise<void> {
    try {
      const resourceUsage = await this.getCurrentResourceUsage();
      const thresholds = this.config.resourceThresholds;

      let shouldAdjust = false;
      let adjustmentReason = '';
      let newConcurrency = this.config.maxConcurrent;

      // 检查内存使用
      if (resourceUsage.memoryUsage > thresholds.maxMemoryUsage) {
        this.emit('resource-threshold-exceeded', 'maxMemoryUsage', resourceUsage.memoryUsage, thresholds.maxMemoryUsage);
        shouldAdjust = true;
        adjustmentReason = '内存使用过高';
        newConcurrency = Math.max(1, Math.floor(this.config.maxConcurrent * 0.8));
      }

      // 检查CPU使用
      if (resourceUsage.cpuUsage > thresholds.maxCpuUsage) {
        this.emit('resource-threshold-exceeded', 'maxCpuUsage', resourceUsage.cpuUsage, thresholds.maxCpuUsage);
        shouldAdjust = true;
        adjustmentReason = 'CPU使用过高';
        newConcurrency = Math.max(1, Math.floor(this.config.maxConcurrent * 0.7));
      }

      // 检查网络带宽
      if (resourceUsage.networkBandwidth > thresholds.maxNetworkBandwidth) {
        this.emit('resource-threshold-exceeded', 'maxNetworkBandwidth', resourceUsage.networkBandwidth, thresholds.maxNetworkBandwidth);
        shouldAdjust = true;
        adjustmentReason = '网络带宽使用过高';
        newConcurrency = Math.max(1, Math.floor(this.config.maxConcurrent * 0.6));
      }

      // 根据策略调整并发数
      if (shouldAdjust) {
        this.applyAdaptiveStrategy(newConcurrency, adjustmentReason);
      } else {
        // 如果资源使用正常，可以考虑增加并发数
        this.considerIncreasingConcurrency(resourceUsage);
      }

    } catch (error) {
      console.warn('资源监控检查失败:', error);
    }
  }

  /**
   * 获取当前资源使用情况
   */
  private async getCurrentResourceUsage(): Promise<ResourceUsage> {
    // 这里应该实现实际的资源监控逻辑
    // 在实际应用中，可以使用 Node.js 的 process.memoryUsage() 等API
    // 或者集成第三方监控库

    return {
      memoryUsage: process.memoryUsage().heapUsed / 1024 / 1024, // MB
      cpuUsage: 0, // 需要实现CPU监控
      networkBandwidth: 0, // 需要实现网络监控
      diskIO: 0, // 需要实现磁盘IO监控
      timestamp: new Date()
    };
  }

  /**
   * 应用自适应策略
   */
  private applyAdaptiveStrategy(suggestedConcurrency: number, reason: string): void {
    let finalConcurrency = suggestedConcurrency;

    switch (this.config.adaptiveStrategy) {
      case 'conservative':
        // 保守策略：更激进地减少并发数
        finalConcurrency = Math.max(1, Math.floor(suggestedConcurrency * 0.8));
        break;

      case 'balanced':
        // 平衡策略：使用建议的并发数
        finalConcurrency = suggestedConcurrency;
        break;

      case 'aggressive':
        // 激进策略：较少地减少并发数
        finalConcurrency = Math.max(1, Math.floor(suggestedConcurrency * 1.2));
        break;
    }

    if (finalConcurrency !== this.config.maxConcurrent) {
      this.adjustConcurrency(finalConcurrency, `自适应调整: ${reason}`);
    }
  }

  /**
   * 考虑增加并发数
   */
  private considerIncreasingConcurrency(resourceUsage: ResourceUsage): void {
    const thresholds = this.config.resourceThresholds;
    const utilizationRate = this.getStats().utilizationRate;

    // 如果利用率高且资源使用低，考虑增加并发数
    if (utilizationRate > 90 &&
        resourceUsage.memoryUsage < thresholds.maxMemoryUsage * 0.6 &&
        resourceUsage.cpuUsage < thresholds.maxCpuUsage * 0.6) {

      const newConcurrency = Math.min(10, this.config.maxConcurrent + 1); // 最大不超过10
      if (newConcurrency > this.config.maxConcurrent) {
        this.adjustConcurrency(newConcurrency, '自适应增加: 资源充足且利用率高');
      }
    }
  }

  /**
   * 发送统计信息更新事件
   */
  private emitStatsUpdate(): void {
    const stats = this.getStats();
    this.emit('stats-updated', stats);
  }
}

export default ConcurrencyController;
