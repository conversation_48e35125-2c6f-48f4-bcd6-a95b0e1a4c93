import React, { useState, useEffect } from 'react';
import { Card, Progress, Alert, Statistic, Row, Col, Badge, Tooltip, Button } from 'antd';
import { 
  HddOutlined, 
  MoreOutlined as MemoryOutlined, 
  WifiOutlined, 
  WarningOutlined,
  CheckCircleOutlined,
  SyncOutlined,
  DeleteOutlined
} from '@ant-design/icons';

interface DiskSpaceInfo {
  total: number;
  free: number;
  used: number;
  usagePercentage: number;
}

interface MemoryInfo {
  totalMemory: number;
  freeMemory: number;
  usedMemory: number;
  usagePercentage: number;
  processMemory: NodeJS.MemoryUsage;
}

interface NetworkStatus {
  isOnline: boolean;
  connectionType: 'wifi' | 'ethernet' | 'cellular' | 'unknown';
  speed: {
    download: number;
    upload: number;
    ping: number;
  };
  latency: number;
  stability: number;
}

interface SystemResourceMonitorProps {
  onOptimize?: () => void;
  onCleanup?: () => void;
}

export const SystemResourceMonitor: React.FC<SystemResourceMonitorProps> = ({
  onOptimize,
  onCleanup
}) => {
  const [diskSpace, setDiskSpace] = useState<DiskSpaceInfo | null>(null);
  const [memory, setMemory] = useState<MemoryInfo | null>(null);
  const [network, setNetwork] = useState<NetworkStatus | null>(null);
  const [warnings, setWarnings] = useState<string[]>([]);
  const [isMonitoring, setIsMonitoring] = useState(false);

  useEffect(() => {
    startMonitoring();
    return () => stopMonitoring();
  }, []);

  const startMonitoring = async () => {
    setIsMonitoring(true);
    
    // 模拟监控数据更新
    const updateInterval = setInterval(() => {
      updateSystemStats();
    }, 5000);

    // 初始更新
    updateSystemStats();

    return () => clearInterval(updateInterval);
  };

  const stopMonitoring = () => {
    setIsMonitoring(false);
  };

  const updateSystemStats = () => {
    // 模拟磁盘空间数据
    const mockDiskSpace: DiskSpaceInfo = {
      total: 500 * 1024 * 1024 * 1024, // 500GB
      free: 150 * 1024 * 1024 * 1024,  // 150GB
      used: 350 * 1024 * 1024 * 1024,  // 350GB
      usagePercentage: 0.7
    };

    // 模拟内存数据
    const mockMemory: MemoryInfo = {
      totalMemory: 16 * 1024 * 1024 * 1024, // 16GB
      freeMemory: 4 * 1024 * 1024 * 1024,   // 4GB
      usedMemory: 12 * 1024 * 1024 * 1024,  // 12GB
      usagePercentage: 0.75,
      processMemory: {
        rss: 200 * 1024 * 1024,
        heapTotal: 150 * 1024 * 1024,
        heapUsed: 120 * 1024 * 1024,
        external: 10 * 1024 * 1024,
        arrayBuffers: 5 * 1024 * 1024
      }
    };

    // 模拟网络状态
    const mockNetwork: NetworkStatus = {
      isOnline: true,
      connectionType: 'wifi',
      speed: {
        download: 25.5,
        upload: 5.2,
        ping: 45
      },
      latency: 45,
      stability: 0.85
    };

    setDiskSpace(mockDiskSpace);
    setMemory(mockMemory);
    setNetwork(mockNetwork);

    // 检查警告
    const newWarnings: string[] = [];
    if (mockDiskSpace.usagePercentage > 0.9) {
      newWarnings.push('磁盘空间不足');
    }
    if (mockMemory.usagePercentage > 0.9) {
      newWarnings.push('内存使用率过高');
    }
    if (!mockNetwork.isOnline) {
      newWarnings.push('网络连接断开');
    }
    setWarnings(newWarnings);
  };

  const formatBytes = (bytes: number): string => {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  };

  const getProgressColor = (percentage: number): string => {
    if (percentage < 0.6) return '#52c41a';
    if (percentage < 0.8) return '#faad14';
    return '#ff4d4f';
  };

  const getNetworkQualityBadge = (network: NetworkStatus) => {
    if (!network.isOnline) {
      return <Badge status="error" text="离线" />;
    }
    
    const quality = network.speed.download > 20 && network.stability > 0.8 ? 'excellent' :
                   network.speed.download > 10 && network.stability > 0.6 ? 'good' :
                   network.speed.download > 5 ? 'fair' : 'poor';

    const statusMap = {
      excellent: { status: 'success' as const, text: '优秀' },
      good: { status: 'processing' as const, text: '良好' },
      fair: { status: 'warning' as const, text: '一般' },
      poor: { status: 'error' as const, text: '较差' }
    };

    return <Badge status={statusMap[quality].status} text={statusMap[quality].text} />;
  };

  return (
    <div style={{ padding: '16px' }}>
      {warnings.length > 0 && (
        <Alert
          message="系统资源警告"
          description={warnings.join(', ')}
          type="warning"
          icon={<WarningOutlined />}
          showIcon
          closable
          style={{ marginBottom: '16px' }}
        />
      )}

      <Row gutter={[16, 16]}>
        {/* 磁盘空间监控 */}
        <Col xs={24} md={8}>
          <Card
            title={
              <span>
                <HddOutlined style={{ marginRight: 8 }} />
                磁盘空间
              </span>
            }
            extra={
              <Tooltip title="清理磁盘空间">
                <Button 
                  type="text" 
                  icon={<DeleteOutlined />} 
                  onClick={onCleanup}
                  size="small"
                />
              </Tooltip>
            }
          >
            {diskSpace && (
              <>
                <Progress
                  type="circle"
                  percent={Math.round(diskSpace.usagePercentage * 100)}
                  strokeColor={getProgressColor(diskSpace.usagePercentage)}
                  size={120}
                  format={(percent) => `${percent}%`}
                />
                <div style={{ marginTop: '16px' }}>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Statistic
                        title="已使用"
                        value={formatBytes(diskSpace.used)}
                        valueStyle={{ fontSize: '14px' }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="可用"
                        value={formatBytes(diskSpace.free)}
                        valueStyle={{ fontSize: '14px' }}
                      />
                    </Col>
                  </Row>
                  <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                    总容量: {formatBytes(diskSpace.total)}
                  </div>
                </div>
              </>
            )}
          </Card>
        </Col>

        {/* 内存监控 */}
        <Col xs={24} md={8}>
          <Card
            title={
              <span>
                <MemoryOutlined style={{ marginRight: 8 }} />
                内存使用
              </span>
            }
            extra={
              <Tooltip title="优化内存">
                <Button 
                  type="text" 
                  icon={<SyncOutlined />} 
                  onClick={onOptimize}
                  size="small"
                />
              </Tooltip>
            }
          >
            {memory && (
              <>
                <Progress
                  type="circle"
                  percent={Math.round(memory.usagePercentage * 100)}
                  strokeColor={getProgressColor(memory.usagePercentage)}
                  size={120}
                  format={(percent) => `${percent}%`}
                />
                <div style={{ marginTop: '16px' }}>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Statistic
                        title="系统已用"
                        value={formatBytes(memory.usedMemory)}
                        valueStyle={{ fontSize: '14px' }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="应用占用"
                        value={formatBytes(memory.processMemory.heapUsed)}
                        valueStyle={{ fontSize: '14px' }}
                      />
                    </Col>
                  </Row>
                  <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                    总内存: {formatBytes(memory.totalMemory)}
                  </div>
                </div>
              </>
            )}
          </Card>
        </Col>

        {/* 网络状态监控 */}
        <Col xs={24} md={8}>
          <Card
            title={
              <span>
                <WifiOutlined style={{ marginRight: 8 }} />
                网络状态
              </span>
            }
            extra={network && getNetworkQualityBadge(network)}
          >
            {network && (
              <>
                <div style={{ textAlign: 'center', marginBottom: '16px' }}>
                  {network.isOnline ? (
                    <CheckCircleOutlined 
                      style={{ fontSize: '48px', color: '#52c41a' }} 
                    />
                  ) : (
                    <WarningOutlined 
                      style={{ fontSize: '48px', color: '#ff4d4f' }} 
                    />
                  )}
                </div>
                <Row gutter={16}>
                  <Col span={8}>
                    <Statistic
                      title="下载"
                      value={network.speed.download}
                      suffix="Mbps"
                      valueStyle={{ fontSize: '14px' }}
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="延迟"
                      value={network.latency}
                      suffix="ms"
                      valueStyle={{ fontSize: '14px' }}
                    />
                  </Col>
                  <Col span={8}>
                    <Statistic
                      title="稳定性"
                      value={Math.round(network.stability * 100)}
                      suffix="%"
                      valueStyle={{ fontSize: '14px' }}
                    />
                  </Col>
                </Row>
                <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                  连接类型: {network.connectionType}
                </div>
              </>
            )}
          </Card>
        </Col>
      </Row>

      {/* 系统状态总览 */}
      <Card 
        title="系统状态总览" 
        style={{ marginTop: '16px' }}
        extra={
          <Badge 
            status={isMonitoring ? 'processing' : 'default'} 
            text={isMonitoring ? '监控中' : '已停止'} 
          />
        }
      >
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="磁盘使用率"
              value={diskSpace ? Math.round(diskSpace.usagePercentage * 100) : 0}
              suffix="%"
              valueStyle={{ 
                color: diskSpace ? getProgressColor(diskSpace.usagePercentage) : '#666' 
              }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="内存使用率"
              value={memory ? Math.round(memory.usagePercentage * 100) : 0}
              suffix="%"
              valueStyle={{ 
                color: memory ? getProgressColor(memory.usagePercentage) : '#666' 
              }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="网络速度"
              value={network?.speed.download || 0}
              suffix="Mbps"
              valueStyle={{ color: network?.isOnline ? '#52c41a' : '#ff4d4f' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="系统状态"
              value={warnings.length === 0 ? '正常' : '警告'}
              valueStyle={{ 
                color: warnings.length === 0 ? '#52c41a' : '#faad14' 
              }}
            />
          </Col>
        </Row>
      </Card>
    </div>
  );
};