import { FilterOption } from '../types';
import { SmartEduClient } from './SmartEduClient';

/**
 * 筛选服务
 * 提供课程资源的筛选选项获取功能
 */
export class FilterService {
  private static instance: FilterService;
  private smartEduClient: SmartEduClient;

  private constructor() {
    this.smartEduClient = new SmartEduClient();
  }

  public static getInstance(): FilterService {
    if (!FilterService.instance) {
      FilterService.instance = new FilterService();
    }
    return FilterService.instance;
  }

  /**
   * 获取学段选项
   */
  async getStageOptions(): Promise<FilterOption[]> {
    try {
      const stages = await this.smartEduClient.getStages();
      return stages.map((stage: any) => ({
        value: stage.value || stage.id,
        label: stage.label || stage.name
      }));
    } catch (error) {
      console.error('获取学段选项失败:', error);
      return [];
    }
  }

  /**
   * 获取年级选项
   */
  async getGradeOptions(stage?: string): Promise<FilterOption[]> {
    try {
      if (!stage) return [];
      const grades = await this.smartEduClient.getGradesByStage(stage);
      return grades.map((grade: any) => ({
        value: grade.value || grade.id,
        label: grade.label || grade.name
      }));
    } catch (error) {
      console.error('获取年级选项失败:', error);
      return [];
    }
  }

  /**
   * 获取学科选项
   */
  async getSubjectOptions(stage?: string, grade?: string): Promise<FilterOption[]> {
    try {
      if (!stage || !grade) return [];
      const subjects = await this.smartEduClient.getSubjectsByStageAndGrade(stage, grade);
      return subjects.map((subject: any) => ({
        value: subject.value || subject.id,
        label: subject.label || subject.name
      }));
    } catch (error) {
      console.error('获取学科选项失败:', error);
      return [];
    }
  }

  /**
   * 获取版本选项
   */
  async getVersionOptions(stage?: string, grade?: string, subject?: string): Promise<FilterOption[]> {
    try {
      if (!stage || !grade || !subject) return [];
      // 暂时返回一些常见的版本选项
      return [
        { value: 'rjb', label: '人教版' },
        { value: 'bsb', label: '北师大版' },
        { value: 'sj', label: '苏教版' },
        { value: 'hk', label: '沪科版' },
        { value: 'other', label: '其他版本' }
      ];
    } catch (error) {
      console.error('获取版本选项失败:', error);
      return [];
    }
  }

  /**
   * 获取册别选项
   */
  async getVolumeOptions(stage?: string, grade?: string, subject?: string, version?: string): Promise<FilterOption[]> {
    try {
      if (!stage || !grade || !subject || !version) return [];
      // 暂时返回一些常见的册别选项
      return [
        { value: 'vol1', label: '上册' },
        { value: 'vol2', label: '下册' },
        { value: 'all', label: '全册' }
      ];
    } catch (error) {
      console.error('获取册别选项失败:', error);
      return [];
    }
  }

  /**
   * 获取所有筛选选项
   */
  async getAllFilterOptions(): Promise<{
    stages: FilterOption[];
    grades: FilterOption[];
    subjects: FilterOption[];
    versions: FilterOption[];
    volumes: FilterOption[];
  }> {
    return {
      stages: [],
      grades: [],
      subjects: [],
      versions: [],
      volumes: []
    };
  }
}

export default FilterService;
