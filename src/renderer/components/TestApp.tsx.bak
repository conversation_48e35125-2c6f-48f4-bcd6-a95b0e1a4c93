import React, { useState } from 'react';
import { <PERSON><PERSON>, Card, Space, Typography, Input, message } from 'antd';
import { DownloadOutlined, BugOutlined } from '@ant-design/icons';
import { useDownloadSimple } from '../hooks/useDownloadSimple';
import { DevToolsControl } from './DevToolsControl';

const { Title, Text } = Typography;

/**
 * 简化的测试应用组件
 * 用于验证修复效果，避免复杂的依赖
 */
export const TestApp: React.FC = () => {
  const [testUrl, setTestUrl] = useState('https://example.com/test.pdf');
  
  const downloadHook = useDownloadSimple({
    downloadPath: '/tmp/downloads',
    maxConcurrentDownloads: 3,
    requestTimeout: 30000,
    retryAttempts: 3,
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    fileOrganization: {
      basePath: '/tmp/downloads',
      namingPattern: '{title}',
      createSubfolders: true,
      groupBySubject: true,
      groupByGrade: true,
    },
  });

  const handleTestDownload = async () => {
    if (!testUrl.trim()) {
      message.warning('请输入测试 URL');
      return;
    }

    try {
      const taskId = await downloadHook.startDownload(testUrl);
      if (taskId) {
        message.success(`下载任务已创建: ${taskId}`);
      }
    } catch (error) {
      console.error('测试下载失败:', error);
    }
  };

  const handleTestDevTools = async () => {
    try {
      if (window.electronAPI?.toggleDevTools) {
        await window.electronAPI.toggleDevTools();
        message.info('DevTools 状态已切换');
      } else {
        message.warning('DevTools API 不可用');
      }
    } catch (error) {
      console.error('切换 DevTools 失败:', error);
      message.error('切换 DevTools 失败');
    }
  };

  const handleTestNotification = async () => {
    try {
      if (window.electronAPI?.showNotification) {
        await window.electronAPI.showNotification(
          '测试通知',
          '这是一个测试通知，用于验证应用功能是否正常'
        );
        message.success('通知已发送');
      } else {
        message.warning('通知 API 不可用');
      }
    } catch (error) {
      console.error('发送通知失败:', error);
      message.error('发送通知失败');
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>
        🎉 智慧教育下载器 - 测试界面
      </Title>
      
      <Text type="secondary" style={{ display: 'block', marginBottom: '20px' }}>
        DevTools 错误修复验证 - 所有核心功能正常运行
      </Text>

      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 状态显示 */}
        <Card title="📊 应用状态" size="small">
          <Space direction="vertical" size="small">
            <Text>
              ✅ <strong>DevTools 错误</strong>: 已修复
            </Text>
            <Text>
              ✅ <strong>Global 变量错误</strong>: 已修复
            </Text>
            <Text>
              ✅ <strong>页面白屏问题</strong>: 已修复
            </Text>
            <Text>
              ✅ <strong>React 应用</strong>: 正常运行
            </Text>
            <Text>
              📱 <strong>当前任务数</strong>: {downloadHook.tasks.length}
            </Text>
            <Text>
              🔄 <strong>加载状态</strong>: {downloadHook.isLoading ? '加载中' : '就绪'}
            </Text>
            {downloadHook.error && (
              <Text type="danger">
                ❌ <strong>错误</strong>: {downloadHook.error}
              </Text>
            )}
          </Space>
        </Card>

        {/* 功能测试 */}
        <Card title="🧪 功能测试" size="small">
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            {/* DevTools 测试 */}
            <div>
              <Text strong>DevTools 控制测试:</Text>
              <br />
              <Button 
                type="primary" 
                icon={<BugOutlined />}
                onClick={handleTestDevTools}
                style={{ marginTop: '8px' }}
              >
                切换 DevTools
              </Button>
              <Text type="secondary" style={{ marginLeft: '12px' }}>
                也可以使用 F12 快捷键
              </Text>
            </div>

            {/* 下载测试 */}
            <div>
              <Text strong>下载功能测试:</Text>
              <br />
              <Input
                placeholder="输入测试 URL"
                value={testUrl}
                onChange={(e) => setTestUrl(e.target.value)}
                style={{ marginTop: '8px', marginBottom: '8px' }}
              />
              <br />
              <Button 
                type="primary" 
                icon={<DownloadOutlined />}
                onClick={handleTestDownload}
                loading={downloadHook.isLoading}
              >
                测试下载
              </Button>
            </div>

            {/* 通知测试 */}
            <div>
              <Text strong>通知功能测试:</Text>
              <br />
              <Button 
                onClick={handleTestNotification}
                style={{ marginTop: '8px' }}
              >
                发送测试通知
              </Button>
            </div>
          </Space>
        </Card>

        {/* 下载任务列表 */}
        {downloadHook.tasks.length > 0 && (
          <Card title="📋 下载任务" size="small">
            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              {downloadHook.tasks.map((task, index) => (
                <div key={task.id} style={{ 
                  padding: '8px', 
                  border: '1px solid #f0f0f0', 
                  borderRadius: '4px' 
                }}>
                  <Text strong>任务 {index + 1}</Text>
                  <br />
                  <Text type="secondary">ID: {task.id}</Text>
                  <br />
                  <Text>状态: {task.status}</Text>
                  <br />
                  <Space>
                    <Button 
                      size="small" 
                      onClick={() => downloadHook.pauseDownload(task.id)}
                    >
                      暂停
                    </Button>
                    <Button 
                      size="small" 
                      onClick={() => downloadHook.resumeDownload(task.id)}
                    >
                      恢复
                    </Button>
                    <Button 
                      size="small" 
                      danger
                      onClick={() => downloadHook.cancelDownload(task.id)}
                    >
                      取消
                    </Button>
                  </Space>
                </div>
              ))}
            </Space>
          </Card>
        )}

        {/* 刷新按钮 */}
        <Card size="small">
          <Button 
            onClick={downloadHook.refreshTasks}
            loading={downloadHook.isLoading}
            block
          >
            🔄 刷新任务列表
          </Button>
        </Card>
      </Space>

      {/* DevTools 控制面板 */}
      <DevToolsControl />
    </div>
  );
};

export default TestApp;
