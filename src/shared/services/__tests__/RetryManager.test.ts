import { RetryManager, RetryConfig, RetryResult } from '../RetryManager';
import { NetworkError, ApiError, FileError } from '../../types';

describe('RetryManager', () => {
  let retryManager: RetryManager;

  beforeEach(() => {
    retryManager = new RetryManager({
      baseDelay: 10, // 使用很短的延迟来加速测试
      maxDelay: 100,
      jitter: false // 禁用抖动以获得可预测的结果
    });
  });

  afterEach(() => {
    retryManager.removeAllListeners();
  });

  describe('基本重试功能', () => {
    it('应该在操作成功时返回结果', async () => {
      const mockOperation = jest.fn().mockResolvedValue('success');
      
      const result = await retryManager.executeWithRetry(mockOperation);
      
      expect(result.success).toBe(true);
      expect(result.result).toBe('success');
      expect(result.attempts).toBe(1);
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    it('应该在操作失败时进行重试', async () => {
      const mockOperation = jest.fn()
        .mockRejectedValueOnce(new NetworkError('网络错误'))
        .mockRejectedValueOnce(new NetworkError('网络错误'))
        .mockResolvedValue('success');
      
      const result = await retryManager.executeWithRetry(mockOperation, { baseDelay: 10 });
      
      expect(result.success).toBe(true);
      expect(result.result).toBe('success');
      expect(result.attempts).toBe(3);
      expect(mockOperation).toHaveBeenCalledTimes(3);
    }, 10000);

    it('应该在达到最大重试次数后返回失败', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new NetworkError('网络错误'));
      
      const result = await retryManager.executeWithRetry(mockOperation, { maxRetries: 2, baseDelay: 10 });
      
      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(NetworkError);
      expect(result.attempts).toBe(3); // 初始尝试 + 2次重试
      expect(mockOperation).toHaveBeenCalledTimes(3);
    }, 10000);

    it('应该对不可重试的错误立即失败', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new FileError('文件错误'));
      
      const result = await retryManager.executeWithRetry(mockOperation);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(FileError);
      expect(result.attempts).toBe(1);
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });
  });

  describe('重试策略', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('应该使用指数退避策略', async () => {
      const mockOperation = jest.fn()
        .mockRejectedValueOnce(new NetworkError('网络错误'))
        .mockRejectedValueOnce(new NetworkError('网络错误'))
        .mockResolvedValue('success');

      const config: Partial<RetryConfig> = {
        strategy: 'exponential',
        baseDelay: 100,
        backoffFactor: 2,
        jitter: false
      };

      const resultPromise = retryManager.executeWithRetry(mockOperation, config);

      // 第一次重试延迟：100ms
      jest.advanceTimersByTime(100);
      await Promise.resolve(); // 让微任务执行

      // 第二次重试延迟：200ms
      jest.advanceTimersByTime(200);
      await Promise.resolve();

      const result = await resultPromise;
      
      expect(result.success).toBe(true);
      expect(result.attempts).toBe(3);
    });

    it('应该使用线性退避策略', async () => {
      const mockOperation = jest.fn()
        .mockRejectedValueOnce(new NetworkError('网络错误'))
        .mockRejectedValueOnce(new NetworkError('网络错误'))
        .mockResolvedValue('success');

      const config: Partial<RetryConfig> = {
        strategy: 'linear',
        baseDelay: 100,
        jitter: false
      };

      const resultPromise = retryManager.executeWithRetry(mockOperation, config);

      // 第一次重试延迟：100ms
      jest.advanceTimersByTime(100);
      await Promise.resolve();

      // 第二次重试延迟：200ms
      jest.advanceTimersByTime(200);
      await Promise.resolve();

      const result = await resultPromise;
      
      expect(result.success).toBe(true);
      expect(result.attempts).toBe(3);
    });

    it('应该使用固定延迟策略', async () => {
      const mockOperation = jest.fn()
        .mockRejectedValueOnce(new NetworkError('网络错误'))
        .mockRejectedValueOnce(new NetworkError('网络错误'))
        .mockResolvedValue('success');

      const config: Partial<RetryConfig> = {
        strategy: 'fixed',
        baseDelay: 100,
        jitter: false
      };

      const resultPromise = retryManager.executeWithRetry(mockOperation, config);

      // 第一次重试延迟：100ms
      jest.advanceTimersByTime(100);
      await Promise.resolve();

      // 第二次重试延迟：100ms
      jest.advanceTimersByTime(100);
      await Promise.resolve();

      const result = await resultPromise;
      
      expect(result.success).toBe(true);
      expect(result.attempts).toBe(3);
    });

    it('应该应用最大延迟限制', async () => {
      const mockOperation = jest.fn()
        .mockRejectedValueOnce(new NetworkError('网络错误'))
        .mockResolvedValue('success');

      const config: Partial<RetryConfig> = {
        strategy: 'exponential',
        baseDelay: 1000,
        maxDelay: 500,
        backoffFactor: 2,
        jitter: false
      };

      const resultPromise = retryManager.executeWithRetry(mockOperation, config);

      // 延迟应该被限制在500ms
      jest.advanceTimersByTime(500);
      await Promise.resolve();

      const result = await resultPromise;
      
      expect(result.success).toBe(true);
    });
  });

  describe('错误分类', () => {
    it('应该正确分类网络错误', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new NetworkError('网络连接失败'));
      
      const result = await retryManager.executeWithRetry(mockOperation, { maxRetries: 1 });
      
      expect(result.success).toBe(false);
      expect(result.attempts).toBe(2); // 会重试网络错误
    });

    it('应该正确分类API错误', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new ApiError('服务器错误'));
      
      const result = await retryManager.executeWithRetry(mockOperation, { maxRetries: 1 });
      
      expect(result.success).toBe(false);
      expect(result.attempts).toBe(2); // 会重试API错误
    });

    it('应该正确分类文件错误', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new FileError('文件不存在'));
      
      const result = await retryManager.executeWithRetry(mockOperation, { maxRetries: 1 });
      
      expect(result.success).toBe(false);
      expect(result.attempts).toBe(1); // 不会重试文件错误
    });

    it('应该根据错误消息分类未知错误', async () => {
      const timeoutError = new Error('请求超时');
      const mockOperation = jest.fn().mockRejectedValue(timeoutError);
      
      const result = await retryManager.executeWithRetry(mockOperation, { maxRetries: 1 });
      
      expect(result.success).toBe(false);
      expect(result.attempts).toBe(2); // 会重试超时错误
    });
  });

  describe('事件发射', () => {
    it('应该在重试时发射事件', async () => {
      const retryAttemptSpy = jest.fn();
      const retrySuccessSpy = jest.fn();
      
      retryManager.on('retry-attempt', retryAttemptSpy);
      retryManager.on('retry-success', retrySuccessSpy);

      const mockOperation = jest.fn()
        .mockRejectedValueOnce(new NetworkError('网络错误'))
        .mockResolvedValue('success');
      
      await retryManager.executeWithRetry(mockOperation);
      
      expect(retryAttemptSpy).toHaveBeenCalledTimes(1);
      expect(retrySuccessSpy).toHaveBeenCalledTimes(1);
    });

    it('应该在重试耗尽时发射事件', async () => {
      const retryExhaustedSpy = jest.fn();
      
      retryManager.on('retry-exhausted', retryExhaustedSpy);

      const mockOperation = jest.fn().mockRejectedValue(new NetworkError('网络错误'));
      
      await retryManager.executeWithRetry(mockOperation, { maxRetries: 1 });
      
      expect(retryExhaustedSpy).toHaveBeenCalledTimes(1);
    });

    it('应该在重试失败时发射事件', async () => {
      const retryFailedSpy = jest.fn();
      
      retryManager.on('retry-failed', retryFailedSpy);

      const mockOperation = jest.fn().mockRejectedValue(new FileError('文件错误'));
      
      await retryManager.executeWithRetry(mockOperation);
      
      expect(retryFailedSpy).toHaveBeenCalledTimes(1);
    });
  });

  describe('函数包装器', () => {
    it('应该创建带重试的函数包装器', async () => {
      const originalFunction = jest.fn()
        .mockRejectedValueOnce(new NetworkError('网络错误'))
        .mockResolvedValue('success');

      const wrappedFunction = retryManager.wrap(originalFunction, { maxRetries: 1 });
      
      const result = await wrappedFunction('arg1', 'arg2');
      
      expect(result).toBe('success');
      expect(originalFunction).toHaveBeenCalledTimes(2);
      expect(originalFunction).toHaveBeenCalledWith('arg1', 'arg2');
    });

    it('包装器应该在失败时抛出错误', async () => {
      const originalFunction = jest.fn().mockRejectedValue(new NetworkError('网络错误'));
      const wrappedFunction = retryManager.wrap(originalFunction, { maxRetries: 1 });
      
      await expect(wrappedFunction()).rejects.toThrow('网络错误');
    });
  });

  describe('批量操作', () => {
    it('应该执行批量重试操作', async () => {
      const operation1 = jest.fn().mockResolvedValue('result1');
      const operation2 = jest.fn()
        .mockRejectedValueOnce(new NetworkError('网络错误'))
        .mockResolvedValue('result2');
      const operation3 = jest.fn().mockRejectedValue(new FileError('文件错误'));

      const results = await retryManager.executeBatchWithRetry([
        operation1,
        operation2,
        operation3
      ]);

      expect(results).toHaveLength(3);
      expect(results[0].success).toBe(true);
      expect(results[0].result).toBe('result1');
      expect(results[1].success).toBe(true);
      expect(results[1].result).toBe('result2');
      expect(results[2].success).toBe(false);
      expect(results[2].error).toBeInstanceOf(FileError);
    });

    it('应该执行并行批量重试操作', async () => {
      const operation1 = jest.fn().mockResolvedValue('result1');
      const operation2 = jest.fn().mockResolvedValue('result2');
      const operation3 = jest.fn().mockResolvedValue('result3');

      const startTime = Date.now();
      const results = await retryManager.executeBatchParallelWithRetry([
        operation1,
        operation2,
        operation3
      ], {}, 2);
      const endTime = Date.now();

      expect(results).toHaveLength(3);
      expect(results.every(r => r.success)).toBe(true);
      
      // 并行执行应该比串行执行快
      expect(endTime - startTime).toBeLessThan(1000);
    });
  });

  describe('统计信息', () => {
    it('应该计算错误统计信息', () => {
      const states = [
        {
          attempt: 2,
          totalDelay: 1000,
          startTime: new Date(),
          errorHistory: [
            { error: new NetworkError('网络错误'), timestamp: new Date(), attempt: 1 }
          ]
        },
        {
          attempt: 1,
          totalDelay: 0,
          startTime: new Date(),
          errorHistory: []
        },
        {
          attempt: 3,
          totalDelay: 3000,
          startTime: new Date(),
          errorHistory: [
            { error: new ApiError('API错误'), timestamp: new Date(), attempt: 1 },
            { error: new NetworkError('网络错误'), timestamp: new Date(), attempt: 2 }
          ]
        }
      ];

      const stats = retryManager.getErrorStats(states);

      expect(stats.totalAttempts).toBe(6);
      expect(stats.totalErrors).toBe(3);
      expect(stats.errorsByType.network).toBe(2);
      expect(stats.errorsByType.api).toBe(1);
      expect(stats.averageAttempts).toBe(2);
      expect(stats.successRate).toBe(1/3); // 1个成功，2个有错误
    });
  });

  describe('配置管理', () => {
    it('应该更新配置', () => {
      const newConfig = { maxRetries: 5, baseDelay: 2000 };
      
      retryManager.updateConfig(newConfig);
      
      const config = retryManager.getConfig();
      expect(config.maxRetries).toBe(5);
      expect(config.baseDelay).toBe(2000);
    });

    it('应该重置配置为默认值', () => {
      retryManager.updateConfig({ maxRetries: 10 });
      retryManager.resetConfig();
      
      const config = retryManager.getConfig();
      expect(config.maxRetries).toBe(3); // 默认值
    });

    it('应该创建预设配置', () => {
      const aggressiveConfig = RetryManager.createPresetConfig('aggressive');
      expect(aggressiveConfig.maxRetries).toBe(5);
      expect(aggressiveConfig.baseDelay).toBe(500);

      const conservativeConfig = RetryManager.createPresetConfig('conservative');
      expect(conservativeConfig.maxRetries).toBe(2);
      expect(conservativeConfig.baseDelay).toBe(2000);

      const fastConfig = RetryManager.createPresetConfig('fast');
      expect(fastConfig.strategy).toBe('linear');
      expect(fastConfig.baseDelay).toBe(200);

      const robustConfig = RetryManager.createPresetConfig('robust');
      expect(robustConfig.maxRetries).toBe(7);
      expect(robustConfig.retryableErrors).toContain('file');
    });
  });

  describe('边界情况', () => {
    it('应该处理零重试次数', async () => {
      const mockOperation = jest.fn().mockRejectedValue(new NetworkError('网络错误'));
      
      const result = await retryManager.executeWithRetry(mockOperation, { maxRetries: 0 });
      
      expect(result.success).toBe(false);
      expect(result.attempts).toBe(1);
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    it('应该处理负数延迟', async () => {
      const mockOperation = jest.fn()
        .mockRejectedValueOnce(new NetworkError('网络错误'))
        .mockResolvedValue('success');

      const config: Partial<RetryConfig> = {
        baseDelay: -1000,
        jitter: false
      };

      const result = await retryManager.executeWithRetry(mockOperation, config);
      
      expect(result.success).toBe(true);
    });

    it('应该处理空操作', async () => {
      const mockOperation = jest.fn().mockResolvedValue(undefined);
      
      const result = await retryManager.executeWithRetry(mockOperation);
      
      expect(result.success).toBe(true);
      expect(result.result).toBeUndefined();
    });
  });
});