import { useState, useEffect, useCallback, useRef } from 'react';
import { message } from 'antd';
import {
  DownloadTask,
  CourseResource,
  DownloadStats,
  DownloadProgress as DownloadProgressType
} from '../../shared/types';
import { RendererDownloadManager } from '../services/RendererDownloadManager';

/**
 * 下载状态
 */
export interface DownloadState {
  tasks: DownloadTask[];
  stats: DownloadStats;
  isInitialized: boolean;
  error: string | null;
}

/**
 * 下载Hook返回值
 */
export interface UseDownloadReturn {
  // 状态
  tasks: DownloadTask[];
  stats: DownloadStats;
  isInitialized: boolean;
  error: string | null;
  
  // 操作方法
  downloadResource: (resource: CourseResource) => Promise<DownloadTask>;
  downloadBatch: (resources: CourseResource[]) => Promise<DownloadTask[]>;
  pauseTask: (taskId: string) => void;
  resumeTask: (taskId: string) => void;
  cancelTask: (taskId: string) => void;
  retryTask: (taskId: string) => void;
  clearTask: (taskId: string) => void;
  
  // 批量操作
  pauseAll: () => void;
  resumeAll: () => void;
  cancelAll: () => void;
  clearCompleted: () => void;
  
  // 配置
  updateDownloadPath: (path: string) => void;
  updateConcurrency: (count: number) => void;
  
  // 查询
  getTask: (taskId: string) => DownloadTask | undefined;
  getTasksByStatus: (status: string) => DownloadTask[];
}

/**
 * 下载管理Hook
 */
export const useDownload = (): UseDownloadReturn => {
  const [state, setState] = useState<DownloadState>({
    tasks: [],
    stats: {
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      activeTasks: 0,
      totalDownloadedBytes: 0,
      averageSpeed: 0
    },
    isInitialized: false,
    error: null
  });

  const downloadManagerRef = useRef<RendererDownloadManager | null>(null);

  /**
   * 初始化下载管理器
   */
  const initializeDownloadManager = useCallback(async () => {
    try {
      // 创建渲染进程下载管理器
      const downloadManager = new RendererDownloadManager();
      downloadManagerRef.current = downloadManager;

      setState(prev => ({
        ...prev,
        isInitialized: true,
        error: null
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '初始化下载管理器失败';
      setState(prev => ({
        ...prev,
        error: errorMessage,
        isInitialized: false
      }));
      message.error(errorMessage);
    }
  }, []);

  /**
   * 下载单个资源
   */
  const downloadResource = useCallback(async (resource: CourseResource): Promise<DownloadTask> => {
    if (!downloadManagerRef.current) {
      throw new Error('下载管理器未初始化');
    }

    try {
      const task = downloadManagerRef.current.addTask(resource);
      return task;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '添加下载任务失败';
      message.error(errorMessage);
      throw error;
    }
  }, []);

  /**
   * 批量下载资源
   */
  const downloadBatch = useCallback(async (resources: CourseResource[]): Promise<DownloadTask[]> => {
    if (!downloadManagerRef.current) {
      throw new Error('下载管理器未初始化');
    }

    try {
      const tasks = downloadManagerRef.current.addBatchTasks(resources);
      message.success(`已添加 ${tasks.length} 个下载任务`);
      return tasks;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '批量添加下载任务失败';
      message.error(errorMessage);
      throw error;
    }
  }, []);

  /**
   * 暂停任务
   */
  const pauseTask = useCallback((taskId: string) => {
    if (!downloadManagerRef.current) return;
    
    try {
      downloadManagerRef.current.pauseTask(taskId);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '暂停任务失败';
      message.error(errorMessage);
    }
  }, []);

  /**
   * 恢复任务
   */
  const resumeTask = useCallback((taskId: string) => {
    if (!downloadManagerRef.current) return;
    
    try {
      downloadManagerRef.current.resumeTask(taskId);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '恢复任务失败';
      message.error(errorMessage);
    }
  }, []);

  /**
   * 取消任务
   */
  const cancelTask = useCallback((taskId: string) => {
    if (!downloadManagerRef.current) return;
    
    try {
      downloadManagerRef.current.cancelTask(taskId);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '取消任务失败';
      message.error(errorMessage);
    }
  }, []);

  /**
   * 重试任务
   */
  const retryTask = useCallback((taskId: string) => {
    if (!downloadManagerRef.current) return;
    
    try {
      downloadManagerRef.current.retryTask(taskId);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '重试任务失败';
      message.error(errorMessage);
    }
  }, []);

  /**
   * 清除任务
   */
  const clearTask = useCallback((taskId: string) => {
    if (!downloadManagerRef.current) return;
    
    try {
      downloadManagerRef.current.clearTask(taskId);
      setState(prev => ({
        ...prev,
        tasks: prev.tasks.filter(t => t.id !== taskId)
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '清除任务失败';
      message.error(errorMessage);
    }
  }, []);

  /**
   * 暂停所有任务
   */
  const pauseAll = useCallback(() => {
    if (!downloadManagerRef.current) return;
    downloadManagerRef.current.pauseAll();
    message.info('已暂停所有下载任务');
  }, []);

  /**
   * 恢复所有任务
   */
  const resumeAll = useCallback(() => {
    if (!downloadManagerRef.current) return;
    downloadManagerRef.current.resumeAll();
    message.info('已恢复所有暂停的任务');
  }, []);

  /**
   * 取消所有任务
   */
  const cancelAll = useCallback(() => {
    if (!downloadManagerRef.current) return;
    downloadManagerRef.current.cancelAll();
    message.warning('已取消所有下载任务');
  }, []);

  /**
   * 清除已完成的任务
   */
  const clearCompleted = useCallback(() => {
    if (!downloadManagerRef.current) return;
    downloadManagerRef.current.clearCompletedTasks();
    setState(prev => ({
      ...prev,
      tasks: prev.tasks.filter(t => t.status !== 'completed')
    }));
    message.info('已清除所有已完成的任务');
  }, []);

  /**
   * 更新下载路径
   */
  const updateDownloadPath = useCallback((path: string) => {
    // TODO: 实现更新下载路径的逻辑
    message.info(`下载路径已更新为: ${path}`);
  }, []);

  /**
   * 更新并发数
   */
  const updateConcurrency = useCallback((count: number) => {
    if (!downloadManagerRef.current) return;
    downloadManagerRef.current.updateConfig({ maxConcurrentDownloads: count });
    message.info(`并发下载数已更新为: ${count}`);
  }, []);

  /**
   * 获取任务
   */
  const getTask = useCallback((taskId: string): DownloadTask | undefined => {
    if (!downloadManagerRef.current) return undefined;
    return downloadManagerRef.current.getTask(taskId);
  }, []);

  /**
   * 根据状态获取任务
   */
  const getTasksByStatus = useCallback((status: string): DownloadTask[] => {
    if (!downloadManagerRef.current) return [];
    return downloadManagerRef.current.getTasksByStatus(status as any);
  }, []);

  // 初始化
  useEffect(() => {
    initializeDownloadManager();

    // 清理函数
    return () => {
      if (downloadManagerRef.current) {
        downloadManagerRef.current.destroy();
        downloadManagerRef.current = null;
      }
    };
  }, [initializeDownloadManager]);

  return {
    // 状态
    tasks: state.tasks,
    stats: state.stats,
    isInitialized: state.isInitialized,
    error: state.error,
    
    // 操作方法
    downloadResource,
    downloadBatch,
    pauseTask,
    resumeTask,
    cancelTask,
    retryTask,
    clearTask,
    
    // 批量操作
    pauseAll,
    resumeAll,
    cancelAll,
    clearCompleted,
    
    // 配置
    updateDownloadPath,
    updateConcurrency,
    
    // 查询
    getTask,
    getTasksByStatus
  };
};
