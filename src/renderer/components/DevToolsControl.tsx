import React, { useState, useEffect } from 'react';
import { Button, Space, Typography, Card } from 'antd';
import { BugOutlined, EyeOutlined, EyeInvisibleOutlined } from '@ant-design/icons';

const { Text } = Typography;

/**
 * DevTools 控制组件
 * 仅在开发环境中显示，用于控制开发者工具
 */
export const DevToolsControl: React.FC = () => {
  const [isDevToolsOpened, setIsDevToolsOpened] = useState(false);
  const [loading, setLoading] = useState(false);

  // 仅在开发环境中显示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  // 检查 DevTools 状态
  useEffect(() => {
    const checkDevToolsStatus = async () => {
      try {
        if (window.electronAPI?.isDevToolsOpened) {
          const opened = await window.electronAPI.isDevToolsOpened();
          setIsDevToolsOpened(opened);
        }
      } catch (error) {
        console.error('Failed to check DevTools status:', error);
      }
    };

    checkDevToolsStatus();
    
    // 定期检查状态
    const interval = setInterval(checkDevToolsStatus, 2000);
    return () => clearInterval(interval);
  }, []);

  // 切换 DevTools
  const handleToggleDevTools = async () => {
    if (!window.electronAPI?.toggleDevTools) {
      console.warn('DevTools API not available');
      return;
    }

    setLoading(true);
    try {
      await window.electronAPI.toggleDevTools();
      
      // 延迟更新状态，给 DevTools 时间打开/关闭
      setTimeout(async () => {
        try {
          const opened = await window.electronAPI.isDevToolsOpened();
          setIsDevToolsOpened(opened);
        } catch (error) {
          console.error('Failed to update DevTools status:', error);
        }
        setLoading(false);
      }, 500);
    } catch (error) {
      console.error('Failed to toggle DevTools:', error);
      setLoading(false);
    }
  };

  return (
    <Card 
      size="small" 
      title={
        <Space>
          <BugOutlined />
          <Text strong>开发工具</Text>
        </Space>
      }
      style={{ 
        position: 'fixed', 
        top: 10, 
        right: 10, 
        zIndex: 1000,
        minWidth: 200,
        opacity: 0.9
      }}
    >
      <Space direction="vertical" size="small" style={{ width: '100%' }}>
        <Text type="secondary" style={{ fontSize: '12px' }}>
          状态: {isDevToolsOpened ? '已打开' : '已关闭'}
        </Text>
        
        <Button
          type="primary"
          size="small"
          icon={isDevToolsOpened ? <EyeInvisibleOutlined /> : <EyeOutlined />}
          loading={loading}
          onClick={handleToggleDevTools}
          block
        >
          {isDevToolsOpened ? '关闭' : '打开'} DevTools
        </Button>

        <Text type="secondary" style={{ fontSize: '11px', lineHeight: '1.2' }}>
          💡 提示: 也可以使用 F12 或 Ctrl+Shift+I 快捷键
        </Text>
      </Space>
    </Card>
  );
};

export default DevToolsControl;
