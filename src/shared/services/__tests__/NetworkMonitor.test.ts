import { NetworkMonitor, NetworkStatus, DownloadStrategy } from '../NetworkMonitor';

// Mock fetch
global.fetch = jest.fn();
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

describe('NetworkMonitor', () => {
  let monitor: NetworkMonitor;

  beforeEach(() => {
    monitor = new NetworkMonitor(1000); // 1秒检查间隔
    jest.clearAllMocks();
  });

  afterEach(() => {
    monitor.stopMonitoring();
  });

  describe('checkNetworkStatus', () => {
    it('should return current network status', async () => {
      const status = await monitor.checkNetworkStatus();

      expect(status).toHaveProperty('isOnline');
      expect(status).toHaveProperty('latency');
      expect(status).toHaveProperty('stability');
      expect(typeof status.isOnline).toBe('boolean');
      expect(typeof status.latency).toBe('number');
    });
  });

  describe('getDownloadStrategy', () => {
    it('should return conservative strategy for offline status', () => {
      const offlineStatus: NetworkStatus = {
        isOnline: false,
        connectionType: 'unknown',
        speed: { download: 0, upload: 0, ping: 0 },
        latency: 0,
        stability: 0
      };

      // Set the current status
      (monitor as any).currentStatus = offlineStatus;

      const strategy = monitor.getDownloadStrategy();

      expect(strategy).toEqual({
        maxConcurrentDownloads: 0,
        chunkSize: 0,
        retryDelay: 5000,
        timeoutMs: 30000,
        useCompression: false
      });
    });

    it('should return aggressive strategy for high-speed stable network', () => {
      const highSpeedStatus: NetworkStatus = {
        isOnline: true,
        connectionType: 'wifi',
        speed: { download: 100, upload: 20, ping: 20 },
        latency: 20,
        stability: 0.9
      };

      (monitor as any).currentStatus = highSpeedStatus;

      const strategy = monitor.getDownloadStrategy();

      expect(strategy.maxConcurrentDownloads).toBe(8);
      expect(strategy.chunkSize).toBe(5 * 1024 * 1024); // 5MB
      expect(strategy.retryDelay).toBe(500);
      expect(strategy.useCompression).toBe(false);
    });

    it('should return conservative strategy for low-speed unstable network', () => {
      const lowSpeedStatus: NetworkStatus = {
        isOnline: true,
        connectionType: 'cellular',
        speed: { download: 2, upload: 0.5, ping: 200 },
        latency: 600,
        stability: 0.3
      };

      (monitor as any).currentStatus = lowSpeedStatus;

      const strategy = monitor.getDownloadStrategy();

      expect(strategy.maxConcurrentDownloads).toBe(1);
      expect(strategy.chunkSize).toBe(512 * 1024); // 512KB
      expect(strategy.retryDelay).toBe(3000);
      expect(strategy.useCompression).toBe(true);
    });
  });

  describe('getNetworkQuality', () => {
    it('should return excellent for high-speed stable network', () => {
      const excellentStatus: NetworkStatus = {
        isOnline: true,
        connectionType: 'ethernet',
        speed: { download: 100, upload: 50, ping: 10 },
        latency: 10,
        stability: 0.95
      };

      (monitor as any).currentStatus = excellentStatus;

      const quality = monitor.getNetworkQuality();
      expect(quality).toBe('excellent');
    });

    it('should return poor for low-speed unstable network', () => {
      const poorStatus: NetworkStatus = {
        isOnline: true,
        connectionType: 'cellular',
        speed: { download: 1, upload: 0.2, ping: 500 },
        latency: 800,
        stability: 0.2
      };

      (monitor as any).currentStatus = poorStatus;

      const quality = monitor.getNetworkQuality();
      expect(quality).toBe('poor');
    });

    it('should return offline when not connected', () => {
      const offlineStatus: NetworkStatus = {
        isOnline: false,
        connectionType: 'unknown',
        speed: { download: 0, upload: 0, ping: 0 },
        latency: 0,
        stability: 0
      };

      (monitor as any).currentStatus = offlineStatus;

      const quality = monitor.getNetworkQuality();
      expect(quality).toBe('offline');
    });
  });

  describe('startMonitoring', () => {
    it('should start and stop monitoring without errors', async () => {
      await monitor.startMonitoring();
      expect(true).toBe(true); // Should not throw
      monitor.stopMonitoring();
    });
  });

  describe('getNetworkSuggestions', () => {
    it('should provide suggestions for offline network', () => {
      const offlineStatus: NetworkStatus = {
        isOnline: false,
        connectionType: 'unknown',
        speed: { download: 0, upload: 0, ping: 0 },
        latency: 0,
        stability: 0
      };

      (monitor as any).currentStatus = offlineStatus;

      const suggestions = monitor.getNetworkSuggestions();
      expect(suggestions).toContain('网络连接已断开，请检查网络设置');
    });

    it('should provide suggestions for slow network', () => {
      const slowStatus: NetworkStatus = {
        isOnline: true,
        connectionType: 'cellular',
        speed: { download: 2, upload: 0.5, ping: 100 },
        latency: 100,
        stability: 0.8
      };

      (monitor as any).currentStatus = slowStatus;

      const suggestions = monitor.getNetworkSuggestions();
      expect(suggestions).toContain('网络速度较慢，建议减少并发下载数量');
    });

    it('should provide suggestions for high latency network', () => {
      const highLatencyStatus: NetworkStatus = {
        isOnline: true,
        connectionType: 'wifi',
        speed: { download: 20, upload: 5, ping: 200 },
        latency: 600,
        stability: 0.7
      };

      (monitor as any).currentStatus = highLatencyStatus;

      const suggestions = monitor.getNetworkSuggestions();
      expect(suggestions).toContain('网络延迟较高，可能影响下载稳定性');
    });

    it('should provide suggestions for unstable network', () => {
      const unstableStatus: NetworkStatus = {
        isOnline: true,
        connectionType: 'wifi',
        speed: { download: 15, upload: 3, ping: 50 },
        latency: 50,
        stability: 0.3
      };

      (monitor as any).currentStatus = unstableStatus;

      const suggestions = monitor.getNetworkSuggestions();
      expect(suggestions).toContain('网络不稳定，建议启用断点续传功能');
    });

    it('should indicate good network status', () => {
      const goodStatus: NetworkStatus = {
        isOnline: true,
        connectionType: 'ethernet',
        speed: { download: 50, upload: 10, ping: 20 },
        latency: 20,
        stability: 0.9
      };

      (monitor as any).currentStatus = goodStatus;

      const suggestions = monitor.getNetworkSuggestions();
      expect(suggestions).toContain('网络状态良好');
    });
  });

  describe('shouldPauseDownloads', () => {
    it('should return true for offline network', () => {
      const offlineStatus: NetworkStatus = {
        isOnline: false,
        connectionType: 'unknown',
        speed: { download: 0, upload: 0, ping: 0 },
        latency: 0,
        stability: 0
      };

      (monitor as any).currentStatus = offlineStatus;

      expect(monitor.shouldPauseDownloads()).toBe(true);
    });

    it('should return true for poor network quality', () => {
      const poorStatus: NetworkStatus = {
        isOnline: true,
        connectionType: 'cellular',
        speed: { download: 1, upload: 0.1, ping: 800 },
        latency: 1000,
        stability: 0.1
      };

      (monitor as any).currentStatus = poorStatus;

      expect(monitor.shouldPauseDownloads()).toBe(true);
    });

    it('should return false for good network quality', () => {
      const goodStatus: NetworkStatus = {
        isOnline: true,
        connectionType: 'wifi',
        speed: { download: 25, upload: 5, ping: 30 },
        latency: 30,
        stability: 0.8
      };

      (monitor as any).currentStatus = goodStatus;

      expect(monitor.shouldPauseDownloads()).toBe(false);
    });
  });

  describe('performance tests', () => {
    it('should handle multiple concurrent status checks', async () => {
      const promises = Array(10).fill(0).map(() => 
        monitor.checkNetworkStatus()
      );

      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(10);
      results.forEach(result => {
        expect(result).toHaveProperty('isOnline');
        expect(result).toHaveProperty('latency');
      });
    });

    it('should complete network check within reasonable time', async () => {
      const startTime = Date.now();
      await monitor.checkNetworkStatus();
      const elapsed = Date.now() - startTime;

      expect(elapsed).toBeLessThan(2000); // Should complete within 2 seconds
    });

    it('should maintain speed history efficiently', () => {
      const history = monitor.getSpeedHistory();
      expect(Array.isArray(history)).toBe(true);
      expect(history.length).toBeLessThanOrEqual(50); // Should not exceed max history size
    });
  });
});