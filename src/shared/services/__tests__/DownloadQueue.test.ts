import { DownloadQueue, TaskStatus, DownloadTask, DownloadProgress } from '../DownloadQueue';

describe('DownloadQueue', () => {
  let queue: DownloadQueue;
  
  const mockResource = {
    id: 'resource-1',
    title: '测试资源',
    type: 'video' as const,
    url: 'https://example.com/video.m3u8',
    metadata: {
      stage: '小学',
      grade: '一年级',
      subject: '语文',
      version: '人教版',
      volume: '上册',
      chapter: '第一章',
      lesson: '第一课',
      fileSize: 1024000,
      duration: 3600
    },
    requiresAuth: false
  };

  beforeEach(() => {
    queue = new DownloadQueue({
      maxConcurrentDownloads: 2,
      retryAttempts: 3,
      retryDelay: 1000
    });
  });

  afterEach(() => {
    queue.removeAllListeners();
  });

  describe('任务添加和基本操作', () => {
    test('应该能够添加任务到队列', () => {
      const taskId = queue.addTask({
        resource: mockResource,
        requiresAuth: false
      });

      expect(taskId).toBeDefined();
      expect(typeof taskId).toBe('string');
      
      const task = queue.getTask(taskId);
      expect(task).toBeDefined();
      // 任务添加后会立即开始下载，所以状态可能是 downloading
      expect([TaskStatus.PENDING, TaskStatus.DOWNLOADING]).toContain(task?.status);
      expect(task?.resource.title).toBe('测试资源');
    });

    test('应该能够获取所有任务', () => {
      const taskId1 = queue.addTask({ resource: mockResource, requiresAuth: false });
      const taskId2 = queue.addTask({ resource: { ...mockResource, id: 'resource-2' }, requiresAuth: true });

      const allTasks = queue.getAllTasks();
      expect(allTasks).toHaveLength(2);
      expect(allTasks.map(t => t.id)).toContain(taskId1);
      expect(allTasks.map(t => t.id)).toContain(taskId2);
    });

    test('应该能够根据状态获取任务', () => {
      const taskId1 = queue.addTask({ resource: mockResource, requiresAuth: false });
      const taskId2 = queue.addTask({ resource: { ...mockResource, id: 'resource-2' }, requiresAuth: false });

      // 暂停一个任务
      queue.pauseTask(taskId1);

      const pausedTasks = queue.getTasksByStatus(TaskStatus.PAUSED);
      const allTasks = queue.getAllTasks();

      expect(allTasks).toHaveLength(2);
      expect(pausedTasks).toHaveLength(1);
      expect(pausedTasks[0].id).toBe(taskId1);
    });
  });

  describe('任务状态管理', () => {
    test('应该能够暂停正在下载的任务', () => {
      const taskId = queue.addTask({ resource: mockResource, requiresAuth: false });
      
      // 任务添加后会立即开始下载
      const paused = queue.pauseTask(taskId);
      expect(paused).toBe(true);
      expect(queue.getTaskStatus(taskId)).toBe(TaskStatus.PAUSED);
    });

    test('应该能够恢复暂停的任务', (done) => {
      const taskId = queue.addTask({ resource: mockResource, requiresAuth: false });
      
      // 先暂停任务
      queue.pauseTask(taskId);
      
      queue.on('taskResumed', (task) => {
        expect(task.id).toBe(taskId);
        expect(task.status).toBe(TaskStatus.PENDING);
        done();
      });
      
      const resumed = queue.resumeTask(taskId);
      expect(resumed).toBe(true);
    });

    test('应该能够取消任务', (done) => {
      const taskId = queue.addTask({ resource: mockResource, requiresAuth: false });
      
      queue.on('taskCancelled', (task) => {
        expect(task.id).toBe(taskId);
        expect(task.status).toBe(TaskStatus.CANCELLED);
        done();
      });
      
      const cancelled = queue.cancelTask(taskId);
      expect(cancelled).toBe(true);
    });

    test('应该能够标记任务完成', (done) => {
      const taskId = queue.addTask({ resource: mockResource, requiresAuth: false });
      
      queue.on('taskCompleted', (task) => {
        expect(task.id).toBe(taskId);
        expect(task.status).toBe(TaskStatus.COMPLETED);
        done();
      });
      
      queue.completeTask(taskId);
    });

    test('应该能够标记任务失败', (done) => {
      const taskId = queue.addTask({ resource: mockResource, requiresAuth: false });
      const errorMessage = '网络连接失败';
      
      queue.on('taskFailed', (task) => {
        expect(task.id).toBe(taskId);
        expect(task.status).toBe(TaskStatus.FAILED);
        expect(task.error).toBe(errorMessage);
        done();
      });
      
      queue.failTask(taskId, errorMessage);
    });
  });

  describe('并发控制', () => {
    test('应该限制并发下载数量', () => {
      const taskId1 = queue.addTask({ resource: mockResource, requiresAuth: false });
      const taskId2 = queue.addTask({ resource: { ...mockResource, id: 'resource-2' }, requiresAuth: false });
      const taskId3 = queue.addTask({ resource: { ...mockResource, id: 'resource-3' }, requiresAuth: false });

      // 应该只有2个任务开始（maxConcurrentDownloads = 2）
      const downloadingTasks = queue.getTasksByStatus(TaskStatus.DOWNLOADING);
      const pendingTasks = queue.getTasksByStatus(TaskStatus.PENDING);
      
      expect(downloadingTasks.length + pendingTasks.length).toBe(3);
      expect(downloadingTasks.length).toBeLessThanOrEqual(2);
    });

    test('完成任务后应该自动开始下一个任务', () => {
      const taskId1 = queue.addTask({ resource: mockResource, requiresAuth: false });
      const taskId2 = queue.addTask({ resource: { ...mockResource, id: 'resource-2' }, requiresAuth: false });
      const taskId3 = queue.addTask({ resource: { ...mockResource, id: 'resource-3' }, requiresAuth: false });

      // 完成第一个任务
      queue.completeTask(taskId1);
      
      const downloadingTasks = queue.getTasksByStatus(TaskStatus.DOWNLOADING);
      const completedTasks = queue.getTasksByStatus(TaskStatus.COMPLETED);
      
      expect(completedTasks).toHaveLength(1);
      expect(downloadingTasks.length).toBeGreaterThan(0);
    });
  });

  describe('进度更新', () => {
    test('应该能够更新任务进度', (done) => {
      const taskId = queue.addTask({ resource: mockResource, requiresAuth: false });
      
      const progress: DownloadProgress = {
        taskId,
        progress: 50,
        speed: 1024000,
        estimatedTime: 30,
        downloadedBytes: 512000,
        totalBytes: 1024000
      };

      queue.on('taskProgress', (task, progressData) => {
        expect(task.id).toBe(taskId);
        expect(task.progress).toBe(50);
        expect(task.speed).toBe(1024000);
        expect(task.estimatedTime).toBe(30);
        expect(progressData).toEqual(progress);
        done();
      });

      queue.updateTaskProgress(taskId, progress);
    });
  });

  describe('批量操作', () => {
    test('应该能够暂停所有任务', () => {
      const taskId1 = queue.addTask({ resource: mockResource, requiresAuth: false });
      const taskId2 = queue.addTask({ resource: { ...mockResource, id: 'resource-2' }, requiresAuth: false });

      // 等待任务开始
      setTimeout(() => {
        queue.pauseAll();
        
        const pausedTasks = queue.getTasksByStatus(TaskStatus.PAUSED);
        expect(pausedTasks.length).toBeGreaterThan(0);
      }, 10);
    });

    test('应该能够恢复所有暂停的任务', () => {
      const taskId1 = queue.addTask({ resource: mockResource, requiresAuth: false });
      const taskId2 = queue.addTask({ resource: { ...mockResource, id: 'resource-2' }, requiresAuth: false });

      // 暂停所有任务
      queue.pauseAll();
      
      // 恢复所有任务
      queue.resumeAll();
      
      const pausedTasks = queue.getTasksByStatus(TaskStatus.PAUSED);
      expect(pausedTasks).toHaveLength(0);
    });

    test('应该能够清空已完成的任务', (done) => {
      const taskId1 = queue.addTask({ resource: mockResource, requiresAuth: false });
      const taskId2 = queue.addTask({ resource: { ...mockResource, id: 'resource-2' }, requiresAuth: false });

      // 完成第一个任务，取消第二个任务
      queue.completeTask(taskId1);
      queue.cancelTask(taskId2);

      let removedCount = 0;
      queue.on('taskRemoved', () => {
        removedCount++;
        if (removedCount === 2) {
          expect(queue.getAllTasks()).toHaveLength(0);
          done();
        }
      });

      queue.clearCompletedTasks();
    });
  });

  describe('队列统计', () => {
    test('应该能够获取队列统计信息', () => {
      const taskId1 = queue.addTask({ resource: mockResource, requiresAuth: false });
      const taskId2 = queue.addTask({ resource: { ...mockResource, id: 'resource-2' }, requiresAuth: false });
      const taskId3 = queue.addTask({ resource: { ...mockResource, id: 'resource-3' }, requiresAuth: false });

      // 设置不同的任务状态
      queue.pauseTask(taskId1);
      queue.completeTask(taskId2);
      queue.failTask(taskId3, '测试错误');

      const stats = queue.getQueueStats();
      expect(stats.total).toBe(3);
      expect(stats.paused).toBe(1);
      expect(stats.completed).toBe(1);
      expect(stats.failed).toBe(1);
    });
  });

  describe('错误处理', () => {
    test('对不存在的任务操作应该返回false或null', () => {
      const nonExistentId = 'non-existent-task';
      
      expect(queue.pauseTask(nonExistentId)).toBe(false);
      expect(queue.resumeTask(nonExistentId)).toBe(false);
      expect(queue.cancelTask(nonExistentId)).toBe(false);
      expect(queue.getTask(nonExistentId)).toBe(null);
      expect(queue.getTaskStatus(nonExistentId)).toBe(null);
    });

    test('对已完成任务的操作应该返回false', () => {
      const taskId = queue.addTask({ resource: mockResource, requiresAuth: false });
      queue.completeTask(taskId);

      expect(queue.pauseTask(taskId)).toBe(false);
      expect(queue.cancelTask(taskId)).toBe(false);
    });

    test('对已取消任务的操作应该返回false', () => {
      const taskId = queue.addTask({ resource: mockResource, requiresAuth: false });
      queue.cancelTask(taskId);

      expect(queue.pauseTask(taskId)).toBe(false);
      expect(queue.cancelTask(taskId)).toBe(false);
    });
  });

  describe('事件系统', () => {
    test('应该在任务添加时触发事件', (done) => {
      queue.on('taskAdded', (task) => {
        expect(task.resource.title).toBe('测试资源');
        expect(task.status).toBe(TaskStatus.PENDING);
        done();
      });

      queue.addTask({ resource: mockResource, requiresAuth: false });
    });

    test('应该在任务状态变化时触发事件', (done) => {
      const taskId = queue.addTask({ resource: mockResource, requiresAuth: false });

      queue.on('taskStatusChanged', (task, oldStatus, newStatus) => {
        expect(task.id).toBe(taskId);
        expect(oldStatus).toBeDefined();
        expect(newStatus).toBeDefined();
        done();
      });
      
      // 触发状态变化
      queue.pauseTask(taskId);
    });
  });
});