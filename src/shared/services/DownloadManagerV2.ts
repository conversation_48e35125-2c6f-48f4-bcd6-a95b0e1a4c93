import { EventEmitter } from 'events';
import {
  DownloadTask,
  CourseResource,
  TaskStatus,
  DownloadProgress
} from '../types';
import { TextbookDownloader } from './TextbookDownloader';
import { M3U8Downloader } from './M3U8Downloader';
import { FileOrganizer } from './FileOrganizer';
import { DownloadQueue, TaskPriority } from './DownloadQueue';
import { TaskStateManager } from './TaskStateManager';
import { ConcurrencyController } from './ConcurrencyController';

/**
 * 下载管理器配置
 */
export interface DownloadManagerConfig {
  maxConcurrentDownloads: number;
  autoRetry: boolean;
  maxRetries: number;
  retryDelay: number;
  enableNotifications: boolean;
  enablePriority: boolean;
  persistState: boolean;
}

/**
 * 下载统计信息
 */
export interface DownloadStats {
  totalTasks: number;
  completedTasks: number;
  failedTasks: number;
  activeTasks: number;
  pendingTasks: number;
  pausedTasks: number;
  cancelledTasks: number;
  totalDownloadedBytes: number;
  averageSpeed: number;
  estimatedTimeRemaining: number;
}

/**
 * 下载管理器事件
 */
export interface DownloadManagerEvents {
  'task-added': (task: DownloadTask) => void;
  'task-started': (task: DownloadTask) => void;
  'task-progress': (task: DownloadTask, progress: DownloadProgress) => void;
  'task-completed': (task: DownloadTask) => void;
  'task-failed': (task: DownloadTask, error: string) => void;
  'task-cancelled': (task: DownloadTask) => void;
  'task-paused': (task: DownloadTask) => void;
  'task-resumed': (task: DownloadTask) => void;
  'queue-empty': () => void;
  'stats-updated': (stats: DownloadStats) => void;
  'concurrency-changed': (oldValue: number, newValue: number) => void;
}

/**
 * 下载管理器 V2
 * 使用 DownloadQueue、TaskStateManager 和 ConcurrencyController 进行任务管理
 */
export class DownloadManagerV2 extends EventEmitter {
  private config: DownloadManagerConfig;
  private textbookDownloader: TextbookDownloader;
  private m3u8Downloader: M3U8Downloader;
  private fileOrganizer: FileOrganizer;
  private queue: DownloadQueue;
  private stateManager: TaskStateManager;
  private concurrencyController: ConcurrencyController;
  private tasks: Map<string, DownloadTask> = new Map();
  private downloadedBytes: Map<string, number> = new Map();
  private isInitialized: boolean = false;

  private readonly defaultConfig: DownloadManagerConfig = {
    maxConcurrentDownloads: 3,
    autoRetry: true,
    maxRetries: 3,
    retryDelay: 5000, // 5 seconds
    enableNotifications: true,
    enablePriority: true,
    persistState: true
  };

  constructor(
    fileOrganizer: FileOrganizer,
    config?: Partial<DownloadManagerConfig>
  ) {
    super();
    this.config = { ...this.defaultConfig, ...config };
    this.fileOrganizer = fileOrganizer;
    this.textbookDownloader = new TextbookDownloader(fileOrganizer);
    this.m3u8Downloader = new M3U8Downloader(fileOrganizer);
    
    // 初始化队列管理器
    this.queue = new DownloadQueue({
      maxConcurrentDownloads: this.config.maxConcurrentDownloads,
      retryAttempts: this.config.maxRetries,
      retryDelay: this.config.retryDelay
    });
    
    // 初始化状态管理器
    this.stateManager = new TaskStateManager({
      enableHistory: true,
      maxHistorySize: 1000,
      persistHistory: this.config.persistState,
      validateTransitions: true
    });
    
    // 初始化并发控制器
    this.concurrencyController = new ConcurrencyController({
      maxConcurrent: this.config.maxConcurrentDownloads,
      enableDynamicAdjustment: true,
      resourceThresholds: {
        maxMemoryUsage: 1024, // 1GB
        maxCpuUsage: 80, // 80%
        maxNetworkBandwidth: 50, // 50MB/s
        maxDiskIO: 100 // 100MB/s
      },
      adaptiveStrategy: 'balanced'
    });
    
    this.setupEventListeners();
    this.isInitialized = true;
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 队列事件
    this.queue.on('item-added', (item) => {
      this.emitStatsUpdate();
    });
    
    this.queue.on('item-processing', (taskId) => {
      this.startDownloadTask(taskId);
    });
    
    this.queue.on('queue-empty', () => {
      this.emit('queue-empty');
    });
    
    // 状态管理器事件
    this.stateManager.on('state-changed', (taskId, fromStatus, toStatus) => {
      const task = this.tasks.get(taskId);
      if (task) {
        task.status = toStatus;
        task.updatedAt = new Date();
        
        // 根据状态变化触发相应事件
        if (toStatus === 'downloading' && fromStatus !== 'downloading') {
          this.emit('task-started', task);
        } else if (toStatus === 'completed') {
          this.emit('task-completed', task);
          if (this.config.enableNotifications) {
            this.showNotification(`下载完成: ${task.resource.title}`);
          }
        } else if (toStatus === 'failed') {
          this.emit('task-failed', task, task.error || '未知错误');
        } else if (toStatus === 'paused') {
          this.emit('task-paused', task);
        } else if (toStatus === 'cancelled') {
          this.emit('task-cancelled', task);
        }
      }
      
      this.emitStatsUpdate();
    });
    
    // 并发控制器事件
    this.concurrencyController.on('concurrency-adjusted', (oldLimit, newLimit) => {
      this.config.maxConcurrentDownloads = newLimit;
      this.queue.updateConfig({ maxConcurrentDownloads: newLimit });
      this.emit('concurrency-changed', oldLimit, newLimit);
    });
  }

  /**
   * 添加下载任务
   */
  addTask(resource: CourseResource, priority: TaskPriority = TaskPriority.NORMAL): DownloadTask {
    const taskId = this.generateTaskId(resource);
    
    // 检查是否已存在相同任务
    if (this.tasks.has(taskId)) {
      const existingTask = this.tasks.get(taskId)!;
      if (existingTask.status === 'downloading' || existingTask.status === 'pending') {
        throw new Error('该资源已在下载队列中');
      }
    }

    const task: DownloadTask = {
      id: taskId,
      resource,
      status: 'pending',
      progress: 0,
      speed: 0,
      estimatedTime: 0,
      requiresAuth: resource.requiresAuth,
      createdAt: new Date(),
      updatedAt: new Date(),
      outputPath: '',
      retryCount: 0,
      maxRetries: this.config.maxRetries
    };

    // 保存任务信息
    this.tasks.set(taskId, task);
    
    // 初始化任务状态
    this.stateManager.initializeTask(taskId, 'pending');
    
    // 添加到下载队列
    this.queue.addTask(taskId);

    this.emit('task-added', task);
    this.emitStatsUpdate();

    return task;
  }

  /**
   * 批量添加下载任务
   */
  addBatchTasks(resources: CourseResource[], priority: TaskPriority = TaskPriority.NORMAL): DownloadTask[] {
    const tasks: DownloadTask[] = [];
    
    for (const resource of resources) {
      try {
        const task = this.addTask(resource, priority);
        tasks.push(task);
      } catch (error) {
        console.warn(`添加任务失败: ${resource.title}`, error);
      }
    }

    return tasks;
  }

  /**
   * 暂停任务
   */
  async pauseTask(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error('任务不存在');
    }

    if (task.status === 'downloading') {
      // 根据资源类型暂停下载
      if (task.resource.type === 'video') {
        await this.m3u8Downloader.pauseDownload(taskId);
        // 检查是否可以断点续传
        task.canResume = await this.m3u8Downloader.canResumeTask(taskId);
      } else {
        this.textbookDownloader.cancelDownload(taskId);
        task.canResume = false;
      }

      // 更新任务状态
      this.stateManager.transitionState(taskId, 'paused', '用户暂停');
      
      // 释放并发槽位
      const slotId = this.concurrencyController.getSlotForTask(taskId);
      if (slotId) {
        this.concurrencyController.releaseSlot(slotId);
      }
    }
  }

  /**
   * 恢复任务
   */
  async resumeTask(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error('任务不存在');
    }

    if (task.status === 'paused') {
      // 如果是视频任务且支持断点续传，直接恢复下载
      if (task.resource.type === 'video' && task.canResume) {
        this.stateManager.transitionState(taskId, 'resuming', '恢复下载');
        task.lastResumeAt = new Date();

        if (task.resumeState) {
          task.resumeState.resumeCount++;
        }

        try {
          // 直接恢复下载，不通过队列
          await this.resumeVideoDownload(task);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '恢复下载失败';
          task.error = errorMessage;
          this.stateManager.transitionState(taskId, 'failed', errorMessage);
        }
      } else {
        // 普通恢复，重新加入队列
        this.stateManager.transitionState(taskId, 'pending', '重新加入队列');
        
        // 将任务重新加入队列，优先级高
        this.queue.addTask(taskId);
      }
    }
  }

  /**
   * 取消任务
   */
  cancelTask(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error('任务不存在');
    }

    if (task.status === 'downloading') {
      // 取消下载
      if (task.resource.type === 'video') {
        this.m3u8Downloader.cancelDownload(taskId);
      } else {
        this.textbookDownloader.cancelDownload(taskId);
      }
      
      // 释放并发槽位
      const slotId = this.concurrencyController.getSlotForTask(taskId);
      if (slotId) {
        this.concurrencyController.releaseSlot(slotId);
      }
    }

    // 从队列中移除
    this.queue.removeTask(taskId);
    
    // 更新状态
    this.stateManager.transitionState(taskId, 'cancelled', '用户取消');
  }

  /**
   * 重试任务
   */
  retryTask(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (!task) {
      throw new Error('任务不存在');
    }

    if (task.status === 'failed') {
      // 重置任务状态
      task.retryCount = 0;
      task.error = undefined;

      // 更新状态
      this.stateManager.transitionState(taskId, 'pending', '用户重试');

      // 将任务重新加入队列
      this.queue.addTask(taskId);
    }
  }

  /**
   * 清除任务
   */
  clearTask(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (!task) {
      return;
    }

    // 如果任务正在下载，先取消
    if (task.status === 'downloading') {
      this.cancelTask(taskId);
    }

    // 从队列中移除
    this.queue.removeTask(taskId);

    // 从状态管理器中移除
    this.stateManager.removeTask(taskId);

    // 删除任务
    this.tasks.delete(taskId);
    this.downloadedBytes.delete(taskId);

    this.emitStatsUpdate();
  }

  /**
   * 开始下载任务
   */
  private async startDownloadTask(taskId: string): Promise<void> {
    const task = this.tasks.get(taskId);
    if (!task || task.status !== 'pending') {
      return;
    }

    // 请求并发槽位
    const slotId = await this.concurrencyController.acquireSlot(taskId);
    if (!slotId) {
      // 无法获取槽位，任务保持在队列中
      return;
    }

    // 更新任务状态
    this.stateManager.transitionState(taskId, 'downloading', '开始下载');

    try {
      // 根据资源类型选择下载器
      if (task.resource.type === 'video') {
        // 使用M3U8下载器下载视频
        const outputPath = await this.m3u8Downloader.downloadVideo(
          task.resource,
          (progress: DownloadProgress) => {
            // 更新任务进度
            task.progress = progress.progress;
            task.speed = progress.speed;
            task.estimatedTime = progress.estimatedTime;
            task.updatedAt = new Date();

            // 更新下载字节数
            if (progress.downloadedBytes) {
              this.downloadedBytes.set(taskId, progress.downloadedBytes);
            }

            // 更新断点续传状态
            if (progress.segmentsCompleted !== undefined && progress.segmentsTotal !== undefined) {
              if (task.resumeState) {
                task.resumeState.downloadedSegments = progress.segmentsCompleted;
                task.resumeState.totalSegments = progress.segmentsTotal;
                task.resumeState.lastSavedAt = new Date();
              }
            }

            // 更新并发控制器活动时间
            this.concurrencyController.updateSlotActivity(slotId);

            this.emit('task-progress', task, progress);
          }
        );

        task.outputPath = outputPath;
      } else {
        // 使用教材下载器下载其他类型资源
        const downloadResult = await this.textbookDownloader.downloadTextbook(
          task.resource,
          (progress: DownloadProgress) => {
            // 更新任务进度
            task.progress = progress.progress;
            task.speed = progress.speed;
            task.estimatedTime = progress.estimatedTime;
            task.updatedAt = new Date();

            // 更新下载字节数
            if (progress.downloadedBytes) {
              this.downloadedBytes.set(taskId, progress.downloadedBytes);
            }

            // 更新并发控制器活动时间
            this.concurrencyController.updateSlotActivity(slotId);

            this.emit('task-progress', task, progress);
          }
        );

        // Extract output path from the download result
        task.outputPath = this.fileOrganizer.generatePath(task.resource);
      }

      // 下载完成
      task.progress = 100;
      task.updatedAt = new Date();
      this.stateManager.transitionState(taskId, 'completed', '下载完成');

      // 释放并发槽位
      this.concurrencyController.releaseSlot(slotId);

      // 标记队列任务完成
      this.queue.markTaskCompleted(taskId);

    } catch (error) {
      // 下载失败
      const errorMessage = error instanceof Error ? error.message : '下载失败';
      task.error = errorMessage;
      task.retryCount++;
      task.updatedAt = new Date();

      // 释放并发槽位
      this.concurrencyController.releaseSlot(slotId);

      // 检查是否需要自动重试
      if (this.config.autoRetry && task.retryCount < task.maxRetries) {
        // 延迟后重新加入队列
        setTimeout(() => {
          this.stateManager.transitionState(taskId, 'pending', '自动重试');
          this.queue.addTask(taskId as any);
        }, this.config.retryDelay);
      } else {
        this.stateManager.transitionState(taskId, 'failed', errorMessage);
        this.queue.markTaskFailed(taskId, errorMessage);
      }
    }
  }

  /**
   * 恢复视频下载
   */
  private async resumeVideoDownload(task: DownloadTask): Promise<void> {
    try {
      // 请求并发槽位
      const slotId = await this.concurrencyController.acquireSlot(task.id);
      if (!slotId) {
        throw new Error('无法获取并发槽位');
      }

      const outputPath = await this.m3u8Downloader.resumeDownload(
        task.resource,
        (progress: DownloadProgress) => {
          // 更新任务进度
          task.progress = progress.progress;
          task.speed = progress.speed;
          task.estimatedTime = progress.estimatedTime;
          task.updatedAt = new Date();

          // 更新状态为下载中
          if (task.status !== 'downloading') {
            this.stateManager.transitionState(task.id, 'downloading', '恢复下载中');
          }

          // 更新下载字节数
          if (progress.downloadedBytes) {
            this.downloadedBytes.set(task.id, progress.downloadedBytes);
          }

          // 更新并发控制器活动时间
          this.concurrencyController.updateSlotActivity(slotId);

          this.emit('task-progress', task, progress);
        }
      );

      // 下载完成
      task.status = 'completed';
      task.progress = 100;
      task.outputPath = outputPath;
      task.updatedAt = new Date();

      // 更新状态
      this.stateManager.transitionState(task.id, 'completed', '恢复下载完成');

      // 释放并发槽位
      this.concurrencyController.releaseSlot(slotId);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '恢复下载失败';
      task.error = errorMessage;

      // 更新状态
      this.stateManager.transitionState(task.id, 'failed', errorMessage);

      // 释放并发槽位
      const slotId = this.concurrencyController.getSlotForTask(task.id);
      if (slotId) {
        this.concurrencyController.releaseSlot(slotId);
      }

      throw error;
    }
  }

  /**
   * 显示通知
   */
  private showNotification(message: string): void {
    // 在实际应用中，这里可以集成系统通知
    console.log(`[通知] ${message}`);
  }

  /**
   * 发送统计信息更新事件
   */
  private emitStatsUpdate(): void {
    const stats = this.getStats();
    this.emit('stats-updated', stats);
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(resource: CourseResource): string {
    return `download_${resource.id}_${Date.now()}`;
  }

  /**
   * 获取任务
   */
  getTask(taskId: string): DownloadTask | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * 获取所有任务
   */
  getAllTasks(): DownloadTask[] {
    return Array.from(this.tasks.values());
  }

  /**
   * 根据状态获取任务
   */
  getTasksByStatus(status: TaskStatus): DownloadTask[] {
    const taskIds = this.stateManager.getTasksByStatus(status);
    return taskIds
      .map(id => this.tasks.get(id))
      .filter((task): task is DownloadTask => task !== undefined);
  }

  /**
   * 获取下载统计信息
   */
  getStats(): DownloadStats {
    const statusStats = this.stateManager.getStatusStats();

    // 计算总下载字节数
    const totalDownloadedBytes = Array.from(this.downloadedBytes.values())
      .reduce((sum, bytes) => sum + bytes, 0);

    // 计算平均速度
    const activeTasks = this.getTasksByStatus('downloading');
    const totalSpeed = activeTasks.reduce((sum, task) => sum + task.speed, 0);
    const averageSpeed = activeTasks.length > 0 ? totalSpeed / activeTasks.length : 0;

    // 计算剩余时间
    const estimatedTimeRemaining = this.calculateEstimatedTimeRemaining();

    return {
      totalTasks: this.tasks.size,
      completedTasks: statusStats.completed,
      failedTasks: statusStats.failed,
      activeTasks: statusStats.downloading,
      pendingTasks: statusStats.pending,
      pausedTasks: statusStats.paused,
      cancelledTasks: statusStats.cancelled,
      totalDownloadedBytes,
      averageSpeed,
      estimatedTimeRemaining
    };
  }

  /**
   * 计算估计剩余时间
   */
  private calculateEstimatedTimeRemaining(): number {
    const activeTasks = this.getTasksByStatus('downloading');
    if (activeTasks.length === 0) {
      return 0;
    }

    // 找出最长的剩余时间
    let maxTime = 0;
    for (const task of activeTasks) {
      if (task.estimatedTime > maxTime) {
        maxTime = task.estimatedTime;
      }
    }

    return maxTime;
  }

  /**
   * 暂停所有任务
   */
  async pauseAll(): Promise<void> {
    const downloadingTasks = this.getTasksByStatus('downloading');
    await Promise.all(downloadingTasks.map(task => this.pauseTask(task.id)));
  }

  /**
   * 恢复所有暂停的任务
   */
  resumeAll(): void {
    const pausedTasks = this.getTasksByStatus('paused');
    pausedTasks.forEach(task => this.resumeTask(task.id));
  }

  /**
   * 取消所有任务
   */
  cancelAll(): void {
    const activeTasks = [...this.getTasksByStatus('downloading'), ...this.getTasksByStatus('pending')];
    activeTasks.forEach(task => this.cancelTask(task.id));
  }

  /**
   * 清除所有已完成的任务
   */
  clearCompletedTasks(): void {
    const completedTasks = this.getTasksByStatus('completed');
    completedTasks.forEach(task => this.clearTask(task.id));
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<DownloadManagerConfig>): void {
    this.config = { ...this.config, ...config };

    // 更新队列配置
    if (config.maxConcurrentDownloads !== undefined ||
        config.maxRetries !== undefined ||
        config.retryDelay !== undefined ||
        config.enablePriority !== undefined ||
        config.persistState !== undefined) {

      this.queue.updateConfig({
        maxConcurrentTasks: config.maxConcurrentDownloads ?? this.queue.getConfig().maxConcurrentTasks,
        maxRetries: config.maxRetries ?? this.queue.getConfig().maxRetries,
        retryDelay: config.retryDelay ?? this.queue.getConfig().retryDelay,
        enablePriority: config.enablePriority ?? this.queue.getConfig().enablePriority,
        persistState: config.persistState ?? this.queue.getConfig().persistState
      });
    }

    // 更新并发控制器配置
    if (config.maxConcurrentDownloads !== undefined) {
      this.concurrencyController.updateConfig({
        maxConcurrent: config.maxConcurrentDownloads
      });
    }
  }

  /**
   * 获取当前配置
   */
  getConfig(): DownloadManagerConfig {
    return { ...this.config };
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.cancelAll();
    this.queue.destroy();
    this.stateManager.destroy();
    this.concurrencyController.destroy();
    this.removeAllListeners();
    this.tasks.clear();
    this.downloadedBytes.clear();
  }
}

export default DownloadManagerV2;
