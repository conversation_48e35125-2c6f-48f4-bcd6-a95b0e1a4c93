import { DiskSpaceMonitor, DiskSpaceInfo, DiskSpaceWarning } from '../DiskSpaceMonitor';
import { promises as fs } from 'fs';
import { EventEmitter } from 'events';

// Mock fs.statfs
jest.mock('fs', () => ({
  promises: {
    statfs: jest.fn()
  }
}));

const mockStatfs = fs.statfs as jest.MockedFunction<typeof fs.statfs>;

describe('DiskSpaceMonitor', () => {
  let monitor: DiskSpaceMonitor;
  const testPath = '/test/path';

  beforeEach(() => {
    monitor = new DiskSpaceMonitor(1000); // 1秒检查间隔
    jest.clearAllMocks();
  });

  afterEach(() => {
    monitor.stopMonitoring();
  });

  describe('getDiskSpaceInfo', () => {
    it('should return correct disk space information', async () => {
      const mockStats = {
        blocks: 1000000,
        bsize: 4096,
        bavail: 500000
      };
      mockStatfs.mockResolvedValue(mockStats as any);

      const result = await monitor.getDiskSpaceInfo(testPath);

      expect(result).toEqual({
        total: 1000000 * 4096,
        free: 500000 * 4096,
        used: 500000 * 4096,
        usagePercentage: 0.5
      });
    });

    it('should throw error when statfs fails', async () => {
      mockStatfs.mockRejectedValue(new Error('Permission denied'));

      await expect(monitor.getDiskSpaceInfo(testPath))
        .rejects.toThrow('无法获取磁盘空间信息');
    });
  });

  describe('checkDiskSpace', () => {
    beforeEach(() => {
      const mockStats = {
        blocks: 1000000,
        bsize: 4096,
        bavail: 100000 // 10% free space
      };
      mockStatfs.mockResolvedValue(mockStats as any);
    });

    it('should return critical warning when required space exceeds available', async () => {
      const requiredSpace = 500000 * 4096; // More than available
      const warning = await monitor.checkDiskSpace(testPath, requiredSpace);

      expect(warning).toEqual({
        level: 'critical',
        message: expect.stringContaining('磁盘空间不足'),
        freeSpace: 100000 * 4096,
        requiredSpace
      });
    });

    it('should return critical warning when usage exceeds 95%', async () => {
      const mockStats = {
        blocks: 1000000,
        bsize: 4096,
        bavail: 40000 // 4% free space (96% used)
      };
      mockStatfs.mockResolvedValue(mockStats as any);

      const warning = await monitor.checkDiskSpace(testPath);

      expect(warning).toEqual({
        level: 'critical',
        message: expect.stringContaining('磁盘空间严重不足'),
        freeSpace: 40000 * 4096
      });
    });

    it('should return warning when usage exceeds 90%', async () => {
      const mockStats = {
        blocks: 1000000,
        bsize: 4096,
        bavail: 80000 // 8% free space (92% used)
      };
      mockStatfs.mockResolvedValue(mockStats as any);

      const warning = await monitor.checkDiskSpace(testPath);

      expect(warning).toEqual({
        level: 'critical', // 92% usage triggers critical, not warning
        message: expect.stringContaining('磁盘空间严重不足'),
        freeSpace: 80000 * 4096
      });
    });

    it('should return null when disk space is sufficient', async () => {
      const mockStats = {
        blocks: 1000000,
        bsize: 4096,
        bavail: 500000 // 50% free space
      };
      mockStatfs.mockResolvedValue(mockStats as any);

      const warning = await monitor.checkDiskSpace(testPath);

      expect(warning).toBeNull();
    });
  });

  describe('startMonitoring', () => {
    it('should start and stop monitoring without errors', () => {
      const mockStats = {
        blocks: 1000000,
        bsize: 4096,
        bavail: 500000
      };
      mockStatfs.mockResolvedValue(mockStats as any);

      monitor.startMonitoring(testPath);
      expect(true).toBe(true); // Should not throw
      monitor.stopMonitoring();
    });
  });

  describe('hasEnoughSpace', () => {
    it('should return true when there is enough space', async () => {
      const mockStats = {
        blocks: 1000000,
        bsize: 4096,
        bavail: 500000
      };
      mockStatfs.mockResolvedValue(mockStats as any);

      const result = await monitor.hasEnoughSpace(testPath, 1000000);
      expect(result).toBe(true);
    });

    it('should return false when there is not enough space', async () => {
      const mockStats = {
        blocks: 1000000,
        bsize: 4096,
        bavail: 100000
      };
      mockStatfs.mockResolvedValue(mockStats as any);

      const result = await monitor.hasEnoughSpace(testPath, 1000000000);
      expect(result).toBe(false);
    });

    it('should return false when statfs fails', async () => {
      mockStatfs.mockRejectedValue(new Error('Test error'));

      const result = await monitor.hasEnoughSpace(testPath, 1000000);
      expect(result).toBe(false);
    });
  });

  describe('getCleanupSuggestions', () => {
    it('should provide suggestions when free space is low', async () => {
      const mockStats = {
        blocks: 1000000,
        bsize: 4096,
        bavail: 100000 // Low free space
      };
      mockStatfs.mockResolvedValue(mockStats as any);

      const suggestions = await monitor.getCleanupSuggestions(testPath);

      expect(suggestions).toContain('清理系统临时文件');
      expect(suggestions).toContain('删除不需要的下载文件');
    });

    it('should provide different suggestions when usage is high', async () => {
      const mockStats = {
        blocks: 1000000,
        bsize: 4096,
        bavail: 150000 // 85% usage
      };
      mockStatfs.mockResolvedValue(mockStats as any);

      const suggestions = await monitor.getCleanupSuggestions(testPath);

      expect(suggestions).toContain('考虑将下载目录更改到其他磁盘');
    });
  });

  describe('performance tests', () => {
    it('should handle multiple concurrent getDiskSpaceInfo calls', async () => {
      const mockStats = {
        blocks: 1000000,
        bsize: 4096,
        bavail: 500000
      };
      mockStatfs.mockResolvedValue(mockStats as any);

      const promises = Array(10).fill(0).map(() => 
        monitor.getDiskSpaceInfo(testPath)
      );

      const results = await Promise.all(promises);
      
      expect(results).toHaveLength(10);
      results.forEach(result => {
        expect(result.usagePercentage).toBe(0.5);
      });
    });

    it('should complete disk space check within reasonable time', async () => {
      const mockStats = {
        blocks: 1000000,
        bsize: 4096,
        bavail: 500000
      };
      mockStatfs.mockResolvedValue(mockStats as any);

      const startTime = Date.now();
      await monitor.getDiskSpaceInfo(testPath);
      const elapsed = Date.now() - startTime;
      
      expect(elapsed).toBeLessThan(1000); // Should complete within 1 second
    });

    it('should handle rapid start/stop cycles', () => {
      const mockStats = {
        blocks: 1000000,
        bsize: 4096,
        bavail: 500000
      };
      mockStatfs.mockResolvedValue(mockStats as any);

      // Rapidly start and stop monitoring
      for (let i = 0; i < 10; i++) {
        monitor.startMonitoring(testPath);
        monitor.stopMonitoring();
      }

      // Should not throw any errors
      expect(true).toBe(true);
    });
  });
});