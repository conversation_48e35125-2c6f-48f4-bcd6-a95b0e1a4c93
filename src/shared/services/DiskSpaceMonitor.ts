import { promises as fs } from 'fs';
import * as path from 'path';
import { EventEmitter } from 'events';

export interface DiskSpaceInfo {
  total: number;
  free: number;
  used: number;
  usagePercentage: number;
}

export interface DiskSpaceWarning {
  level: 'warning' | 'critical';
  message: string;
  freeSpace: number;
  requiredSpace?: number;
}

export class DiskSpaceMonitor extends EventEmitter {
  private monitorInterval: NodeJS.Timeout | null = null;
  private readonly warningThreshold = 0.9; // 90%使用率警告
  private readonly criticalThreshold = 0.95; // 95%使用率严重警告
  private readonly minFreeSpaceGB = 1; // 最少1GB可用空间

  constructor(private checkIntervalMs: number = 30000) {
    super();
  }

  /**
   * 获取指定路径的磁盘空间信息
   */
  async getDiskSpaceInfo(targetPath: string): Promise<DiskSpaceInfo> {
    try {
      const stats = await fs.statfs(targetPath);
      const total = stats.blocks * stats.bsize;
      const free = stats.bavail * stats.bsize;
      const used = total - free;
      const usagePercentage = used / total;

      return {
        total,
        free,
        used,
        usagePercentage
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`无法获取磁盘空间信息: ${errorMessage}`);
    }
  }

  /**
   * 检查磁盘空间并发出警告
   */
  async checkDiskSpace(targetPath: string, requiredSpace?: number): Promise<DiskSpaceWarning | null> {
    const spaceInfo = await this.getDiskSpaceInfo(targetPath);
    const freeSpaceGB = spaceInfo.free / (1024 * 1024 * 1024);

    // 检查是否有足够空间用于特定下载
    if (requiredSpace && spaceInfo.free < requiredSpace) {
      return {
        level: 'critical',
        message: `磁盘空间不足，需要 ${this.formatBytes(requiredSpace)}，但只有 ${this.formatBytes(spaceInfo.free)} 可用`,
        freeSpace: spaceInfo.free,
        requiredSpace
      };
    }

    // 检查临界阈值
    if (spaceInfo.usagePercentage >= this.criticalThreshold || freeSpaceGB < this.minFreeSpaceGB) {
      return {
        level: 'critical',
        message: `磁盘空间严重不足，仅剩 ${this.formatBytes(spaceInfo.free)} (${(spaceInfo.usagePercentage * 100).toFixed(1)}%)`,
        freeSpace: spaceInfo.free
      };
    }

    // 检查警告阈值
    if (spaceInfo.usagePercentage >= this.warningThreshold) {
      return {
        level: 'warning',
        message: `磁盘空间不足，仅剩 ${this.formatBytes(spaceInfo.free)} (${(spaceInfo.usagePercentage * 100).toFixed(1)}%)`,
        freeSpace: spaceInfo.free
      };
    }

    return null;
  }

  /**
   * 开始监控磁盘空间
   */
  startMonitoring(targetPath: string): void {
    if (this.monitorInterval) {
      this.stopMonitoring();
    }

    this.monitorInterval = setInterval(async () => {
      try {
        const warning = await this.checkDiskSpace(targetPath);
        if (warning) {
          this.emit('diskSpaceWarning', warning);
        }

        const spaceInfo = await this.getDiskSpaceInfo(targetPath);
        this.emit('diskSpaceUpdate', spaceInfo);
      } catch (error) {
        this.emit('error', error);
      }
    }, this.checkIntervalMs);
  }

  /**
   * 停止监控磁盘空间
   */
  stopMonitoring(): void {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
  }

  /**
   * 格式化字节数为可读格式
   */
  private formatBytes(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    let size = bytes;
    let unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`;
  }

  /**
   * 检查是否有足够空间进行下载
   */
  async hasEnoughSpace(targetPath: string, requiredBytes: number): Promise<boolean> {
    try {
      const spaceInfo = await this.getDiskSpaceInfo(targetPath);
      return spaceInfo.free >= requiredBytes;
    } catch {
      return false;
    }
  }

  /**
   * 获取推荐的清理建议
   */
  async getCleanupSuggestions(targetPath: string): Promise<string[]> {
    const suggestions: string[] = [];
    
    try {
      const spaceInfo = await this.getDiskSpaceInfo(targetPath);
      const freeSpaceGB = spaceInfo.free / (1024 * 1024 * 1024);

      if (freeSpaceGB < 5) {
        suggestions.push('清理系统临时文件');
        suggestions.push('删除不需要的下载文件');
        suggestions.push('清空回收站');
      }

      if (spaceInfo.usagePercentage > 0.8) {
        suggestions.push('考虑将下载目录更改到其他磁盘');
        suggestions.push('压缩或移动旧的下载文件');
      }
    } catch {
      suggestions.push('无法获取磁盘信息，请检查目标路径');
    }

    return suggestions;
  }
}