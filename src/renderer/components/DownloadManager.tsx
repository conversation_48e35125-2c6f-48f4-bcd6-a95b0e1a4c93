import React, { useState, useMemo, useEffect } from 'react';
import { 
  <PERSON>, 
  List, 
  Button, 
  Space, 
  Typography, 
  Tabs, 
  Empty, 
  Statistic, 
  Row, 
  Col,
  Switch,
  Tooltip,
  Badge,
  Progress,
  notification,
  Alert
} from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  ClearOutlined,
  DownloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  ThunderboltOutlined,
  FileOutlined
} from '@ant-design/icons';
import { DownloadTask, DownloadManagerProps } from '../../shared/types';
import { DownloadProgress } from './DownloadProgress';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

/**
 * 扩展的下载管理器属性
 */
interface ExtendedDownloadManagerProps extends DownloadManagerProps {
  onPauseAll?: () => void;
  onResumeAll?: () => void;
  onCancelAll?: () => void;
  onClearCompleted?: () => void;
  stats?: {
    totalTasks: number;
    completedTasks: number;
    failedTasks: number;
    activeTasks: number;
    totalDownloadedBytes: number;
    averageSpeed: number;
  };
  enableNotifications?: boolean;
  showOverallProgress?: boolean;
}

/**
 * 下载完成统计信息
 */
interface CompletionStats {
  completedCount: number;
  totalSize: number;
  totalTime: number;
  averageSpeed: number;
}

/**
 * 格式化文件大小
 */
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 格式化速度
 */
const formatSpeed = (bytesPerSecond: number): string => {
  return `${formatFileSize(bytesPerSecond)}/s`;
};

/**
 * 下载管理器组件
 */
export const DownloadManager: React.FC<ExtendedDownloadManagerProps> = ({
  tasks,
  onPause,
  onResume,
  onCancel,
  onRetry,
  onClear,
  onPauseAll,
  onResumeAll,
  onCancelAll,
  onClearCompleted,
  showCompleted = true,
  stats,
  enableNotifications = true,
  showOverallProgress = true
}) => {
  const [compactMode, setCompactMode] = useState(false);
  const [activeTab, setActiveTab] = useState('all');
  const [lastCompletedCount, setLastCompletedCount] = useState(0);
  const [sessionStats, setSessionStats] = useState<CompletionStats>({
    completedCount: 0,
    totalSize: 0,
    totalTime: 0,
    averageSpeed: 0
  });

  // 按状态分组任务
  const tasksByStatus = useMemo(() => {
    const groups: Record<string, DownloadTask[]> = {
      all: tasks,
      downloading: tasks.filter(t => t.status === 'downloading'),
      pending: tasks.filter(t => t.status === 'pending'),
      paused: tasks.filter(t => t.status === 'paused'),
      completed: tasks.filter(t => t.status === 'completed'),
      failed: tasks.filter(t => t.status === 'failed'),
      cancelled: tasks.filter(t => t.status === 'cancelled')
    };
    
    return groups;
  }, [tasks]);

  // 计算统计信息
  const calculatedStats = useMemo(() => {
    if (stats) return stats;
    
    const activeTasks = tasksByStatus.downloading;
    const totalDownloadedBytes = tasksByStatus.completed.reduce((sum, task) => {
      return sum + (task.resource.metadata?.fileSize || 0);
    }, 0);
    const averageSpeed = activeTasks.length > 0 
      ? activeTasks.reduce((sum, task) => sum + task.speed, 0) / activeTasks.length
      : 0;

    return {
      totalTasks: tasks.length,
      completedTasks: tasksByStatus.completed.length,
      failedTasks: tasksByStatus.failed.length,
      activeTasks: tasksByStatus.downloading.length,
      totalDownloadedBytes,
      averageSpeed
    };
  }, [tasks, tasksByStatus, stats]);

  // 计算总体进度
  const overallProgress = useMemo(() => {
    if (tasks.length === 0) return 0;
    
    const totalProgress = tasks.reduce((sum, task) => sum + task.progress, 0);
    return Math.round(totalProgress / tasks.length);
  }, [tasks]);

  // 监听下载完成事件并显示通知
  useEffect(() => {
    const currentCompletedCount = calculatedStats.completedTasks;
    
    if (enableNotifications && currentCompletedCount > lastCompletedCount) {
      const newlyCompleted = currentCompletedCount - lastCompletedCount;
      
      if (newlyCompleted === 1) {
        // 单个任务完成
        const completedTask = tasksByStatus.completed[tasksByStatus.completed.length - 1];
        if (completedTask) {
          notification.success({
            message: '下载完成',
            description: `${completedTask.resource.title} 已成功下载`,
            duration: 4.5,
            placement: 'topRight'
          });
        }
      } else if (newlyCompleted > 1) {
        // 多个任务完成
        notification.success({
          message: '批量下载完成',
          description: `${newlyCompleted} 个任务已成功下载`,
          duration: 4.5,
          placement: 'topRight'
        });
      }
      
      // 更新会话统计
      const newCompletedTasks = tasksByStatus.completed.slice(-newlyCompleted);
      const newTotalSize = newCompletedTasks.reduce((sum, task) => 
        sum + (task.resource.metadata?.fileSize || 0), 0);
      
      setSessionStats(prev => ({
        completedCount: prev.completedCount + newlyCompleted,
        totalSize: prev.totalSize + newTotalSize,
        totalTime: prev.totalTime + newlyCompleted * 60, // 估算时间
        averageSpeed: calculatedStats.averageSpeed
      }));
    }
    
    setLastCompletedCount(currentCompletedCount);
  }, [calculatedStats.completedTasks, enableNotifications, lastCompletedCount, tasksByStatus.completed, calculatedStats.averageSpeed]);

  // 检查是否所有任务都已完成
  useEffect(() => {
    if (enableNotifications && tasks.length > 0 && calculatedStats.activeTasks === 0 && 
        calculatedStats.completedTasks > 0 && calculatedStats.failedTasks === 0) {
      notification.success({
        message: '全部下载完成！',
        description: `所有 ${calculatedStats.completedTasks} 个任务已成功下载完成`,
        duration: 6,
        placement: 'topRight'
      });
    }
  }, [calculatedStats.activeTasks, calculatedStats.completedTasks, calculatedStats.failedTasks, tasks.length, enableNotifications]);

  // 渲染总体进度
  const renderOverallProgress = () => {
    if (!showOverallProgress || tasks.length === 0) return null;

    return (
      <Card size="small" style={{ marginBottom: 16 }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 12 }}>
          <Text strong>总体进度</Text>
          <Text type="secondary">
            {calculatedStats.completedTasks} / {calculatedStats.totalTasks} 任务完成
          </Text>
        </div>
        <Progress 
          percent={overallProgress} 
          status={calculatedStats.activeTasks > 0 ? 'active' : 'normal'}
          strokeColor={{
            '0%': '#108ee9',
            '100%': '#87d068',
          }}
        />
        {calculatedStats.activeTasks > 0 && calculatedStats.averageSpeed > 0 && (
          <div style={{ marginTop: 8, display: 'flex', justifyContent: 'space-between' }}>
            <Text type="secondary">
              平均速度: {formatSpeed(calculatedStats.averageSpeed)}
            </Text>
            <Text type="secondary">
              已下载: {formatFileSize(calculatedStats.totalDownloadedBytes)}
            </Text>
          </div>
        )}
      </Card>
    );
  };

  // 渲染统计卡片
  const renderStatsCards = () => (
    <Row gutter={16} style={{ marginBottom: 16 }}>
      <Col span={6}>
        <Card size="small">
          <Statistic
            title="总任务"
            value={calculatedStats.totalTasks}
            prefix={<DownloadOutlined />}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card size="small">
          <Statistic
            title="进行中"
            value={calculatedStats.activeTasks}
            prefix={<PlayCircleOutlined />}
            valueStyle={{ color: '#1890ff' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card size="small">
          <Statistic
            title="已完成"
            value={calculatedStats.completedTasks}
            prefix={<CheckCircleOutlined />}
            valueStyle={{ color: '#52c41a' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card size="small">
          <Statistic
            title="失败"
            value={calculatedStats.failedTasks}
            prefix={<ExclamationCircleOutlined />}
            valueStyle={{ color: '#ff4d4f' }}
          />
        </Card>
      </Col>
    </Row>
  );

  // 渲染会话统计信息
  const renderSessionStats = () => {
    if (sessionStats.completedCount === 0) return null;

    return (
      <Alert
        message="本次会话统计"
        description={
          <Row gutter={16}>
            <Col span={6}>
              <Statistic
                title="已完成"
                value={sessionStats.completedCount}
                prefix={<CheckCircleOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="总大小"
                value={formatFileSize(sessionStats.totalSize)}
                prefix={<FileOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="平均速度"
                value={formatSpeed(sessionStats.averageSpeed)}
                prefix={<ThunderboltOutlined />}
              />
            </Col>
            <Col span={6}>
              <Statistic
                title="用时"
                value={`${Math.round(sessionStats.totalTime / 60)}分钟`}
                prefix={<ClockCircleOutlined />}
              />
            </Col>
          </Row>
        }
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />
    );
  };

  // 渲染工具栏
  const renderToolbar = () => (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'space-between', 
      alignItems: 'center',
      marginBottom: 16,
      padding: '12px 16px',
      background: '#fafafa',
      borderRadius: 6
    }}>
      <Space>
        <Text strong>批量操作:</Text>
        {onPauseAll && (
          <Tooltip title="暂停所有下载">
            <Button 
              size="small" 
              icon={<PauseCircleOutlined />}
              onClick={onPauseAll}
              disabled={tasksByStatus.downloading.length === 0}
            >
              暂停全部
            </Button>
          </Tooltip>
        )}
        {onResumeAll && (
          <Tooltip title="恢复所有暂停的下载">
            <Button 
              size="small" 
              icon={<PlayCircleOutlined />}
              onClick={onResumeAll}
              disabled={tasksByStatus.paused.length === 0}
            >
              恢复全部
            </Button>
          </Tooltip>
        )}
        {onCancelAll && (
          <Tooltip title="取消所有下载">
            <Button 
              size="small" 
              icon={<StopOutlined />}
              onClick={onCancelAll}
              disabled={tasksByStatus.downloading.length === 0 && tasksByStatus.pending.length === 0}
              danger
            >
              取消全部
            </Button>
          </Tooltip>
        )}
        {onClearCompleted && (
          <Tooltip title="清除已完成的任务">
            <Button 
              size="small" 
              icon={<ClearOutlined />}
              onClick={onClearCompleted}
              disabled={tasksByStatus.completed.length === 0}
            >
              清除已完成
            </Button>
          </Tooltip>
        )}
      </Space>

      <Space>
        {calculatedStats.averageSpeed > 0 && (
          <Text type="secondary">
            平均速度: {formatSpeed(calculatedStats.averageSpeed)}
          </Text>
        )}
        <Text type="secondary">紧凑模式:</Text>
        <Switch 
          size="small"
          checked={compactMode}
          onChange={setCompactMode}
        />
      </Space>
    </div>
  );

  // 渲染任务列表
  const renderTaskList = (taskList: DownloadTask[]) => {
    if (taskList.length === 0) {
      return (
        <Empty 
          description="暂无下载任务"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }

    return (
      <List
        dataSource={taskList}
        renderItem={(task) => (
          <List.Item key={task.id} style={{ padding: compactMode ? 0 : undefined }}>
            <DownloadProgress
              task={task}
              onPause={onPause}
              onResume={onResume}
              onCancel={onCancel}
              onRetry={onRetry}
              onClear={onClear}
              compact={compactMode}
            />
          </List.Item>
        )}
      />
    );
  };

  // 获取标签页标题和徽章
  const getTabTitle = (key: string, count: number) => {
    const titles: Record<string, string> = {
      all: '全部',
      downloading: '下载中',
      pending: '等待中',
      paused: '已暂停',
      completed: '已完成',
      failed: '失败',
      cancelled: '已取消'
    };

    return (
      <span>
        {titles[key]}
        {count > 0 && <Badge count={count} style={{ marginLeft: 8 }} />}
      </span>
    );
  };

  return (
    <div>
      <Title level={4} style={{ marginBottom: 16 }}>
        下载管理器
      </Title>

      {renderOverallProgress()}
      {renderSessionStats()}
      {renderStatsCards()}
      {renderToolbar()}

      <Card>
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          type="card"
          size="small"
        >
          <TabPane 
            tab={getTabTitle('all', tasksByStatus.all.length)} 
            key="all"
          >
            {renderTaskList(tasksByStatus.all)}
          </TabPane>
          
          <TabPane 
            tab={getTabTitle('downloading', tasksByStatus.downloading.length)} 
            key="downloading"
          >
            {renderTaskList(tasksByStatus.downloading)}
          </TabPane>
          
          <TabPane 
            tab={getTabTitle('pending', tasksByStatus.pending.length)} 
            key="pending"
          >
            {renderTaskList(tasksByStatus.pending)}
          </TabPane>
          
          <TabPane 
            tab={getTabTitle('paused', tasksByStatus.paused.length)} 
            key="paused"
          >
            {renderTaskList(tasksByStatus.paused)}
          </TabPane>
          
          {showCompleted && (
            <TabPane 
              tab={getTabTitle('completed', tasksByStatus.completed.length)} 
              key="completed"
            >
              {renderTaskList(tasksByStatus.completed)}
            </TabPane>
          )}
          
          <TabPane 
            tab={getTabTitle('failed', tasksByStatus.failed.length)} 
            key="failed"
          >
            {renderTaskList(tasksByStatus.failed)}
          </TabPane>
          
          <TabPane 
            tab={getTabTitle('cancelled', tasksByStatus.cancelled.length)} 
            key="cancelled"
          >
            {renderTaskList(tasksByStatus.cancelled)}
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default DownloadManager;
