import React from 'react';
import { createRoot } from 'react-dom/client';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { SimpleTestApp } from './components/SimpleTestApp';
import './styles/global.scss';

/**
 * 简化的渲染进程入口
 * 专注于测试核心下载功能
 */
const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <SimpleTestApp />
    </ConfigProvider>
  );
};

// 渲染应用
const container = document.getElementById('root');
if (container) {
  const root = createRoot(container);
  root.render(<App />);
} else {
  console.error('找不到根容器元素');
}
