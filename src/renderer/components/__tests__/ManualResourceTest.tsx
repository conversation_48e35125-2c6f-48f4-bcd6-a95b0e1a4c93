import React, { useState } from 'react';
import { ResourceList } from '../ResourceList';
import { ResourceService } from '../../../shared/services/ResourceService';
import { SmartEduClient } from '../../../shared/services/SmartEduClient';
import { CourseResource, CourseFilters } from '../../../shared/types';

/**
 * 手动测试组件 - 用于验证资源搜索和展示功能
 * 这个组件可以在开发环境中使用来测试功能
 */
export const ManualResourceTest: React.FC = () => {
  const [resources, setResources] = useState<CourseResource[]>([]);
  const [loading, setLoading] = useState(false);
  const [userLoggedIn, setUserLoggedIn] = useState(false);
  const [selectedResources, setSelectedResources] = useState<string[]>([]);

  // 模拟资源数据
  const mockResources: CourseResource[] = [
    {
      id: 'resource1',
      title: '小学一年级语文教材（上册）',
      type: 'textbook',
      url: 'https://example.com/textbook1',
      metadata: {
        stage: 'primary',
        grade: 'grade1',
        subject: 'chinese',
        version: 'renjiao',
        volume: 'volume1',
        chapter: '第一单元',
        lesson: '识字1',
        fileSize: 2048000
      },
      requiresAuth: false,
      accessLevel: 'public'
    },
    {
      id: 'resource2',
      title: '小学一年级语文视频课程',
      type: 'video',
      url: 'https://example.com/video1',
      metadata: {
        stage: 'primary',
        grade: 'grade1',
        subject: 'chinese',
        version: 'renjiao',
        volume: 'volume1',
        chapter: '第一单元',
        lesson: '识字1',
        duration: 1800,
        fileSize: 52428800
      },
      requiresAuth: true,
      accessLevel: 'registered'
    },
    {
      id: 'resource3',
      title: '高中数学高级课程',
      type: 'video',
      url: 'https://example.com/video2',
      metadata: {
        stage: 'high',
        grade: 'grade12',
        subject: 'math',
        version: 'advanced',
        volume: 'volume2',
        chapter: '微积分',
        lesson: '导数应用',
        duration: 3600,
        fileSize: 104857600
      },
      requiresAuth: true,
      accessLevel: 'premium'
    },
    {
      id: 'resource4',
      title: '初中物理实验教材',
      type: 'textbook',
      url: 'https://example.com/textbook2',
      metadata: {
        stage: 'middle',
        grade: 'grade8',
        subject: 'physics',
        version: 'standard',
        volume: 'volume1',
        chapter: '光学',
        lesson: '光的反射',
        fileSize: 3145728
      },
      requiresAuth: false,
      accessLevel: 'public'
    }
  ];

  // 模拟搜索功能
  const handleSearch = async () => {
    setLoading(true);
    
    // 模拟API延迟
    setTimeout(() => {
      setResources(mockResources);
      setLoading(false);
    }, 1000);
  };

  // 模拟清空结果
  const handleClear = () => {
    setResources([]);
    setSelectedResources([]);
  };

  // 处理下载
  const handleDownload = (resource: CourseResource) => {
    console.log('下载资源:', resource);
    alert(`开始下载: ${resource.title}`);
  };

  // 处理批量下载
  const handleBatchDownload = (resources: CourseResource[]) => {
    console.log('批量下载资源:', resources);
    alert(`开始批量下载 ${resources.length} 个资源`);
  };

  // 处理选择变更
  const handleSelectionChange = (selectedIds: string[]) => {
    setSelectedResources(selectedIds);
    console.log('选中的资源:', selectedIds);
  };

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <h1>资源搜索和展示功能测试</h1>
      
      {/* 控制面板 */}
      <div style={{ 
        marginBottom: '20px', 
        padding: '16px', 
        border: '1px solid #d9d9d9', 
        borderRadius: '6px',
        backgroundColor: '#fafafa'
      }}>
        <h3>测试控制</h3>
        <div style={{ display: 'flex', gap: '10px', alignItems: 'center', flexWrap: 'wrap' }}>
          <button onClick={handleSearch} disabled={loading}>
            {loading ? '搜索中...' : '模拟搜索'}
          </button>
          <button onClick={handleClear}>清空结果</button>
          <label style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
            <input 
              type="checkbox" 
              checked={userLoggedIn}
              onChange={(e) => setUserLoggedIn(e.target.checked)}
            />
            用户已登录
          </label>
          <span>已选择: {selectedResources.length} 个资源</span>
        </div>
      </div>

      {/* 功能说明 */}
      <div style={{ 
        marginBottom: '20px', 
        padding: '16px', 
        border: '1px solid #1890ff', 
        borderRadius: '6px',
        backgroundColor: '#e6f7ff'
      }}>
        <h3>功能测试说明</h3>
        <ul>
          <li>✅ <strong>资源展示</strong>: 支持卡片和列表两种视图模式</li>
          <li>✅ <strong>权限控制</strong>: 根据登录状态显示不同的下载权限</li>
          <li>✅ <strong>资源选择</strong>: 支持单选、全选和批量操作</li>
          <li>✅ <strong>下载功能</strong>: 单个下载和批量下载</li>
          <li>✅ <strong>加载状态</strong>: 显示加载指示器</li>
          <li>✅ <strong>空状态</strong>: 无资源时的友好提示</li>
          <li>✅ <strong>统计信息</strong>: 显示资源数量和类型统计</li>
          <li>✅ <strong>元数据展示</strong>: 显示学段、年级、学科等信息</li>
        </ul>
      </div>

      {/* 资源列表组件 */}
      <ResourceList
        resources={resources}
        onDownload={handleDownload}
        onBatchDownload={handleBatchDownload}
        userLoggedIn={userLoggedIn}
        loading={loading}
        selectedResources={selectedResources}
        onSelectionChange={handleSelectionChange}
      />

      {/* 测试结果显示 */}
      {selectedResources.length > 0 && (
        <div style={{ 
          marginTop: '20px', 
          padding: '16px', 
          border: '1px solid #52c41a', 
          borderRadius: '6px',
          backgroundColor: '#f6ffed'
        }}>
          <h3>选中的资源ID</h3>
          <pre>{JSON.stringify(selectedResources, null, 2)}</pre>
        </div>
      )}
    </div>
  );
};

export default ManualResourceTest;