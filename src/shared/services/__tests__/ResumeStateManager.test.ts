import * as fs from 'fs-extra';
import * as path from 'path';
import { ResumeStateManager, ResumeState, SegmentState } from '../ResumeStateManager';
import { CourseResource } from '../../types';

describe('ResumeStateManager', () => {
  let resumeStateManager: ResumeStateManager;
  let testStateDir: string;
  let mockResource: CourseResource;
  let mockResumeState: ResumeState;

  beforeEach(async () => {
    // 创建测试目录
    testStateDir = path.join(__dirname, 'test-states');
    await fs.ensureDir(testStateDir);

    // 初始化管理器
    resumeStateManager = new ResumeStateManager({
      stateDir: testStateDir,
      enableChecksum: true,
      autoCleanup: false
    });

    // 模拟资源
    mockResource = {
      id: 'test-video-1',
      title: '测试视频',
      type: 'video',
      url: 'https://example.com/test.m3u8',
      metadata: {
        stage: '小学',
        grade: '三年级',
        subject: '数学',
        version: '人教版',
        volume: '上册'
      },
      requiresAuth: false,
      accessLevel: 'public'
    };

    // 模拟断点续传状态
    mockResumeState = {
      taskId: 'test-task-1',
      resource: mockResource,
      outputPath: path.join(testStateDir, 'output', 'video.mp4'),
      tempDir: path.join(testStateDir, 'temp'),
      totalSegments: 10,
      downloadedSegments: [
        {
          index: 0,
          url: 'https://example.com/segment0.ts',
          filePath: path.join(testStateDir, 'temp', 'segment_000000.ts'),
          size: 1024,
          downloadedAt: new Date(),
          isComplete: true
        },
        {
          index: 1,
          url: 'https://example.com/segment1.ts',
          filePath: path.join(testStateDir, 'temp', 'segment_000001.ts'),
          size: 2048,
          downloadedAt: new Date(),
          isComplete: true
        }
      ],
      failedSegments: [5],
      lastUpdateTime: new Date(),
      playlistUrl: 'https://example.com/test.m3u8',
      baseUrl: 'https://example.com/'
    };
  });

  afterEach(async () => {
    // 清理测试目录
    await fs.remove(testStateDir);
  });

  describe('状态保存和加载', () => {
    it('应该能够保存断点续传状态', async () => {
      await resumeStateManager.saveState(mockResumeState);

      const stateFilePath = path.join(testStateDir, `${mockResumeState.taskId}.json`);
      expect(await fs.pathExists(stateFilePath)).toBe(true);

      const savedData = await fs.readJson(stateFilePath);
      expect(savedData.taskId).toBe(mockResumeState.taskId);
      expect(savedData.totalSegments).toBe(mockResumeState.totalSegments);
      expect(savedData.downloadedSegments).toHaveLength(2);
    });

    it('应该能够加载断点续传状态', async () => {
      // 先保存状态
      await resumeStateManager.saveState(mockResumeState);

      // 再加载状态
      const loadedState = await resumeStateManager.loadState(mockResumeState.taskId);

      expect(loadedState).not.toBeNull();
      expect(loadedState!.taskId).toBe(mockResumeState.taskId);
      expect(loadedState!.totalSegments).toBe(mockResumeState.totalSegments);
      expect(loadedState!.downloadedSegments).toHaveLength(2);
      expect(loadedState!.failedSegments).toEqual([5]);
    });

    it('应该在状态文件不存在时返回null', async () => {
      const loadedState = await resumeStateManager.loadState('non-existent-task');
      expect(loadedState).toBeNull();
    });
  });

  describe('片段状态管理', () => {
    beforeEach(async () => {
      await resumeStateManager.saveState(mockResumeState);
    });

    it('应该能够更新片段状态', async () => {
      const newSegmentState: Partial<SegmentState> = {
        index: 2,
        url: 'https://example.com/segment2.ts',
        filePath: '/test/temp/segment_000002.ts',
        size: 3072,
        isComplete: true
      };

      await resumeStateManager.updateSegmentState(
        mockResumeState.taskId,
        2,
        newSegmentState
      );

      const updatedState = await resumeStateManager.loadState(mockResumeState.taskId);
      expect(updatedState!.downloadedSegments).toHaveLength(3);
      
      const newSegment = updatedState!.downloadedSegments.find(s => s.index === 2);
      expect(newSegment).toBeDefined();
      expect(newSegment!.size).toBe(3072);
      expect(newSegment!.isComplete).toBe(true);
    });

    it('应该能够标记片段为失败', async () => {
      await resumeStateManager.markSegmentFailed(mockResumeState.taskId, 7);

      const updatedState = await resumeStateManager.loadState(mockResumeState.taskId);
      expect(updatedState!.failedSegments).toContain(7);
      expect(updatedState!.failedSegments).toContain(5); // 原有的失败片段
    });

    it('应该能够获取已下载的片段列表', async () => {
      const downloadedSegments = resumeStateManager.getDownloadedSegments(mockResumeState.taskId);
      expect(downloadedSegments).toHaveLength(2);
      expect(downloadedSegments[0].index).toBe(0);
      expect(downloadedSegments[1].index).toBe(1);
    });

    it('应该能够获取失败的片段列表', async () => {
      const failedSegments = resumeStateManager.getFailedSegments(mockResumeState.taskId);
      expect(failedSegments).toEqual([5]);
    });
  });

  describe('进度计算', () => {
    beforeEach(async () => {
      await resumeStateManager.saveState(mockResumeState);
    });

    it('应该能够正确计算下载进度', () => {
      const progress = resumeStateManager.calculateProgress(mockResumeState.taskId);
      // 2个已完成片段 / 10个总片段 = 20%
      expect(progress).toBe(20);
    });

    it('应该在没有片段时返回0进度', () => {
      const emptyState = {
        ...mockResumeState,
        taskId: 'empty-task',
        downloadedSegments: []
      };
      
      resumeStateManager.saveState(emptyState);
      const progress = resumeStateManager.calculateProgress('empty-task');
      expect(progress).toBe(0);
    });
  });

  describe('续传检查', () => {
    it('应该在有已下载片段时允许续传', async () => {
      // 创建临时目录和文件
      await fs.ensureDir(mockResumeState.tempDir);
      for (const segment of mockResumeState.downloadedSegments) {
        await fs.ensureDir(path.dirname(segment.filePath));
        await fs.writeFile(segment.filePath, Buffer.alloc(segment.size));
      }

      await resumeStateManager.saveState(mockResumeState);

      const canResume = await resumeStateManager.canResume(mockResumeState.taskId);
      expect(canResume).toBe(true);

      // 清理
      await fs.remove(mockResumeState.tempDir);
    });

    it('应该在临时目录不存在时不允许续传', async () => {
      await resumeStateManager.saveState(mockResumeState);

      const canResume = await resumeStateManager.canResume(mockResumeState.taskId);
      expect(canResume).toBe(false);
    });

    it('应该在片段文件不存在时不允许续传', async () => {
      // 创建临时目录但不创建片段文件
      await fs.ensureDir(mockResumeState.tempDir);
      await resumeStateManager.saveState(mockResumeState);

      const canResume = await resumeStateManager.canResume(mockResumeState.taskId);
      expect(canResume).toBe(false);

      // 清理
      await fs.remove(mockResumeState.tempDir);
    });
  });

  describe('状态清理', () => {
    it('应该能够移除断点续传状态', async () => {
      await resumeStateManager.saveState(mockResumeState);

      const stateFilePath = path.join(testStateDir, `${mockResumeState.taskId}.json`);
      expect(await fs.pathExists(stateFilePath)).toBe(true);

      await resumeStateManager.removeState(mockResumeState.taskId);
      expect(await fs.pathExists(stateFilePath)).toBe(false);
    });

    it('应该能够清理过期的状态文件', async () => {
      // 创建一个过期的状态
      const expiredState = {
        ...mockResumeState,
        taskId: 'expired-task',
        lastUpdateTime: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000) // 8天前
      };

      await resumeStateManager.saveState(expiredState);

      // 手动修改文件时间以模拟过期
      const stateFilePath = path.join(testStateDir, `${expiredState.taskId}.json`);
      const pastTime = new Date(Date.now() - 8 * 24 * 60 * 60 * 1000);
      await fs.utimes(stateFilePath, pastTime, pastTime);

      await resumeStateManager.cleanupExpiredStates();

      expect(await fs.pathExists(stateFilePath)).toBe(false);
    });
  });

  describe('事件发射', () => {
    it('应该在保存状态时发射事件', async () => {
      const stateSavedSpy = jest.fn();
      resumeStateManager.on('state-saved', stateSavedSpy);

      await resumeStateManager.saveState(mockResumeState);

      expect(stateSavedSpy).toHaveBeenCalledWith(
        mockResumeState.taskId,
        expect.objectContaining({
          taskId: mockResumeState.taskId
        })
      );
    });

    it('应该在加载状态时发射事件', async () => {
      await resumeStateManager.saveState(mockResumeState);

      const stateLoadedSpy = jest.fn();
      resumeStateManager.on('state-loaded', stateLoadedSpy);

      await resumeStateManager.loadState(mockResumeState.taskId);

      expect(stateLoadedSpy).toHaveBeenCalledWith(
        mockResumeState.taskId,
        expect.objectContaining({
          taskId: mockResumeState.taskId
        })
      );
    });

    it('应该在更新片段状态时发射事件', async () => {
      await resumeStateManager.saveState(mockResumeState);

      const segmentUpdatedSpy = jest.fn();
      resumeStateManager.on('segment-updated', segmentUpdatedSpy);

      await resumeStateManager.updateSegmentState(mockResumeState.taskId, 3, {
        index: 3,
        isComplete: true
      });

      expect(segmentUpdatedSpy).toHaveBeenCalledWith(
        mockResumeState.taskId,
        3,
        expect.objectContaining({
          index: 3,
          isComplete: true
        })
      );
    });
  });
});
