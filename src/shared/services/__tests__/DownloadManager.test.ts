import { DownloadManager, DownloadManagerConfig } from '../DownloadManager';
import { FileOrganizer, FileOrganizerConfig } from '../FileOrganizer';
import { CourseResource, DownloadTask } from '../../types';

// Mock FileOrganizer
jest.mock('../FileOrganizer');

describe('DownloadManager', () => {
  let downloadManager: DownloadManager;
  let mockFileOrganizer: jest.Mocked<FileOrganizer>;
  let mockResource: CourseResource;
  let config: DownloadManagerConfig;

  beforeEach(() => {
    jest.clearAllMocks();

    // Create mock FileOrganizer
    const organizerConfig: FileOrganizerConfig = {
      basePath: '/test/downloads',
      namingPattern: '{stage}-{grade}-{subject}-{title}',
      createSubfolders: true,
      groupBySubject: true,
      groupByGrade: true
    };

    mockFileOrganizer = new FileOrganizer(organizerConfig) as jest.Mocked<FileOrganizer>;

    config = {
      maxConcurrentDownloads: 2,
      autoRetry: true,
      maxRetries: 3,
      retryDelay: 1000,
      enableNotifications: false
    };

    downloadManager = new DownloadManager(mockFileOrganizer, config);

    mockResource = {
      id: 'resource-123',
      title: '测试教材',
      type: 'textbook',
      url: 'https://example.com/textbook.pdf',
      metadata: {
        stage: '小学',
        grade: '三年级',
        subject: '数学',
        version: '人教版',
        volume: '上册',
        fileSize: 1024000
      },
      requiresAuth: false,
      accessLevel: 'public'
    };
  });

  afterEach(() => {
    downloadManager.destroy();
  });

  describe('addTask', () => {
    it('should add a new download task', () => {
      const task = downloadManager.addTask(mockResource);

      expect(task).toBeDefined();
      expect(task.resource).toEqual(mockResource);
      expect(task.status).toBe('pending');
      expect(task.progress).toBe(0);
      expect(task.retryCount).toBe(0);
      expect(task.maxRetries).toBe(config.maxRetries);
    });

    it('should throw error when adding duplicate task', () => {
      downloadManager.addTask(mockResource);
      
      expect(() => {
        downloadManager.addTask(mockResource);
      }).toThrow('该资源已在下载队列中');
    });

    it('should emit task-added event', (done) => {
      downloadManager.on('task-added', (task: DownloadTask) => {
        expect(task.resource).toEqual(mockResource);
        done();
      });

      downloadManager.addTask(mockResource);
    });
  });

  describe('addBatchTasks', () => {
    it('should add multiple tasks', () => {
      const resources = [
        mockResource,
        { ...mockResource, id: 'resource-456', title: '测试教材2' }
      ];

      const tasks = downloadManager.addBatchTasks(resources);

      expect(tasks).toHaveLength(2);
      expect(downloadManager.getAllTasks()).toHaveLength(2);
    });

    it('should handle duplicate resources in batch', () => {
      const resources = [
        mockResource,
        mockResource, // duplicate
        { ...mockResource, id: 'resource-456', title: '测试教材2' }
      ];

      const tasks = downloadManager.addBatchTasks(resources);

      expect(tasks).toHaveLength(2); // Only unique tasks added
    });
  });

  describe('pauseTask', () => {
    it('should pause a downloading task', () => {
      const task = downloadManager.addTask(mockResource);
      
      // Simulate task being in downloading state
      task.status = 'downloading';
      
      downloadManager.pauseTask(task.id);
      
      expect(task.status).toBe('paused');
    });

    it('should throw error for non-existent task', () => {
      expect(() => {
        downloadManager.pauseTask('non-existent-id');
      }).toThrow('任务不存在');
    });

    it('should emit task-cancelled event', (done) => {
      const task = downloadManager.addTask(mockResource);
      task.status = 'downloading';

      downloadManager.on('task-cancelled', (cancelledTask: DownloadTask) => {
        expect(cancelledTask.id).toBe(task.id);
        done();
      });

      downloadManager.pauseTask(task.id);
    });
  });

  describe('resumeTask', () => {
    it('should resume a paused task', () => {
      const task = downloadManager.addTask(mockResource);
      task.status = 'paused';
      
      downloadManager.resumeTask(task.id);
      
      expect(task.status).toBe('pending');
    });

    it('should throw error for non-existent task', () => {
      expect(() => {
        downloadManager.resumeTask('non-existent-id');
      }).toThrow('任务不存在');
    });
  });

  describe('cancelTask', () => {
    it('should cancel a task', () => {
      const task = downloadManager.addTask(mockResource);
      
      downloadManager.cancelTask(task.id);
      
      expect(task.status).toBe('cancelled');
    });

    it('should emit task-cancelled event', (done) => {
      const task = downloadManager.addTask(mockResource);

      downloadManager.on('task-cancelled', (cancelledTask: DownloadTask) => {
        expect(cancelledTask.id).toBe(task.id);
        done();
      });

      downloadManager.cancelTask(task.id);
    });
  });

  describe('retryTask', () => {
    it('should retry a failed task', () => {
      const task = downloadManager.addTask(mockResource);
      task.status = 'failed';
      task.retryCount = 1;
      task.error = 'Download failed';
      
      downloadManager.retryTask(task.id);
      
      expect(task.status).toBe('pending');
      expect(task.retryCount).toBe(0);
      expect(task.error).toBeUndefined();
    });

    it('should throw error for non-existent task', () => {
      expect(() => {
        downloadManager.retryTask('non-existent-id');
      }).toThrow('任务不存在');
    });
  });

  describe('clearTask', () => {
    it('should clear a task', () => {
      const task = downloadManager.addTask(mockResource);
      
      downloadManager.clearTask(task.id);
      
      expect(downloadManager.getTask(task.id)).toBeUndefined();
    });

    it('should handle non-existent task gracefully', () => {
      expect(() => {
        downloadManager.clearTask('non-existent-id');
      }).not.toThrow();
    });
  });

  describe('getTask', () => {
    it('should return task by ID', () => {
      const task = downloadManager.addTask(mockResource);
      
      const retrievedTask = downloadManager.getTask(task.id);
      
      expect(retrievedTask).toEqual(task);
    });

    it('should return undefined for non-existent task', () => {
      const task = downloadManager.getTask('non-existent-id');
      
      expect(task).toBeUndefined();
    });
  });

  describe('getAllTasks', () => {
    it('should return all tasks', () => {
      const task1 = downloadManager.addTask(mockResource);
      const task2 = downloadManager.addTask({ ...mockResource, id: 'resource-456' });
      
      const allTasks = downloadManager.getAllTasks();
      
      expect(allTasks).toHaveLength(2);
      expect(allTasks).toContain(task1);
      expect(allTasks).toContain(task2);
    });
  });

  describe('getTasksByStatus', () => {
    it('should return tasks filtered by status', () => {
      const task1 = downloadManager.addTask(mockResource);
      const task2 = downloadManager.addTask({ ...mockResource, id: 'resource-456' });
      
      task1.status = 'completed';
      task2.status = 'failed';
      
      const completedTasks = downloadManager.getTasksByStatus('completed');
      const failedTasks = downloadManager.getTasksByStatus('failed');
      
      expect(completedTasks).toHaveLength(1);
      expect(completedTasks[0]).toEqual(task1);
      expect(failedTasks).toHaveLength(1);
      expect(failedTasks[0]).toEqual(task2);
    });
  });

  describe('getStats', () => {
    it('should return download statistics', () => {
      const task1 = downloadManager.addTask(mockResource);
      const task2 = downloadManager.addTask({ ...mockResource, id: 'resource-456' });
      
      task1.status = 'completed';
      task2.status = 'failed';
      
      const stats = downloadManager.getStats();
      
      expect(stats.totalTasks).toBe(2);
      expect(stats.completedTasks).toBe(1);
      expect(stats.failedTasks).toBe(1);
      expect(stats.activeTasks).toBe(0);
    });
  });

  describe('clearCompletedTasks', () => {
    it('should clear all completed tasks', () => {
      const task1 = downloadManager.addTask(mockResource);
      const task2 = downloadManager.addTask({ ...mockResource, id: 'resource-456' });
      
      task1.status = 'completed';
      task2.status = 'pending';
      
      downloadManager.clearCompletedTasks();
      
      const allTasks = downloadManager.getAllTasks();
      expect(allTasks).toHaveLength(1);
      expect(allTasks[0]).toEqual(task2);
    });
  });

  describe('pauseAll', () => {
    it('should pause all downloading tasks', () => {
      const task1 = downloadManager.addTask(mockResource);
      const task2 = downloadManager.addTask({ ...mockResource, id: 'resource-456' });
      
      task1.status = 'downloading';
      task2.status = 'downloading';
      
      downloadManager.pauseAll();
      
      expect(task1.status).toBe('paused');
      expect(task2.status).toBe('paused');
    });
  });

  describe('resumeAll', () => {
    it('should resume all paused tasks', () => {
      const task1 = downloadManager.addTask(mockResource);
      const task2 = downloadManager.addTask({ ...mockResource, id: 'resource-456' });
      
      task1.status = 'paused';
      task2.status = 'paused';
      
      downloadManager.resumeAll();
      
      expect(task1.status).toBe('pending');
      expect(task2.status).toBe('pending');
    });
  });

  describe('cancelAll', () => {
    it('should cancel all active tasks', () => {
      const task1 = downloadManager.addTask(mockResource);
      const task2 = downloadManager.addTask({ ...mockResource, id: 'resource-456' });
      
      task1.status = 'downloading';
      task2.status = 'pending';
      
      downloadManager.cancelAll();
      
      expect(task1.status).toBe('cancelled');
      expect(task2.status).toBe('cancelled');
    });
  });

  describe('updateConfig', () => {
    it('should update configuration', () => {
      const newConfig = { maxConcurrentDownloads: 5 };
      
      downloadManager.updateConfig(newConfig);
      
      const currentConfig = downloadManager.getConfig();
      expect(currentConfig.maxConcurrentDownloads).toBe(5);
    });
  });

  describe('getConfig', () => {
    it('should return current configuration', () => {
      const currentConfig = downloadManager.getConfig();
      
      expect(currentConfig).toEqual(config);
    });
  });

  describe('events', () => {
    it('should emit stats-updated event when tasks change', (done) => {
      downloadManager.on('stats-updated', (stats) => {
        expect(stats.totalTasks).toBe(1);
        done();
      });

      downloadManager.addTask(mockResource);
    });

    it('should emit queue-empty event when no tasks are active', (done) => {
      downloadManager.on('queue-empty', () => {
        done();
      });

      // This is a bit tricky to test without actually running downloads
      // For now, we'll just ensure the event can be listened to
      downloadManager.emit('queue-empty');
    });
  });
});
