// Type definitions for Electron API exposed to renderer process

export interface ElectronAPI {
  // App info
  getAppVersion: () => Promise<string>;
  getAppName: () => Promise<string>;

  // Download related APIs
  getDefaultDownloadPath: () => Promise<string>;
  selectDownloadPath: () => Promise<string | null>;
  openDownloadFolder: (path: string) => Promise<boolean>;

  // File system APIs
  checkFileExists: (path: string) => Promise<boolean>;
  getFileStats: (path: string) => Promise<{
    size: number;
    isFile: boolean;
    isDirectory: boolean;
    mtime: Date;
    ctime: Date;
  } | null>;

  // Notification APIs
  showNotification: (title: string, body: string) => Promise<boolean>;

  // Auto updater APIs
  checkForUpdates: () => Promise<void>;
  quitAndInstall: () => Promise<void>;

  // DevTools APIs (仅在开发环境可用)
  toggleDevTools: () => Promise<void>;
  isDevToolsOpened: () => Promise<boolean>;

  // Download APIs
  startDownload: (url: string, config: any) => Promise<string>;
  pauseDownload: (taskId: string) => Promise<boolean>;
  resumeDownload: (taskId: string) => Promise<boolean>;
  cancelDownload: (taskId: string) => Promise<boolean>;
  getAllDownloadTasks: () => Promise<any[]>;

  // Download event listeners
  onDownloadProgress: (callback: (taskId: string, progress: any) => void) => void;
  onDownloadStatusChange: (callback: (taskId: string, status: string) => void) => void;
}

declare global {
  interface Window {
    electronAPI: ElectronAPI;
  }
}

export {};
