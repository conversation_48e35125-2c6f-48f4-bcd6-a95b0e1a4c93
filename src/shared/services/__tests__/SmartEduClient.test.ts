import axios from 'axios';
import { SmartEduClient } from '../SmartEduClient';
import {
  CourseFilters,
  FilterOptions,
  CourseResource,
  ResourceDetail,
  M3U8Playlist,
  CaptchaInfo,
  AuthResult,
  NetworkError,
  ApiError
} from '../../types';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('SmartEduClient', () => {
  let client: SmartEduClient;
  let mockAxiosInstance: any;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();
    jest.clearAllTimers();
    
    // Create mock axios instance
    mockAxiosInstance = {
      get: jest.fn(),
      post: jest.fn(),
      defaults: {
        baseURL: 'https://basic.smartedu.cn',
        timeout: 30000
      },
      interceptors: {
        request: {
          use: jest.fn()
        },
        response: {
          use: jest.fn()
        }
      }
    };

    // Mock axios.create to return our mock instance
    mockedAxios.create.mockReturnValue(mockAxiosInstance);

    // Create client instance
    client = new SmartEduClient();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('Constructor', () => {
    it('should create axios instance with correct default configuration', () => {
      expect(mockedAxios.create).toHaveBeenCalledWith({
        baseURL: 'https://basic.smartedu.cn',
        timeout: 30000,
        headers: expect.objectContaining({
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Connection': 'keep-alive'
        })
      });
    });

    it('should setup request and response interceptors', () => {
      expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalled();
      expect(mockAxiosInstance.interceptors.response.use).toHaveBeenCalled();
    });

    it('should accept custom baseURL', () => {
      const customURL = 'https://custom.example.com';
      new SmartEduClient(customURL);
      
      expect(mockedAxios.create).toHaveBeenCalledWith(
        expect.objectContaining({
          baseURL: customURL
        })
      );
    });
  });

  describe('User-Agent Management', () => {
    it('should rotate user agents on multiple requests', async () => {
      const mockResponse = { data: { success: true } };
      mockAxiosInstance.get.mockResolvedValue(mockResponse);

      // Make multiple requests
      await client.checkAuthStatus();
      await client.checkAuthStatus();
      await client.checkAuthStatus();

      // Verify that interceptor was called multiple times
      expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalled();
    });

    it('should allow setting custom user agents', () => {
      const customUserAgents = [
        'Custom Agent 1',
        'Custom Agent 2'
      ];

      client.setUserAgents(customUserAgents);
      const config = client.getConfig();
      
      expect(config.userAgentCount).toBe(2);
    });

    it('should throw error when setting empty user agent list', () => {
      expect(() => {
        client.setUserAgents([]);
      }).toThrow('User-Agent列表不能为空');
    });
  });

  describe('Rate Limiting', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    it('should enforce minimum request interval', async () => {
      const mockResponse = { data: { authenticated: true } };
      mockAxiosInstance.get.mockResolvedValue(mockResponse);

      const startTime = Date.now();
      
      // Make first request
      const promise1 = client.checkAuthStatus();
      
      // Advance time by 500ms (less than minimum interval)
      jest.advanceTimersByTime(500);
      
      // Make second request
      const promise2 = client.checkAuthStatus();
      
      // Advance time to complete the delay
      jest.advanceTimersByTime(1000);
      
      await Promise.all([promise1, promise2]);
      
      expect(mockAxiosInstance.get).toHaveBeenCalledTimes(2);
    });

    it('should track request statistics', async () => {
      const mockResponse = { data: { authenticated: true } };
      mockAxiosInstance.get.mockResolvedValue(mockResponse);

      await client.checkAuthStatus();
      
      const stats = client.getRequestStats();
      expect(stats.totalRequests).toBe(1);
      expect(stats.requestsInLastMinute).toBe(1);
      expect(stats.lastRequestTime).toBeInstanceOf(Date);
    });

    it('should reset request statistics', () => {
      client.resetRequestStats();
      
      const stats = client.getRequestStats();
      expect(stats.totalRequests).toBe(0);
      expect(stats.requestsInLastMinute).toBe(0);
      expect(stats.lastRequestTime).toBeNull();
    });
  });

  describe('Authentication Methods', () => {
    describe('login', () => {
      it('should successfully login with valid credentials', async () => {
        const mockAuthResult: AuthResult = {
          success: true,
          token: 'mock-token',
          user: {
            id: '123',
            username: 'testuser',
            displayName: 'Test User',
            permissions: ['read']
          }
        };

        mockAxiosInstance.post.mockResolvedValue({ data: mockAuthResult });

        const result = await client.login('testuser', 'password123');

        expect(mockAxiosInstance.post).toHaveBeenCalledWith(
          '/api/auth/login',
          {
            username: 'testuser',
            password: 'password123',
            captcha: undefined
          },
          undefined
        );
        expect(result).toEqual(mockAuthResult);
      });

      it('should handle login failure', async () => {
        mockAxiosInstance.post.mockResolvedValue({
          data: null
        });

        const result = await client.login('testuser', 'wrongpassword');

        expect(result.success).toBe(false);
        expect(result.error).toBe('登录失败');
      });

      it('should handle network errors during login', async () => {
        const networkError = new Error('Network error');
        mockAxiosInstance.post.mockRejectedValue(networkError);

        const result = await client.login('testuser', 'password123');

        expect(result.success).toBe(false);
        expect(result.error).toBe('Network error');
      });

      it('should include captcha when provided', async () => {
        const mockAuthResult: AuthResult = { success: true };
        mockAxiosInstance.post.mockResolvedValue({ data: mockAuthResult });

        await client.login('testuser', 'password123', 'captcha123');

        expect(mockAxiosInstance.post).toHaveBeenCalledWith(
          '/api/auth/login',
          {
            username: 'testuser',
            password: 'password123',
            captcha: 'captcha123'
          },
          undefined
        );
      });
    });

    describe('logout', () => {
      it('should call logout endpoint', async () => {
        mockAxiosInstance.post.mockResolvedValue({ data: {} });

        await client.logout();

        expect(mockAxiosInstance.post).toHaveBeenCalledWith('/api/auth/logout', undefined, undefined);
      });
    });

    describe('checkAuthStatus', () => {
      it('should return true for authenticated user', async () => {
        mockAxiosInstance.get.mockResolvedValue({
          data: { authenticated: true }
        });

        const result = await client.checkAuthStatus();

        expect(result).toBe(true);
        expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/auth/status', undefined);
      });

      it('should return false for unauthenticated user', async () => {
        mockAxiosInstance.get.mockResolvedValue({
          data: { authenticated: false }
        });

        const result = await client.checkAuthStatus();

        expect(result).toBe(false);
      });

      it('should return false on network error', async () => {
        mockAxiosInstance.get.mockRejectedValue(new Error('Network error'));

        const result = await client.checkAuthStatus();

        expect(result).toBe(false);
      });
    });

    describe('getCaptcha', () => {
      it('should successfully get captcha info', async () => {
        const mockCaptcha: CaptchaInfo = {
          image: 'base64-image-data',
          token: 'captcha-token'
        };

        mockAxiosInstance.get.mockResolvedValue({ data: mockCaptcha });

        const result = await client.getCaptcha();

        expect(result).toEqual(mockCaptcha);
        expect(mockAxiosInstance.get).toHaveBeenCalledWith('/api/auth/captcha', undefined);
      });

      it('should throw ApiError when captcha request fails', async () => {
        mockAxiosInstance.get.mockResolvedValue({ data: null });

        await expect(client.getCaptcha()).rejects.toThrow(ApiError);
      });
    });
  });

  describe('Resource Methods', () => {
    describe('getFilterOptions', () => {
      it('should get filter options without parent filter', async () => {
        const mockFilterOptions: FilterOptions = {
          stages: [{ value: 'primary', label: '小学' }],
          grades: [{ value: 'grade1', label: '一年级' }],
          subjects: [{ value: 'math', label: '数学' }],
          versions: [{ value: 'v1', label: '版本1' }],
          volumes: [{ value: 'vol1', label: '上册' }]
        };

        mockAxiosInstance.get.mockResolvedValue({ data: mockFilterOptions });

        const result = await client.getFilterOptions();

        expect(result).toEqual(mockFilterOptions);
        expect(mockAxiosInstance.get).toHaveBeenCalledWith(
          '/syncClassroom/getFilterOptions',
          { params: {} }
        );
      });

      it('should get filter options with parent filter', async () => {
        const parentFilter: Partial<CourseFilters> = {
          stage: 'primary',
          grade: 'grade1'
        };

        const mockFilterOptions: FilterOptions = {
          stages: [],
          grades: [],
          subjects: [{ value: 'math', label: '数学' }],
          versions: [],
          volumes: []
        };

        mockAxiosInstance.get.mockResolvedValue({ data: mockFilterOptions });

        const result = await client.getFilterOptions(parentFilter);

        expect(result).toEqual(mockFilterOptions);
        expect(mockAxiosInstance.get).toHaveBeenCalledWith(
          '/syncClassroom/getFilterOptions',
          { params: parentFilter }
        );
      });

      it('should throw ApiError when request fails', async () => {
        mockAxiosInstance.get.mockResolvedValue({ data: null });

        await expect(client.getFilterOptions()).rejects.toThrow(ApiError);
      });
    });

    describe('searchResources', () => {
      it('should search resources with filters', async () => {
        const filters: CourseFilters = {
          stage: 'primary',
          grade: 'grade1',
          subject: 'math',
          version: 'v1',
          volume: 'vol1'
        };

        const mockResources: CourseResource[] = [
          {
            id: 'resource1',
            title: '数学课程1',
            type: 'video',
            url: 'http://example.com/video1',
            metadata: {
              stage: 'primary',
              grade: 'grade1',
              subject: 'math',
              version: 'v1',
              volume: 'vol1'
            },
            requiresAuth: false,
            accessLevel: 'public'
          }
        ];

        mockAxiosInstance.get.mockResolvedValue({ data: mockResources });

        const result = await client.searchResources(filters);

        expect(result).toEqual(mockResources);
        expect(mockAxiosInstance.get).toHaveBeenCalledWith(
          '/syncClassroom/search',
          { params: filters }
        );
      });

      it('should throw ApiError when search fails', async () => {
        const filters: CourseFilters = {
          stage: 'primary',
          grade: 'grade1',
          subject: 'math',
          version: 'v1',
          volume: 'vol1'
        };

        mockAxiosInstance.get.mockRejectedValue(new Error('Search failed'));

        await expect(client.searchResources(filters)).rejects.toThrow(ApiError);
      });
    });

    describe('getResourceDetail', () => {
      it('should get resource detail by ID', async () => {
        const resourceId = 'resource123';
        const mockDetail: ResourceDetail = {
          id: resourceId,
          title: '详细资源',
          type: 'textbook',
          url: 'http://example.com/textbook',
          metadata: {
            stage: 'primary',
            grade: 'grade1',
            subject: 'math',
            version: 'v1',
            volume: 'vol1'
          },
          requiresAuth: true,
          accessLevel: 'registered',
          description: '这是一个详细的资源描述',
          tags: ['数学', '小学'],
          createdAt: new Date(),
          updatedAt: new Date()
        };

        mockAxiosInstance.get.mockResolvedValue({ data: mockDetail });

        const result = await client.getResourceDetail(resourceId);

        expect(result).toEqual(mockDetail);
        expect(mockAxiosInstance.get).toHaveBeenCalledWith(`/api/resource/${resourceId}`, undefined);
      });

      it('should throw ApiError when resource not found', async () => {
        mockAxiosInstance.get.mockResolvedValue({ data: null });

        await expect(client.getResourceDetail('nonexistent')).rejects.toThrow(ApiError);
      });
    });

    describe('getVideoPlaylist', () => {
      it('should get video playlist by ID', async () => {
        const videoId = 'video123';
        const mockPlaylist: M3U8Playlist = {
          baseUrl: 'http://example.com/video/',
          segments: [
            { url: 'segment1.ts', duration: 10, sequence: 1 },
            { url: 'segment2.ts', duration: 10, sequence: 2 }
          ],
          totalDuration: 20,
          isEncrypted: false
        };

        mockAxiosInstance.get.mockResolvedValue({ data: mockPlaylist });

        const result = await client.getVideoPlaylist(videoId);

        expect(result).toEqual(mockPlaylist);
        expect(mockAxiosInstance.get).toHaveBeenCalledWith(`/api/video/${videoId}/playlist`, undefined);
      });

      it('should throw ApiError when playlist request fails', async () => {
        mockAxiosInstance.get.mockRejectedValue(new Error('Playlist not found'));

        await expect(client.getVideoPlaylist('nonexistent')).rejects.toThrow(ApiError);
      });
    });
  });

  describe('Retry Mechanism', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    it('should implement retry mechanism with exponential backoff', () => {
      // Test that the retry mechanism is properly configured
      const config = client.getConfig();
      expect(config.minRequestInterval).toBe(1000);
      expect(config.maxRequestsPerMinute).toBe(30);
    });

    it('should handle network errors gracefully', async () => {
      // Create persistent network error
      const networkError = { request: {}, message: 'Persistent network error' };
      mockAxiosInstance.get.mockRejectedValue(networkError);

      const result = await client.checkAuthStatus();
      
      // Should return false instead of throwing
      expect(result).toBe(false);
      expect(mockAxiosInstance.get).toHaveBeenCalled();
    });
  });

  describe('Configuration', () => {
    it('should return current configuration', () => {
      const config = client.getConfig();

      expect(config).toEqual({
        baseURL: 'https://basic.smartedu.cn',
        timeout: 30000,
        minRequestInterval: 1000,
        maxRequestsPerMinute: 30,
        userAgentCount: 5
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle axios response errors correctly', async () => {
      const responseError = {
        response: {
          status: 404,
          statusText: 'Not Found'
        },
        config: {
          url: '/test',
          method: 'GET'
        }
      };

      mockAxiosInstance.get.mockRejectedValue(responseError);

      const result = await client.checkAuthStatus();
      expect(result).toBe(false);
    });

    it('should handle axios request errors correctly', async () => {
      const requestError = {
        request: {},
        message: 'Request timeout'
      };

      mockAxiosInstance.get.mockRejectedValue(requestError);

      const result = await client.checkAuthStatus();
      expect(result).toBe(false);
    });

    it('should handle other axios errors correctly', async () => {
      const configError = {
        message: 'Config error'
      };

      mockAxiosInstance.get.mockRejectedValue(configError);

      const result = await client.checkAuthStatus();
      expect(result).toBe(false);
    });
  });
});