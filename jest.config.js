module.exports = {
  preset: 'ts-jest',
  roots: ['<rootDir>/src'],
  testMatch: [
    '**/__tests__/**/*.test.ts',
    '**/__tests__/**/*.test.tsx'
  ],
  transform: {
    '^.+\\.tsx?$': 'ts-jest'
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/__tests__/**',
    '!src/**/index.ts'
  ],
  setupFilesAfterEnv: ['<rootDir>/src/shared/utils/__tests__/setup.ts'],
  projects: [
    {
      displayName: 'node',
      preset: 'ts-jest',
      testEnvironment: 'node',
      testMatch: [
        '<rootDir>/src/shared/**/__tests__/**/*.test.ts',
        '<rootDir>/src/main/**/__tests__/**/*.test.ts'
      ],
      transform: {
        '^.+\\.tsx?$': 'ts-jest'
      },
      setupFilesAfterEnv: ['<rootDir>/src/shared/utils/__tests__/setup.ts']
    },
    {
      displayName: 'jsdom',
      preset: 'ts-jest',
      testEnvironment: 'jsdom',
      testMatch: [
        '<rootDir>/src/renderer/**/__tests__/**/*.test.tsx',
        '<rootDir>/src/renderer/**/__tests__/**/*.test.ts'
      ],
      transform: {
        '^.+\\.tsx?$': 'ts-jest'
      },
      moduleNameMapper: {
        '\\.(css|less|scss|sass)$': 'identity-obj-proxy'
      },
      setupFilesAfterEnv: ['<rootDir>/src/shared/utils/__tests__/setup.ts']
    }
  ]
};