import { autoUpdater } from 'electron-updater';
import { BrowserWindow, dialog } from 'electron';
import log from 'electron-log';

export class AutoUpdater {
  private mainWindow: BrowserWindow | null = null;

  constructor(mainWindow: BrowserWindow) {
    this.mainWindow = mainWindow;
    this.setupAutoUpdater();
  }

  private setupAutoUpdater(): void {
    // 配置日志
    autoUpdater.logger = log;
    (autoUpdater.logger as any).transports.file.level = 'info';

    // 配置更新服务器
    autoUpdater.setFeedURL({
      provider: 'github',
      owner: 'smart-edu-downloader',
      repo: 'smart-edu-downloader',
      private: false
    });

    // 监听更新事件
    autoUpdater.on('checking-for-update', () => {
      log.info('正在检查更新...');
      this.sendStatusToWindow('正在检查更新...');
    });

    autoUpdater.on('update-available', (info) => {
      log.info('发现新版本:', info.version);
      this.sendStatusToWindow(`发现新版本 ${info.version}`);
      this.showUpdateDialog(info);
    });

    autoUpdater.on('update-not-available', (info) => {
      log.info('当前已是最新版本:', info.version);
      this.sendStatusToWindow('当前已是最新版本');
    });

    autoUpdater.on('error', (err) => {
      log.error('自动更新错误:', err);
      this.sendStatusToWindow('更新检查失败');
    });

    autoUpdater.on('download-progress', (progressObj) => {
      let logMessage = `下载速度: ${progressObj.bytesPerSecond}`;
      logMessage += ` - 已下载 ${progressObj.percent}%`;
      logMessage += ` (${progressObj.transferred}/${progressObj.total})`;
      
      log.info(logMessage);
      this.sendStatusToWindow(logMessage);
    });

    autoUpdater.on('update-downloaded', (info) => {
      log.info('更新下载完成:', info.version);
      this.sendStatusToWindow('更新下载完成');
      this.showRestartDialog(info);
    });
  }

  public checkForUpdates(): void {
    autoUpdater.checkForUpdatesAndNotify();
  }

  public checkForUpdatesManually(): void {
    autoUpdater.checkForUpdates();
  }

  private sendStatusToWindow(message: string): void {
    if (this.mainWindow) {
      this.mainWindow.webContents.send('update-status', message);
    }
  }

  private async showUpdateDialog(info: any): Promise<void> {
    if (!this.mainWindow) return;

    const result = await dialog.showMessageBox(this.mainWindow, {
      type: 'info',
      title: '发现新版本',
      message: `智慧教育下载器有新版本可用！`,
      detail: `当前版本: ${process.env.npm_package_version}\n新版本: ${info.version}\n\n是否现在下载更新？`,
      buttons: ['现在下载', '稍后提醒', '跳过此版本'],
      defaultId: 0,
      cancelId: 1
    });

    switch (result.response) {
      case 0: // 现在下载
        autoUpdater.downloadUpdate();
        break;
      case 1: // 稍后提醒
        // 不做任何操作，下次启动时会再次检查
        break;
      case 2: // 跳过此版本
        // 可以在这里记录跳过的版本号
        log.info(`用户选择跳过版本: ${info.version}`);
        break;
    }
  }

  private async showRestartDialog(info: any): Promise<void> {
    if (!this.mainWindow) return;

    const result = await dialog.showMessageBox(this.mainWindow, {
      type: 'info',
      title: '更新已下载',
      message: '新版本已下载完成！',
      detail: `版本 ${info.version} 已准备就绪。\n\n是否现在重启应用以完成更新？`,
      buttons: ['立即重启', '稍后重启'],
      defaultId: 0,
      cancelId: 1
    });

    if (result.response === 0) {
      autoUpdater.quitAndInstall();
    }
  }

  public quitAndInstall(): void {
    autoUpdater.quitAndInstall();
  }
}