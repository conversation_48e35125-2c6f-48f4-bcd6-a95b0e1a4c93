// Global styles for the Smart Edu Downloader application

* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC',
    'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial,
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100%;
}

// Custom scrollbar styles
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

// Utility classes
.text-center {
  text-align: center;
}

.full-width {
  width: 100%;
}

.full-height {
  height: 100%;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

// Animation classes
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// ResourceList component styles
.selected-resource-card {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.selected-resource-item {
  background-color: rgba(24, 144, 255, 0.05);
  border-left: 3px solid #1890ff;
  padding-left: 13px;
}

.resource-metadata-tags {
  .ant-tag {
    margin-bottom: 4px;
  }
}

.resource-stats {
  color: #666;
  font-size: 12px;
  
  .ant-divider-vertical {
    margin: 0 8px;
  }
}

// Resource access level tags
.ant-tag {
  &.access-public {
    background-color: #f6ffed;
    border-color: #b7eb8f;
    color: #52c41a;
  }
  
  &.access-registered {
    background-color: #e6f7ff;
    border-color: #91d5ff;
    color: #1890ff;
  }
  
  &.access-premium {
    background-color: #fffbe6;
    border-color: #ffe58f;
    color: #faad14;
  }
}

// MainLayout component styles
.main-layout {
  .ant-layout-sider {
    .ant-menu {
      border-right: none;
    }
    
    .ant-menu-item {
      margin: 4px 8px;
      border-radius: 6px;
      
      &:hover {
        background-color: rgba(24, 144, 255, 0.1);
      }
      
      &.ant-menu-item-selected {
        background-color: #1890ff;
        color: white;
        
        .anticon {
          color: white;
        }
      }
    }
  }
  
  .ant-layout-header {
    .user-dropdown {
      .ant-dropdown-trigger {
        padding: 8px 12px;
        border-radius: 6px;
        transition: background-color 0.3s;
        
        &:hover {
          background-color: rgba(0, 0, 0, 0.05);
        }
      }
    }
  }
}

// Dashboard styles
.dashboard-stats-card {
  border-radius: 12px;
  overflow: hidden;
  
  .stats-content {
    padding: 24px;
    color: white;
    
    .stats-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 8px;
    }
    
    .stats-value {
      font-size: 32px;
      font-weight: bold;
      line-height: 1;
      margin-bottom: 8px;
    }
    
    .stats-description {
      font-size: 14px;
      opacity: 0.8;
    }
  }
}

// Settings panel styles
.settings-panel {
  .ant-tabs-tab {
    padding: 12px 16px;
    
    .anticon {
      margin-right: 8px;
    }
  }
  
  .settings-section {
    margin-bottom: 24px;
    
    .section-title {
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;
    }
  }
  
  .experimental-feature-card {
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    padding: 16px;
    margin-bottom: 12px;
    
    &:hover {
      border-color: #40a9ff;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
    }
  }
}

// Filter drawer styles
.filter-drawer {
  .ant-drawer-body {
    padding: 0;
  }
  
  .filter-content {
    padding: 24px;
  }
}

// Float button styles
.ant-float-btn-group {
  .ant-float-btn {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    }
  }
}

// Responsive styles
@media (max-width: 768px) {
  .main-layout {
    .ant-layout-sider {
      position: fixed !important;
      height: 100vh;
      z-index: 1000;
    }
    
    .ant-layout-content {
      margin-left: 0 !important;
    }
  }
  
  .dashboard-stats-card {
    margin-bottom: 16px;
  }
  
  .settings-panel {
    .ant-modal {
      margin: 0;
      max-width: 100vw;
      height: 100vh;
      
      .ant-modal-content {
        height: 100vh;
        border-radius: 0;
      }
    }
  }
}

// Dark theme support (for future implementation)
@media (prefers-color-scheme: dark) {
  .dashboard-stats-card {
    .stats-content {
      background: linear-gradient(135deg, #434343 0%, #000000 100%);
    }
  }
}

// Animation enhancements
.fade-slide-in {
  animation: fadeSlideIn 0.3s ease-out;
}

@keyframes fadeSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.bounce-in {
  animation: bounceIn 0.5s ease-out;
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// Loading states
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

// Custom scrollbar for content areas
.custom-scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    
    &:hover {
      background: rgba(0, 0, 0, 0.3);
    }
  }
}