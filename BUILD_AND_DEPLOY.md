# 智慧教育下载器 - 构建和部署指南

本文档说明如何构建、打包和部署智慧教育下载器应用程序。

## 快速开始

### 1. 环境准备

确保您的系统满足以下要求：

- **Node.js**: 16.0 或更高版本
- **npm**: 8.0 或更高版本
- **Python**: 3.7 或更高版本（用于原生模块编译）

#### 平台特定要求

**macOS:**
```bash
# 安装 Xcode Command Line Tools
xcode-select --install
```

**Windows:**
```bash
# 安装 Visual Studio Build Tools 或 Visual Studio Community
# 下载地址: https://visualstudio.microsoft.com/downloads/
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt-get install build-essential libnss3-dev libatk-bridge2.0-dev libdrm2 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2-dev
```

### 2. 安装依赖

```bash
npm install
```

### 3. 运行测试

在构建之前，建议运行部署测试以确保配置正确：

```bash
# 运行完整的部署测试
npm run test:deployment

# 或者分别运行各项测试
npm run test:packaging    # 测试打包配置
npm run test:build       # 测试构建过程
npm run test:install     # 测试安装包
```

## 构建和打包

### 构建源代码

```bash
# 构建所有代码
npm run build

# 分别构建主进程和渲染进程
npm run build:main
npm run build:renderer
```

### 应用打包

```bash
# 打包应用（不生成安装包，用于测试）
npm run pack

# 生成安装包
npm run dist

# 生成特定平台的安装包
npm run dist:mac     # macOS
npm run dist:win     # Windows  
npm run dist:linux   # Linux

# 生成所有平台的安装包
npm run dist:all
```

### 输出文件

打包完成后，文件将输出到 `release/` 目录：

**macOS:**
- `智慧教育下载器-1.0.0.dmg` - DMG 安装包
- `智慧教育下载器-1.0.0-mac.zip` - ZIP 压缩包

**Windows:**
- `智慧教育下载器-1.0.0-x64-setup.exe` - NSIS 安装程序
- `智慧教育下载器-1.0.0-portable.exe` - 便携版

**Linux:**
- `智慧教育下载器-1.0.0.AppImage` - AppImage 格式
- `智慧教育下载器_1.0.0_amd64.deb` - Debian 包
- `智慧教育下载器-1.0.0.x86_64.rpm` - RPM 包

## 自动更新

应用内置了自动更新功能，基于 GitHub Releases：

### 配置自动更新

1. 在 GitHub 上创建仓库
2. 更新 `package.json` 中的发布配置：

```json
{
  "build": {
    "publish": {
      "provider": "github",
      "owner": "your-username",
      "repo": "your-repo-name"
    }
  }
}
```

### 发布更新

```bash
# 更新版本号
npm version patch  # 或 minor, major

# 构建并发布到 GitHub Releases
npm run publish
```

### 更新流程

1. 应用启动后 5 秒自动检查更新
2. 发现新版本时弹窗提示用户
3. 用户确认后下载更新
4. 下载完成后提示重启应用

## 图标和资源

### 应用图标

当前使用的是占位符图标，建议替换为真实图标：

1. 准备一个 1024x1024 像素的 PNG 图片
2. 使用工具转换为各平台格式：

```bash
# 安装图标生成工具
npm install -g electron-icon-builder

# 生成所有平台图标
electron-icon-builder --input=./icon-source.png --output=./build --flatten
```

3. 将生成的图标文件放置在 `build/` 目录：
   - `icon.icns` - macOS 图标
   - `icon.ico` - Windows 图标  
   - `icon.png` - Linux 图标

### 构建资源

`build/` 目录包含以下构建资源：

- `entitlements.mac.plist` - macOS 权限配置
- `installer.nsh` - Windows 安装脚本
- `app-metadata.json` - 应用元数据
- 各平台图标文件

## 代码签名

### macOS 代码签名

1. 获取 Apple Developer 证书
2. 配置签名身份：

```json
{
  "build": {
    "mac": {
      "identity": "Developer ID Application: Your Name (TEAM_ID)"
    }
  }
}
```

### Windows 代码签名

1. 获取代码签名证书
2. 配置签名：

```json
{
  "build": {
    "win": {
      "certificateFile": "path/to/certificate.p12",
      "certificatePassword": "password"
    }
  }
}
```

## 故障排除

### 常见问题

**构建失败:**
```bash
# 清理缓存和重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

**打包失败:**
```bash
# 检查配置
npm run test:packaging

# 清理旧构建
rm -rf dist release
```

**图标问题:**
- 确保图标文件存在于 `build/` 目录
- 检查图标文件格式和尺寸
- 使用专业工具生成图标

**权限问题 (macOS):**
```bash
# 检查权限配置
cat build/entitlements.mac.plist
```

### 调试技巧

1. **启用详细日志:**
   ```bash
   DEBUG=electron-builder npm run dist
   ```

2. **检查构建输出:**
   ```bash
   ls -la release/
   ```

3. **验证应用结构:**
   ```bash
   # macOS
   ls -la "release/mac/智慧教育下载器.app/Contents/"
   
   # Windows
   ls -la "release/win-unpacked/"
   
   # Linux
   ls -la "release/linux-unpacked/"
   ```

## 持续集成

### GitHub Actions

创建 `.github/workflows/build.yml` 用于自动构建：

```yaml
name: Build and Release

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [macos-latest, windows-latest, ubuntu-latest]
    
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - run: npm ci
      - run: npm run test:deployment
      - run: npm run dist
      
      - uses: actions/upload-artifact@v3
        with:
          name: release-${{ matrix.os }}
          path: release/
```

## 性能优化

### 减小包大小

1. **排除不必要的文件:**
   ```json
   {
     "build": {
       "files": [
         "dist/**/*",
         "node_modules/**/*",
         "!node_modules/**/*.{md,txt,LICENSE}",
         "!node_modules/**/test/**"
       ]
     }
   }
   ```

2. **使用 asar 打包:**
   ```json
   {
     "build": {
       "asar": true
     }
   }
   ```

3. **压缩资源:**
   ```json
   {
     "build": {
       "compression": "maximum"
     }
   }
   ```

## 安全考虑

1. **启用上下文隔离:**
   ```javascript
   new BrowserWindow({
     webPreferences: {
       contextIsolation: true,
       nodeIntegration: false
     }
   });
   ```

2. **验证更新包:**
   - 使用 HTTPS 下载更新
   - 验证更新包签名
   - 检查更新包完整性

3. **最小权限原则:**
   - 只请求必要的系统权限
   - 定期审查权限配置

## 更多资源

- [Electron Builder 官方文档](https://www.electron.build/)
- [Electron 安全指南](https://www.electronjs.org/docs/tutorial/security)
- [应用签名指南](https://www.electron.build/code-signing)
- [自动更新文档](https://www.electron.build/auto-update)