# 智慧教育下载器 - 部署指南

本文档详细说明了智慧教育下载器的打包、部署和发布流程。

## 目录

- [环境准备](#环境准备)
- [构建配置](#构建配置)
- [打包流程](#打包流程)
- [平台特定配置](#平台特定配置)
- [自动更新配置](#自动更新配置)
- [发布流程](#发布流程)
- [测试验证](#测试验证)
- [故障排除](#故障排除)

## 环境准备

### 系统要求

- **Node.js**: 版本 16.0 或更高
- **npm**: 版本 8.0 或更高
- **Python**: 版本 3.7 或更高（用于 node-gyp）

### 平台特定要求

#### macOS
- **Xcode Command Line Tools**: `xcode-select --install`
- **代码签名证书**: 用于应用签名（可选）

#### Windows
- **Visual Studio Build Tools**: 或 Visual Studio Community
- **Windows SDK**: 最新版本
- **代码签名证书**: 用于应用签名（可选）

#### Linux
- **构建工具**: `sudo apt-get install build-essential`
- **依赖库**: `sudo apt-get install libnss3-dev libatk-bridge2.0-dev libdrm2 libxcomposite1 libxdamage1 libxrandr2 libgbm1 libxss1 libasound2-dev`

### 安装依赖

```bash
# 安装项目依赖
npm install

# 全局安装 electron-builder（可选）
npm install -g electron-builder
```

## 构建配置

### package.json 配置

应用的构建配置位于 `package.json` 的 `build` 字段中：

```json
{
  "build": {
    "appId": "com.smartedu.downloader",
    "productName": "智慧教育下载器",
    "directories": {
      "output": "release",
      "buildResources": "build"
    },
    "files": [
      "dist/**/*",
      "node_modules/**/*"
    ]
  }
}
```

### 构建资源

构建资源位于 `build/` 目录：

- `icon.icns` - macOS 应用图标
- `icon.ico` - Windows 应用图标
- `icon.png` - Linux 应用图标
- `entitlements.mac.plist` - macOS 权限配置
- `installer.nsh` - Windows 安装脚本
- `app-metadata.json` - 应用元数据

## 打包流程

### 基本构建命令

```bash
# 构建源代码
npm run build

# 打包应用（不生成安装包）
npm run pack

# 生成安装包
npm run dist

# 生成特定平台的安装包
npm run dist:mac    # macOS
npm run dist:win    # Windows
npm run dist:linux  # Linux

# 生成所有平台的安装包
npm run dist:all
```

### 构建流程

1. **源代码编译**: TypeScript → JavaScript
2. **资源打包**: Webpack 打包渲染进程
3. **依赖处理**: 复制 node_modules
4. **应用打包**: electron-builder 打包
5. **安装包生成**: 生成平台特定的安装包

## 平台特定配置

### macOS

```json
{
  "mac": {
    "category": "public.app-category.education",
    "target": [
      { "target": "dmg", "arch": ["x64", "arm64"] },
      { "target": "zip", "arch": ["x64", "arm64"] }
    ],
    "icon": "build/icon.icns",
    "hardenedRuntime": true,
    "entitlements": "build/entitlements.mac.plist"
  }
}
```

**输出文件**:
- `智慧教育下载器-1.0.0.dmg` - DMG 安装包
- `智慧教育下载器-1.0.0-mac.zip` - ZIP 压缩包

### Windows

```json
{
  "win": {
    "target": [
      { "target": "nsis", "arch": ["x64", "ia32"] },
      { "target": "portable", "arch": ["x64"] }
    ],
    "icon": "build/icon.ico",
    "publisherName": "Smart Edu Downloader Team"
  }
}
```

**输出文件**:
- `智慧教育下载器-1.0.0-x64-setup.exe` - NSIS 安装程序
- `智慧教育下载器-1.0.0-portable.exe` - 便携版

### Linux

```json
{
  "linux": {
    "target": [
      { "target": "AppImage", "arch": ["x64"] },
      { "target": "deb", "arch": ["x64"] },
      { "target": "rpm", "arch": ["x64"] }
    ],
    "icon": "build/icon.png",
    "category": "Education"
  }
}
```

**输出文件**:
- `智慧教育下载器-1.0.0.AppImage` - AppImage 格式
- `智慧教育下载器_1.0.0_amd64.deb` - Debian 包
- `智慧教育下载器-1.0.0.x86_64.rpm` - RPM 包

## 自动更新配置

### GitHub Releases

```json
{
  "publish": {
    "provider": "github",
    "owner": "smart-edu-downloader",
    "repo": "smart-edu-downloader"
  }
}
```

### 发布更新

```bash
# 发布到 GitHub Releases
npm run publish

# 或使用 electron-builder 直接发布
npx electron-builder --publish=always
```

### 更新检查

应用会在启动后自动检查更新：

- 检查间隔：启动后 5 秒
- 更新源：GitHub Releases
- 用户交互：弹窗提示用户选择

## 发布流程

### 1. 版本准备

```bash
# 更新版本号
npm version patch  # 或 minor, major

# 更新 CHANGELOG.md
# 更新 README.md
```

### 2. 构建测试

```bash
# 运行构建测试
node scripts/build-test.js

# 运行安装测试
node scripts/install-test.js
```

### 3. 生成发布包

```bash
# 清理旧的构建
rm -rf dist release

# 构建所有平台
npm run dist:all
```

### 4. 发布到 GitHub

```bash
# 创建 Git 标签
git tag v1.0.0
git push origin v1.0.0

# 发布到 GitHub Releases
npm run publish
```

## 测试验证

### 自动化测试

```bash
# 构建测试
node scripts/build-test.js

# 安装测试
node scripts/install-test.js
```

### 手动测试清单

#### 安装测试
- [ ] 安装包能正常下载
- [ ] 安装过程无错误
- [ ] 应用图标正确显示
- [ ] 桌面快捷方式创建成功
- [ ] 开始菜单项创建成功

#### 功能测试
- [ ] 应用能正常启动
- [ ] 界面显示正常
- [ ] 基本功能可用
- [ ] 网络连接正常
- [ ] 文件下载功能正常

#### 更新测试
- [ ] 自动更新检查正常
- [ ] 更新下载正常
- [ ] 更新安装正常
- [ ] 更新后功能正常

### 性能测试

- **启动时间**: < 3 秒
- **内存占用**: < 200MB
- **安装包大小**: < 150MB
- **下载速度**: 根据网络环境

## 故障排除

### 常见问题

#### 构建失败

**问题**: `node-gyp` 编译错误
**解决**: 安装正确的构建工具和 Python 环境

**问题**: 依赖包安装失败
**解决**: 清理 `node_modules` 和 `package-lock.json`，重新安装

#### 打包失败

**问题**: 图标文件缺失
**解决**: 确保 `build/` 目录包含所需的图标文件

**问题**: 代码签名失败
**解决**: 检查签名证书配置或禁用代码签名

#### 自动更新问题

**问题**: 更新检查失败
**解决**: 检查网络连接和 GitHub API 访问

**问题**: 更新下载失败
**解决**: 检查 GitHub Releases 配置和文件权限

### 调试技巧

1. **启用详细日志**:
   ```bash
   DEBUG=electron-builder npm run dist
   ```

2. **检查构建输出**:
   ```bash
   ls -la release/
   ```

3. **验证应用结构**:
   ```bash
   # macOS
   ls -la "release/mac/智慧教育下载器.app/Contents/"
   
   # Windows
   ls -la "release/win-unpacked/"
   
   # Linux
   ls -la "release/linux-unpacked/"
   ```

## 持续集成

### GitHub Actions

创建 `.github/workflows/build.yml`:

```yaml
name: Build and Release

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [macos-latest, windows-latest, ubuntu-latest]
    
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - run: npm ci
      - run: npm run build
      - run: npm run dist
      
      - uses: actions/upload-artifact@v3
        with:
          name: release-${{ matrix.os }}
          path: release/
```

## 安全考虑

1. **代码签名**: 为发布的应用进行代码签名
2. **权限最小化**: 只请求必要的系统权限
3. **更新验证**: 验证更新包的完整性
4. **依赖审计**: 定期审计依赖包的安全性

## 维护建议

1. **定期更新**: 保持依赖包和 Electron 版本更新
2. **监控反馈**: 收集用户反馈和错误报告
3. **性能优化**: 定期优化应用性能和包大小
4. **文档更新**: 保持部署文档的及时更新