/**
 * 渲染进程下载管理器
 * 通过 IPC 与主进程的下载服务通信，避免在渲染进程中直接使用 Node.js 模块
 */

import { DownloadTask, DownloadProgress, AppConfig } from '../../shared/types';

export class RendererDownloadManager {
  private tasks: Map<string, DownloadTask> = new Map();
  private progressCallbacks: Map<string, (progress: DownloadProgress) => void> = new Map();

  constructor() {
    this.setupIpcListeners();
  }

  /**
   * 设置 IPC 监听器
   */
  private setupIpcListeners(): void {
    // 监听下载进度更新
    if (window.electronAPI?.onDownloadProgress) {
      window.electronAPI.onDownloadProgress((taskId: string, progress: DownloadProgress) => {
        const callback = this.progressCallbacks.get(taskId);
        if (callback) {
          callback(progress);
        }
      });
    }

    // 监听下载状态变化
    if (window.electronAPI?.onDownloadStatusChange) {
      window.electronAPI.onDownloadStatusChange((taskId: string, status: string) => {
        const task = this.tasks.get(taskId);
        if (task) {
          task.status = status as any;
          this.tasks.set(taskId, task);
        }
      });
    }
  }

  /**
   * 开始下载任务
   */
  async startDownload(
    url: string,
    config: AppConfig,
    onProgress?: (progress: DownloadProgress) => void
  ): Promise<string> {
    try {
      if (!window.electronAPI?.startDownload) {
        throw new Error('下载 API 不可用');
      }

      const taskId = await window.electronAPI.startDownload(url, config);
      
      // 创建任务记录
      const task: DownloadTask = {
        id: taskId,
        url,
        status: 'pending',
        progress: 0,
        createdAt: new Date(),
        config,
      };

      this.tasks.set(taskId, task);

      // 注册进度回调
      if (onProgress) {
        this.progressCallbacks.set(taskId, onProgress);
      }

      return taskId;
    } catch (error) {
      console.error('启动下载失败:', error);
      throw error;
    }
  }

  /**
   * 暂停下载
   */
  async pauseDownload(taskId: string): Promise<void> {
    try {
      if (!window.electronAPI?.pauseDownload) {
        throw new Error('暂停下载 API 不可用');
      }

      await window.electronAPI.pauseDownload(taskId);
      
      const task = this.tasks.get(taskId);
      if (task) {
        task.status = 'paused';
        this.tasks.set(taskId, task);
      }
    } catch (error) {
      console.error('暂停下载失败:', error);
      throw error;
    }
  }

  /**
   * 恢复下载
   */
  async resumeDownload(taskId: string): Promise<void> {
    try {
      if (!window.electronAPI?.resumeDownload) {
        throw new Error('恢复下载 API 不可用');
      }

      await window.electronAPI.resumeDownload(taskId);
      
      const task = this.tasks.get(taskId);
      if (task) {
        task.status = 'downloading';
        this.tasks.set(taskId, task);
      }
    } catch (error) {
      console.error('恢复下载失败:', error);
      throw error;
    }
  }

  /**
   * 取消下载
   */
  async cancelDownload(taskId: string): Promise<void> {
    try {
      if (!window.electronAPI?.cancelDownload) {
        throw new Error('取消下载 API 不可用');
      }

      await window.electronAPI.cancelDownload(taskId);
      
      // 清理本地记录
      this.tasks.delete(taskId);
      this.progressCallbacks.delete(taskId);
    } catch (error) {
      console.error('取消下载失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有下载任务
   */
  async getAllTasks(): Promise<DownloadTask[]> {
    try {
      if (!window.electronAPI?.getAllDownloadTasks) {
        return Array.from(this.tasks.values());
      }

      const tasks = await window.electronAPI.getAllDownloadTasks();
      
      // 更新本地缓存
      tasks.forEach(task => {
        this.tasks.set(task.id, task);
      });

      return tasks;
    } catch (error) {
      console.error('获取下载任务失败:', error);
      return Array.from(this.tasks.values());
    }
  }

  /**
   * 获取单个下载任务
   */
  getTask(taskId: string): DownloadTask | undefined {
    return this.tasks.get(taskId);
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this.tasks.clear();
    this.progressCallbacks.clear();
  }
}
