import axios from 'axios';
import * as fs from 'fs-extra';
import * as path from 'path';
import { EventEmitter } from 'events';
import {
  M3U8Playlist,
  M3U8Segment,
  CourseResource,
  DownloadProgress,
  M3U8Error,
  NetworkError,
  FileError,
  NetworkStatus,
  ResumeEventType
} from '../types';
import { M3U8Parser } from './M3U8Parser';
import { VideoMerger, MergeProgress } from './VideoMerger';
import { RetryManager } from './RetryManager';
import { FileOrganizer } from './FileOrganizer';
import { ResumeStateManager, ResumeState, SegmentState } from './ResumeStateManager';
import { NetworkMonitor } from './NetworkMonitor';

/**
 * M3U8下载器配置
 */
export interface M3U8DownloaderConfig {
  maxConcurrentDownloads: number;
  segmentTimeout: number;
  maxRetries: number;
  retryDelay: number;
  tempDir: string;
  userAgent: string;
  deleteSegmentsAfterMerge: boolean;
  // 断点续传相关配置
  enableResume: boolean;
  resumeStateDir: string;
  networkMonitoring: boolean;
  autoResumeOnReconnect: boolean;
  segmentValidation: boolean;
}

/**
 * 下载状态
 */
export interface M3U8DownloadStatus {
  phase: 'parsing' | 'downloading' | 'merging' | 'completed' | 'failed' | 'resuming' | 'paused';
  totalSegments: number;
  downloadedSegments: number;
  failedSegments: number;
  currentSegment?: number;
  progress: number; // 0-100
  speed: number; // bytes per second
  estimatedTime: number; // seconds
  error?: string;
  // 断点续传相关状态
  isResuming?: boolean;
  resumedFromProgress?: number;
  networkStatus?: NetworkStatus;
  canResume?: boolean;
}

/**
 * M3U8视频下载器
 * 负责下载M3U8视频流并合并为MP4文件
 */
export class M3U8Downloader extends EventEmitter {
  private config: M3U8DownloaderConfig;
  private parser: M3U8Parser;
  private merger: VideoMerger;
  private retryManager: RetryManager;
  private fileOrganizer: FileOrganizer;
  private resumeStateManager!: ResumeStateManager;
  private networkMonitor!: NetworkMonitor;

  private activeDownloads: Map<string, AbortController> = new Map();
  private downloadStats: Map<string, { startTime: number; downloadedBytes: number }> = new Map();
  private pausedTasks: Set<string> = new Set();

  private readonly defaultConfig: M3U8DownloaderConfig = {
    maxConcurrentDownloads: 5,
    segmentTimeout: 30000, // 30 seconds
    maxRetries: 3,
    retryDelay: 1000, // 1 second
    tempDir: path.join(process.cwd(), 'temp'),
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    deleteSegmentsAfterMerge: true,
    // 断点续传默认配置
    enableResume: true,
    resumeStateDir: path.join(process.cwd(), '.download-states'),
    networkMonitoring: true,
    autoResumeOnReconnect: true,
    segmentValidation: true
  };

  constructor(
    fileOrganizer: FileOrganizer,
    config?: Partial<M3U8DownloaderConfig>
  ) {
    super();
    this.config = { ...this.defaultConfig, ...config };
    this.fileOrganizer = fileOrganizer;

    // 初始化组件
    this.parser = new M3U8Parser({
      timeout: this.config.segmentTimeout,
      maxRetries: this.config.maxRetries,
      retryDelay: this.config.retryDelay,
      userAgent: this.config.userAgent
    });

    this.merger = new VideoMerger({
      tempDir: this.config.tempDir,
      deleteSegments: this.config.deleteSegmentsAfterMerge
    });

    this.retryManager = new RetryManager({
      maxRetries: this.config.maxRetries,
      baseDelay: this.config.retryDelay,
      strategy: 'exponential'
    });

    // 初始化断点续传管理器
    if (this.config.enableResume) {
      this.resumeStateManager = new ResumeStateManager({
        stateDir: this.config.resumeStateDir,
        enableChecksum: this.config.segmentValidation,
        autoCleanup: true
      });
    }

    // 初始化网络监控器
    if (this.config.networkMonitoring) {
      this.networkMonitor = new NetworkMonitor(5000);
    }

    // 设置事件监听
    this.setupEventListeners();
  }

  /**
   * 下载M3U8视频
   * @param resource 视频资源
   * @param onProgress 进度回调
   */
  async downloadVideo(
    resource: CourseResource,
    onProgress?: (progress: DownloadProgress) => void
  ): Promise<string> {
    const taskId = this.generateTaskId(resource);

    try {
      console.log(`🎥 开始下载M3U8视频: ${resource.title}`);
      console.log(`📍 视频URL: ${resource.url}`);

      // 检查资源类型
      if (resource.type !== 'video') {
        throw new M3U8Error('资源类型不是视频');
      }

      // 验证M3U8 URL
      if (!M3U8Parser.isM3U8Url(resource.url)) {
        console.log(`⚠️ URL不是标准M3U8格式，尝试智能检测: ${resource.url}`);

        // 尝试智能检测是否为视频资源
        if (!this.isVideoResource(resource.url)) {
          throw new M3U8Error('不是有效的视频资源URL');
        }
      }

      // 生成输出路径
      const outputPath = this.fileOrganizer.generatePath(resource);
      await fs.ensureDir(path.dirname(outputPath));
      console.log(`📁 输出路径: ${outputPath}`);

      // 检查文件是否已存在
      if (await fs.pathExists(outputPath)) {
        const shouldOverwrite = await this.checkFileOverwrite(outputPath, resource);
        if (!shouldOverwrite) {
          console.log(`✅ 文件已存在，跳过下载: ${outputPath}`);
          return outputPath;
        }
      }

      // 检查是否可以恢复下载
      if (this.config.enableResume && this.resumeStateManager) {
        const canResume = await this.resumeStateManager.canResume(taskId);
        if (canResume) {
          console.log(`🔄 恢复断点续传下载`);
          return this.resumeDownload(resource, onProgress);
        }
      }

      // 创建下载状态
      const status: M3U8DownloadStatus = {
        phase: 'parsing',
        totalSegments: 0,
        downloadedSegments: 0,
        failedSegments: 0,
        progress: 0,
        speed: 0,
        estimatedTime: 0,
        canResume: this.config.enableResume,
        networkStatus: this.networkMonitor?.getCurrentStatus() as any
      };

      this.emit('status', taskId, status);

      // 解析M3U8播放列表
      const playlist = await this.parser.parsePlaylist(resource.url);

      status.phase = 'downloading';
      status.totalSegments = playlist.segments.length;
      this.emit('status', taskId, status);

      // 创建临时目录
      const tempDir = await this.createTempDir(taskId);

      // 初始化断点续传状态
      if (this.config.enableResume && this.resumeStateManager) {
        const resumeState: ResumeState = {
          taskId,
          resource,
          outputPath,
          tempDir,
          totalSegments: playlist.segments.length,
          downloadedSegments: [],
          failedSegments: [],
          lastUpdateTime: new Date(),
          playlistUrl: resource.url,
          baseUrl: playlist.baseUrl
        };

        await this.resumeStateManager.saveState(resumeState);
      }
      
      // 下载所有片段
      const segmentPaths = await this.downloadSegments(
        playlist, 
        tempDir, 
        taskId,
        (progress) => {
          status.downloadedSegments = progress.completedSegments;
          status.failedSegments = progress.failedSegments;
          status.progress = Math.round((progress.completedSegments / status.totalSegments) * 80); // 80% for download
          status.speed = progress.speed;
          status.estimatedTime = progress.estimatedTime;
          status.currentSegment = progress.currentSegment;
          
          this.emit('status', taskId, status);
          
          if (onProgress) {
            onProgress({
              taskId: '',
              progress: status.progress,
              speed: status.speed,
              estimatedTime: status.estimatedTime,
              downloadedBytes: 0,
              totalBytes: 0,
              status: 'downloading' as any
            });
          }
        }
      );

      // 合并视频片段
      status.phase = 'merging';
      status.progress = 80;
      this.emit('status', taskId, status);

      await this.mergeVideoSegments(segmentPaths, outputPath, (mergeProgress) => {
        status.progress = 80 + Math.round(mergeProgress.progress * 0.2); // 20% for merge
        this.emit('status', taskId, status);
        
        if (onProgress) {
          onProgress({
            taskId: '',
            progress: status.progress,
            speed: 0,
            estimatedTime: 0,
            downloadedBytes: 0,
            totalBytes: 0,
            status: 'downloading' as any
          });
        }
      });

      // 清理临时文件
      await this.cleanupTempDir(tempDir);

      // 完成
      status.phase = 'completed';
      status.progress = 100;
      this.emit('status', taskId, status);
      
      if (onProgress) {
        onProgress({
          taskId: '',
          progress: 100,
          speed: 0,
          estimatedTime: 0,
          downloadedBytes: 0,
          totalBytes: 0,
          status: 'completed' as any
        });
      }

      return outputPath;
    } catch (error) {
      const status: M3U8DownloadStatus = {
        phase: 'failed',
        totalSegments: 0,
        downloadedSegments: 0,
        failedSegments: 0,
        progress: 0,
        speed: 0,
        estimatedTime: 0,
        error: error instanceof Error ? error.message : '未知错误'
      };
      
      this.emit('status', taskId, status);
      throw error;
    } finally {
      // 清理活动下载
      this.activeDownloads.delete(taskId);
      this.downloadStats.delete(taskId);
    }
  }

  /**
   * 下载视频片段
   */
  private async downloadSegments(
    playlist: M3U8Playlist,
    tempDir: string,
    taskId: string,
    onProgress: (progress: {
      completedSegments: number;
      failedSegments: number;
      currentSegment: number;
      speed: number;
      estimatedTime: number;
    }) => void
  ): Promise<string[]> {
    const segments = playlist.segments;
    const segmentPaths: string[] = [];
    let completedSegments = 0;
    let failedSegments = 0;
    const startTime = Date.now();
    let totalDownloadedBytes = 0;

    // 创建并发下载队列
    const downloadQueue = segments.map((segment, index) => ({
      segment,
      index,
      outputPath: path.join(tempDir, `segment_${String(index).padStart(6, '0')}.ts`)
    }));

    // 并发下载控制
    const concurrentDownloads = Math.min(this.config.maxConcurrentDownloads, segments.length);
    const downloadPromises: Promise<void>[] = [];

    for (let i = 0; i < concurrentDownloads; i++) {
      downloadPromises.push(this.downloadWorker(downloadQueue, segmentPaths, taskId, async (segmentIndex, bytes) => {
        totalDownloadedBytes += bytes;
        completedSegments++;

        // 更新断点续传状态
        if (this.config.enableResume && this.resumeStateManager) {
          await this.resumeStateManager.updateSegmentState(taskId, segmentIndex, {
            index: segmentIndex,
            url: segments[segmentIndex].url,
            filePath: segmentPaths[segmentIndex],
            size: bytes,
            downloadedAt: new Date(),
            isComplete: true
          });
        }

        const elapsedTime = (Date.now() - startTime) / 1000;
        const speed = totalDownloadedBytes / elapsedTime;
        const remainingSegments = segments.length - completedSegments;
        const estimatedTime = remainingSegments > 0 ? (remainingSegments * elapsedTime) / completedSegments : 0;

        onProgress({
          completedSegments,
          failedSegments,
          currentSegment: segmentIndex,
          speed,
          estimatedTime
        });
      }));
    }

    await Promise.all(downloadPromises);

    if (failedSegments > 0) {
      throw new M3U8Error(`${failedSegments} 个视频片段下载失败`);
    }

    return segmentPaths;
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(resource: CourseResource): string {
    return `m3u8_${resource.id}_${Date.now()}`;
  }

  /**
   * 智能检测是否为视频资源
   */
  private isVideoResource(url: string): boolean {
    try {
      const urlLower = url.toLowerCase();

      // 检查URL中是否包含视频相关关键词
      const videoKeywords = [
        'm3u8', 'mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv',
        'video', 'stream', 'media', 'play', 'watch'
      ];

      const hasVideoKeyword = videoKeywords.some(keyword => urlLower.includes(keyword));

      // 检查是否为智慧教育平台的视频URL模式
      const isSmartEduVideo = urlLower.includes('ykt.cbern.com.cn') &&
                             (urlLower.includes('video') || urlLower.includes('media'));

      return hasVideoKeyword || isSmartEduVideo;
    } catch (error) {
      console.warn('视频资源检测失败:', error);
      return false;
    }
  }

  /**
   * 创建临时目录
   */
  private async createTempDir(taskId: string): Promise<string> {
    const tempDir = path.join(this.config.tempDir, taskId);
    await fs.ensureDir(tempDir);
    return tempDir;
  }

  /**
   * 清理临时目录
   */
  private async cleanupTempDir(tempDir: string): Promise<void> {
    try {
      if (await fs.pathExists(tempDir)) {
        await fs.remove(tempDir);
      }
    } catch (error) {
      console.warn(`清理临时目录失败: ${tempDir}`, error);
    }
  }

  /**
   * 检查文件覆盖
   */
  private async checkFileOverwrite(filePath: string, resource: CourseResource): Promise<boolean> {
    // 简单实现：总是覆盖，实际应用中可以检查文件大小、修改时间等
    return true;
  }

  /**
   * 取消下载
   */
  cancelDownload(taskId: string): void {
    const controller = this.activeDownloads.get(taskId);
    if (controller) {
      controller.abort();
      this.activeDownloads.delete(taskId);
    }
  }

  /**
   * 暂停下载
   */
  async pauseDownload(taskId: string): Promise<void> {
    this.pausedTasks.add(taskId);
    this.cancelDownload(taskId);

    // 保存当前状态以便后续恢复
    if (this.config.enableResume && this.resumeStateManager) {
      const state = this.resumeStateManager.getAllActiveStates().get(taskId);
      if (state) {
        await this.resumeStateManager.saveState(state);
      }
    }

    this.emit('download-paused', taskId);
  }

  /**
   * 恢复下载
   */
  async resumeDownload(
    resource: CourseResource,
    onProgress?: (progress: DownloadProgress) => void
  ): Promise<string> {
    const taskId = this.generateTaskId(resource);

    if (!this.config.enableResume || !this.resumeStateManager) {
      throw new Error('断点续传功能未启用');
    }

    // 检查是否可以恢复
    const canResume = await this.resumeStateManager.canResume(taskId);
    if (!canResume) {
      // 如果不能恢复，则重新开始下载
      return this.downloadVideo(resource, onProgress);
    }

    // 加载断点续传状态
    const resumeState = await this.resumeStateManager.loadState(taskId);
    if (!resumeState) {
      throw new Error('无法加载断点续传状态');
    }

    this.pausedTasks.delete(taskId);

    // 开始恢复下载
    return this.resumeVideoDownload(resumeState, onProgress);
  }

  /**
   * 检查任务是否可以恢复
   */
  async canResumeTask(taskId: string): Promise<boolean> {
    if (!this.config.enableResume || !this.resumeStateManager) {
      return false;
    }

    return await this.resumeStateManager.canResume(taskId);
  }

  /**
   * 下载工作器
   */
  private async downloadWorker(
    queue: Array<{ segment: M3U8Segment; index: number; outputPath: string }>,
    segmentPaths: string[],
    taskId: string,
    onSegmentComplete: (segmentIndex: number, bytes: number) => Promise<void>
  ): Promise<void> {
    while (queue.length > 0) {
      const item = queue.shift();
      if (!item) break;

      try {
        // 检查任务是否被暂停
        if (this.pausedTasks.has(taskId)) {
          break;
        }

        const downloadedBytes = await this.downloadSegment(item.segment, item.outputPath);
        segmentPaths[item.index] = item.outputPath;
        await onSegmentComplete(item.index, downloadedBytes);
      } catch (error) {
        console.error(`下载片段失败: ${item.segment.url}`, error);

        // 标记片段为失败
        if (this.config.enableResume && this.resumeStateManager) {
          await this.resumeStateManager.markSegmentFailed(taskId, item.index);
        }

        throw error;
      }
    }
  }

  /**
   * 下载单个视频片段
   */
  private async downloadSegment(segment: M3U8Segment, outputPath: string): Promise<number> {
    const result = await this.retryManager.executeWithRetry(async () => {
      const response = await axios.get(segment.url, {
        timeout: this.config.segmentTimeout,
        responseType: 'stream',
        headers: {
          'User-Agent': this.config.userAgent
        }
      });

      if (response.status !== 200) {
        throw new NetworkError(`HTTP ${response.status}: ${response.statusText}`);
      }

      const writer = fs.createWriteStream(outputPath);
      response.data.pipe(writer);

      return new Promise<number>((resolve, reject) => {
        let downloadedBytes = 0;

        response.data.on('data', (chunk: Buffer) => {
          downloadedBytes += chunk.length;
        });

        writer.on('finish', () => resolve(downloadedBytes));
        writer.on('error', reject);
        response.data.on('error', reject);
      });
    });
    
    if (result.success && result.result !== undefined) {
      return result.result;
    } else {
      throw result.error || new Error('Download failed');
    }
  }

  /**
   * 合并视频片段
   */
  private async mergeVideoSegments(
    segmentPaths: string[],
    outputPath: string,
    onProgress: (progress: MergeProgress) => void
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      this.merger.on('progress', onProgress);
      this.merger.on('completed', resolve);
      this.merger.on('error', reject);

      this.merger.mergeSegments(segmentPaths, outputPath, {
        outputPath,
        format: 'mp4',
        deleteSegments: this.config.deleteSegmentsAfterMerge
      });
    });
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 网络状态监听
    if (this.networkMonitor) {
      this.networkMonitor.on('network-reconnected', () => {
        if (this.config.autoResumeOnReconnect) {
          this.handleNetworkReconnection();
        }
      });

      this.networkMonitor.on('network-offline', () => {
        this.handleNetworkDisconnection();
      });

      // 启动网络监控
      this.networkMonitor.startMonitoring();
    }

    // 断点续传状态管理器事件监听
    if (this.resumeStateManager) {
      this.resumeStateManager.on('error', (error) => {
        this.emit('resume-error', error);
      });
    }
  }

  /**
   * 处理网络重连
   */
  private async handleNetworkReconnection(): Promise<void> {
    if (!this.resumeStateManager) return;

    const activeStates = this.resumeStateManager.getAllActiveStates();

    for (const [taskId, state] of activeStates) {
      if (this.pausedTasks.has(taskId)) {
        try {
          // 尝试恢复暂停的任务
          await this.resumeVideoDownload(state);
          this.emit('auto-resume-success', taskId);
        } catch (error) {
          this.emit('auto-resume-failed', taskId, error);
        }
      }
    }
  }

  /**
   * 处理网络断开
   */
  private handleNetworkDisconnection(): void {
    // 暂停所有活跃的下载任务
    for (const taskId of this.activeDownloads.keys()) {
      this.pauseDownload(taskId);
    }
  }

  /**
   * 恢复视频下载的核心方法
   */
  private async resumeVideoDownload(
    resumeState: ResumeState,
    onProgress?: (progress: DownloadProgress) => void
  ): Promise<string> {
    const taskId = resumeState.taskId;

    try {
      // 创建下载状态
      const status: M3U8DownloadStatus = {
        phase: 'resuming',
        totalSegments: resumeState.totalSegments,
        downloadedSegments: resumeState.downloadedSegments.filter(s => s.isComplete).length,
        failedSegments: resumeState.failedSegments.length,
        progress: this.resumeStateManager.calculateProgress(taskId),
        speed: 0,
        estimatedTime: 0,
        isResuming: true,
        resumedFromProgress: this.resumeStateManager.calculateProgress(taskId),
        canResume: true
      };

      this.emit('status', taskId, status);

      // 解析M3U8播放列表（可能需要重新获取）
      const playlist = await this.parser.parsePlaylist(resumeState.playlistUrl);

      // 验证已下载的片段
      await this.validateDownloadedSegments(resumeState);

      // 继续下载剩余片段
      status.phase = 'downloading';
      this.emit('status', taskId, status);

      const segmentPaths = await this.resumeSegmentDownload(
        playlist,
        resumeState,
        taskId,
        (progress) => {
          status.downloadedSegments = progress.completedSegments;
          status.failedSegments = progress.failedSegments;
          status.progress = Math.round((progress.completedSegments / status.totalSegments) * 80);
          status.speed = progress.speed;
          status.estimatedTime = progress.estimatedTime;
          status.currentSegment = progress.currentSegment;

          this.emit('status', taskId, status);

          if (onProgress) {
            onProgress({
              taskId,
              progress: status.progress,
              speed: status.speed,
              estimatedTime: status.estimatedTime,
              downloadedBytes: 0,
              totalBytes: 0,
              status: 'downloading',
              isResuming: true,
              segmentsCompleted: progress.completedSegments,
              segmentsTotal: status.totalSegments
            });
          }
        }
      );

      // 合并视频片段
      status.phase = 'merging';
      status.progress = 80;
      this.emit('status', taskId, status);

      await this.mergeVideoSegments(segmentPaths, resumeState.outputPath, (mergeProgress) => {
        status.progress = 80 + Math.round(mergeProgress.progress * 0.2);
        this.emit('status', taskId, status);

        if (onProgress) {
          onProgress({
            taskId,
            progress: status.progress,
            speed: 0,
            estimatedTime: 0,
            downloadedBytes: 0,
            totalBytes: 0,
            status: 'downloading'
          });
        }
      });

      // 清理临时文件和状态
      await this.cleanupTempDir(resumeState.tempDir);
      await this.resumeStateManager.removeState(taskId);

      // 完成
      status.phase = 'completed';
      status.progress = 100;
      status.isResuming = false;
      this.emit('status', taskId, status);

      if (onProgress) {
        onProgress({
          taskId,
          progress: 100,
          speed: 0,
          estimatedTime: 0,
          downloadedBytes: 0,
          totalBytes: 0,
          status: 'completed'
        });
      }

      this.emit('resume-completed', taskId, resumeState.outputPath);
      return resumeState.outputPath;

    } catch (error) {
      this.emit('resume-failed', taskId, error);
      throw error;
    }
  }

  /**
   * 验证已下载的片段
   */
  private async validateDownloadedSegments(resumeState: ResumeState): Promise<void> {
    if (!this.config.segmentValidation) return;

    const validSegments: SegmentState[] = [];

    for (const segment of resumeState.downloadedSegments) {
      if (segment.isComplete) {
        const isValid = await this.resumeStateManager.validateSegmentFile(segment);
        if (isValid) {
          validSegments.push(segment);
        } else {
          // 标记为需要重新下载
          resumeState.failedSegments.push(segment.index);
        }
      }
    }

    resumeState.downloadedSegments = validSegments;
    await this.resumeStateManager.saveState(resumeState);
  }

  /**
   * 恢复片段下载
   */
  private async resumeSegmentDownload(
    playlist: M3U8Playlist,
    resumeState: ResumeState,
    taskId: string,
    onProgress: (progress: {
      completedSegments: number;
      failedSegments: number;
      currentSegment: number;
      speed: number;
      estimatedTime: number;
    }) => void
  ): Promise<string[]> {
    const segments = playlist.segments;
    const segmentPaths: string[] = [];
    const downloadedSegments = resumeState.downloadedSegments.filter(s => s.isComplete);
    let completedSegments = downloadedSegments.length;
    let failedSegments = resumeState.failedSegments.length;
    const startTime = Date.now();
    let totalDownloadedBytes = 0;

    // 初始化片段路径数组
    for (let i = 0; i < segments.length; i++) {
      const existingSegment = downloadedSegments.find(s => s.index === i);
      if (existingSegment) {
        segmentPaths[i] = existingSegment.filePath;
      } else {
        segmentPaths[i] = path.join(resumeState.tempDir, `segment_${String(i).padStart(6, '0')}.ts`);
      }
    }

    // 创建需要下载的片段队列（跳过已下载的）
    const downloadQueue = segments
      .map((segment, index) => ({ segment, index, outputPath: segmentPaths[index] }))
      .filter(item => {
        const isDownloaded = downloadedSegments.some(s => s.index === item.index);
        const isFailed = resumeState.failedSegments.includes(item.index);
        return !isDownloaded || isFailed;
      });

    // 并发下载剩余片段
    const concurrency = this.networkMonitor ?
      3 :
      this.config.maxConcurrentDownloads;

    const downloadPromises: Promise<void>[] = [];
    let currentIndex = 0;

    const processNext = async (): Promise<void> => {
      if (currentIndex >= downloadQueue.length) return;

      const item = downloadQueue[currentIndex++];

      try {
        if (this.pausedTasks.has(taskId)) {
          return; // 任务已暂停
        }

        const downloadedBytes = await this.downloadSegment(item.segment, item.outputPath);
        totalDownloadedBytes += downloadedBytes;
        completedSegments++;

        // 更新断点续传状态
        await this.resumeStateManager.updateSegmentState(taskId, item.index, {
          index: item.index,
          url: item.segment.url,
          filePath: item.outputPath,
          size: downloadedBytes,
          downloadedAt: new Date(),
          isComplete: true
        });

        // 计算进度
        const elapsed = Date.now() - startTime;
        const speed = elapsed > 0 ? (totalDownloadedBytes / elapsed) * 1000 : 0;
        const remaining = segments.length - completedSegments;
        const estimatedTime = speed > 0 ? (remaining * (totalDownloadedBytes / completedSegments)) / speed : 0;

        onProgress({
          completedSegments,
          failedSegments,
          currentSegment: item.index,
          speed,
          estimatedTime
        });

      } catch (error) {
        failedSegments++;
        await this.resumeStateManager.markSegmentFailed(taskId, item.index);

        onProgress({
          completedSegments,
          failedSegments,
          currentSegment: item.index,
          speed: 0,
          estimatedTime: 0
        });
      }
    };

    // 启动并发下载
    for (let i = 0; i < Math.min(concurrency, downloadQueue.length); i++) {
      downloadPromises.push(processNext());
    }

    // 等待所有下载完成
    await Promise.all(downloadPromises);

    // 处理剩余的下载任务
    while (currentIndex < downloadQueue.length) {
      await processNext();
    }

    if (failedSegments > 0) {
      throw new Error(`${failedSegments} 个视频片段下载失败`);
    }

    return segmentPaths;
  }
}

export default M3U8Downloader;
