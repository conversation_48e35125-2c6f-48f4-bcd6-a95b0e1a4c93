import { app, BrowserWindow, ipcMain, dialog, shell, Notification } from 'electron';
import * as path from 'path';
import * as fs from 'fs-extra';
import * as crypto from 'crypto';
import axios from 'axios';
import { AutoUpdater } from './services/AutoUpdater';
import { DevToolsManager } from './utils/devtools';

class SmartEduDownloaderApp {
  private mainWindow: BrowserWindow | null = null;
  private autoUpdater: AutoUpdater | null = null;
  private devToolsManager: DevToolsManager | null = null;

  constructor() {
    this.initializeApp();
  }

  private initializeApp(): void {
    // 设置全局错误处理
    process.on('uncaughtException', (error) => {
      console.error('Uncaught Exception:', error);
      // 不要因为 DevTools 错误而退出应用
      if (!error.message.includes('devtools://') && !error.message.includes('Failed to fetch')) {
        // 只有在非 DevTools 错误时才考虑退出
      }
    });

    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    });

    // Handle app ready
    app.whenReady().then(() => {
      this.createMainWindow();
      this.setupIpcHandlers();

      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createMainWindow();
        }
      });
    });

    // Handle app window closed
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });
  }

  private createMainWindow(): void {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: process.env.NODE_ENV === 'development', // 开发环境启用 Node.js 集成
        contextIsolation: process.env.NODE_ENV === 'production', // 生产环境启用上下文隔离
        preload: path.join(__dirname, 'preload.js'),
        webSecurity: process.env.NODE_ENV === 'production', // 开发环境禁用web安全以修复DevTools错误
        allowRunningInsecureContent: process.env.NODE_ENV === 'development',
        // enableRemoteModule 在新版本 Electron 中已被移除
      },
      titleBarStyle: 'default',
      show: false,
    });

    // 初始化 DevTools 管理器
    this.devToolsManager = new DevToolsManager(this.mainWindow);

    // 设置键盘快捷键
    this.setupKeyboardShortcuts();

    // Load the renderer
    if (process.env.NODE_ENV === 'development') {
      this.mainWindow.loadURL('http://localhost:3000');

      // 使用 DevTools 管理器安全地打开开发工具
      this.devToolsManager.openDevTools();
    } else {
      this.mainWindow.loadFile(path.join(__dirname, '../renderer/index.html'));
    }

    // Show window when ready
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow?.show();
      
      // 初始化自动更新（仅在生产环境）
      if (process.env.NODE_ENV !== 'development' && this.mainWindow) {
        this.autoUpdater = new AutoUpdater(this.mainWindow);
        // 启动后延迟5秒检查更新，避免影响启动速度
        setTimeout(() => {
          this.autoUpdater?.checkForUpdates();
        }, 5000);
      }
    });

    this.mainWindow.on('closed', () => {
      // 清理 DevTools 管理器
      if (this.devToolsManager) {
        this.devToolsManager.dispose();
        this.devToolsManager = null;
      }

      this.mainWindow = null;
      this.autoUpdater = null;
    });
  }

  /**
   * 构建请求选项
   */
  private async buildRequestOptions(): Promise<any> {
    // 设置请求选项，添加必要的请求头和Cookie
    const requestOptions: any = {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0',
        'Referer': 'https://basic.smartedu.cn/'
      }
    };

    // 尝试从主窗口获取Cookie和生成X-Nd-Auth
    try {
      const { session } = require('electron');

      // 尝试多个可能的域名
      const domains = ['.ykt.cbern.com.cn', '.smartedu.cn', '.basic.smartedu.cn', 'smartedu.cn', 'ykt.cbern.com.cn'];
      let allCookies: any[] = [];

      for (const domain of domains) {
        const domainCookies = await session.defaultSession.cookies.get({ domain });
        if (domainCookies.length > 0) {
          console.log(`🍪 从域名 ${domain} 获取到 ${domainCookies.length} 个Cookie`);
          allCookies = allCookies.concat(domainCookies);
        }
      }

      if (allCookies.length > 0) {
        const cookieString = allCookies.map((cookie: any) => `${cookie.name}=${cookie.value}`).join('; ');
        requestOptions.headers['Cookie'] = cookieString;
        console.log(`🍪 使用Cookie进行认证 (共${allCookies.length}个): ${cookieString.substring(0, 100)}...`);

        // 生成X-Nd-Auth请求头（智慧教育平台必需）
        const crypto = require('crypto');
        const timestamp = Math.floor(Date.now() / 1000);
        const nonce = Math.random().toString(36).substring(2, 15);

        // 尝试多种认证方式
        // 方式1：基本的MAC认证
        const authString1 = `MAC id="anonymous",ts="${timestamp}",nonce="${nonce}",mac=""`;

        // 方式2：简化认证
        const authString2 = `anonymous`;

        // 方式3：空认证
        const authString3 = ``;

        // 先尝试基本认证
        requestOptions.headers['X-Nd-Auth'] = authString1;
        requestOptions.headers['X-Nd-Checksum'] = 'anonymous';

        console.log(`🔐 添加X-Nd-Auth认证头: ${authString1}`);
      } else {
        console.log(`⚠️ 未找到相关域名的Cookie，可能需要先在浏览器中登录`);

        // 即使没有Cookie，也尝试添加匿名认证头
        const crypto = require('crypto');
        const timestamp = Math.floor(Date.now() / 1000);
        const nonce = Math.random().toString(36).substring(2, 15);

        // 尝试不同的认证方式
        // 先尝试完全匿名（可能某些资源不需要认证）
        console.log(`🔐 尝试匿名访问（无认证头）`);
      }
    } catch (cookieError: any) {
      console.log(`⚠️ 获取Cookie失败: ${cookieError?.message || cookieError}`);
    }

    return requestOptions;
  }

  /**
   * 调用API并处理认证（带重试机制）
   */
  private async callApiWithAuth(url: string, requestOptions: any): Promise<any> {
    // 尝试不同的认证方式
    const authMethods = [
      // 方法1：使用Cookie + X-Nd-Auth
      { ...requestOptions },
      // 方法2：只使用Cookie，移除X-Nd-Auth
      {
        ...requestOptions,
        headers: {
          ...requestOptions.headers,
          'X-Nd-Auth': undefined,
          'X-Nd-Checksum': undefined
        }
      },
      // 方法3：完全匿名，无Cookie无认证头
      {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
          'Accept': 'application/json,text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        }
      }
    ];

    for (let i = 0; i < authMethods.length; i++) {
      const options = authMethods[i];
      console.log(`🔄 尝试认证方法 ${i + 1}/${authMethods.length}`);

      try {
        const result = await this.makeApiRequest(url, options);
        console.log(`✅ 认证方法 ${i + 1} 成功`);
        return result;
      } catch (error: any) {
        console.log(`❌ 认证方法 ${i + 1} 失败: ${error?.message || error}`);
        if (i === authMethods.length - 1) {
          // 所有方法都失败了
          throw error;
        }
      }
    }
  }

  /**
   * 使用浏览器会话下载文件
   */
  private async downloadWithBrowser(url: string, filePath: string, taskId: string): Promise<boolean> {
    return new Promise((resolve) => {
      try {
        const { BrowserWindow } = require('electron');

        // 创建隐藏的浏览器窗口用于下载
        const downloadWindow = new BrowserWindow({
          width: 800,
          height: 600,
          show: false, // 隐藏窗口
          webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            webSecurity: true
          }
        });

        // 监听下载事件
        downloadWindow.webContents.session.on('will-download', (event: any, item: any, webContents: any) => {
          console.log(`📥 浏览器开始下载: ${item.getFilename()}`);

          // 设置下载路径
          item.setSavePath(filePath);

          // 监听下载完成
          item.once('done', (event: any, state: any) => {
            if (state === 'completed') {
              console.log(`✅ 浏览器下载完成: ${filePath}`);
              downloadWindow.close();
              resolve(true);
            } else {
              console.log(`❌ 浏览器下载失败: ${state}`);
              downloadWindow.close();
              resolve(false);
            }
          });
        });

        // 监听页面加载事件
        downloadWindow.webContents.on('did-finish-load', () => {
          console.log(`📄 页面加载完成`);

          // 检查页面内容，看是否有下载链接
          downloadWindow.webContents.executeJavaScript(`
            // 查找可能的下载链接
            const links = Array.from(document.querySelectorAll('a[href*=".mp4"], a[href*=".pdf"], a[href*=".pptx"], a[href*="download"]'));
            if (links.length > 0) {
              console.log('找到下载链接:', links[0].href);
              links[0].click(); // 尝试点击第一个下载链接
              return links[0].href;
            }

            // 如果没有找到链接，检查是否是直接的文件URL
            if (window.location.href.includes('.mp4') || window.location.href.includes('.pdf')) {
              console.log('检测到直接文件URL');
              return window.location.href;
            }

            return null;
          `).then((result: any) => {
            if (result) {
              console.log(`🔗 找到下载链接: ${result}`);
            } else {
              console.log(`⚠️ 页面中没有找到下载链接`);
              // 如果5秒内没有触发下载，就认为失败
              setTimeout(() => {
                console.log(`⏰ 浏览器下载超时（无下载触发）`);
                downloadWindow.close();
                resolve(false);
              }, 5000);
            }
          }).catch((error: any) => {
            console.log(`❌ 页面脚本执行失败: ${error.message}`);
            downloadWindow.close();
            resolve(false);
          });
        });

        // 监听页面加载失败
        downloadWindow.webContents.on('did-fail-load', (event: any, errorCode: any, errorDescription: any) => {
          console.log(`❌ 页面加载失败: ${errorCode} - ${errorDescription}`);
          downloadWindow.close();
          resolve(false);
        });

        // 设置总超时
        setTimeout(() => {
          console.log(`⏰ 浏览器下载总超时`);
          downloadWindow.close();
          resolve(false);
        }, 30000); // 30秒总超时

        // 导航到下载URL
        console.log(`🌐 浏览器导航到: ${url}`);
        downloadWindow.loadURL(url).catch((error: any) => {
          console.log(`❌ 浏览器导航失败: ${error.message}`);
          downloadWindow.close();
          resolve(false);
        });

      } catch (error: any) {
        console.log(`❌ 浏览器下载异常: ${error?.message || error}`);
        resolve(false);
      }
    });
  }

  /**
   * 尝试下载单个URL
   */
  private async tryDownloadUrl(url: string, filePath: string, taskId: string): Promise<boolean> {
    return new Promise(async (resolve) => {
      try {
        const https = require('https');
        const http = require('http');
        const fs = require('fs');

        const client = url.startsWith('https') ? https : http;
        const requestOptions = await this.buildRequestOptions();

        const request = client.get(url, requestOptions, (response: any) => {
          console.log(`📊 ${url} 响应状态: ${response.statusCode}`);

          if (response.statusCode === 200) {
            // 成功，开始下载
            const file = fs.createWriteStream(filePath);
            response.pipe(file);

            file.on('finish', () => {
              file.close();
              console.log(`✅ 文件下载完成: ${filePath}`);
              resolve(true);
            });

            file.on('error', (err: any) => {
              fs.unlink(filePath, () => {});
              console.error(`❌ 文件写入错误: ${err.message}`);
              resolve(false);
            });
          } else if (response.statusCode === 301 || response.statusCode === 302) {
            // 重定向
            const redirectUrl = response.headers.location;
            if (redirectUrl) {
              console.log(`🔄 重定向到: ${redirectUrl}`);
              // 递归尝试重定向的URL
              this.tryDownloadUrl(redirectUrl, filePath, taskId).then(resolve);
            } else {
              resolve(false);
            }
          } else {
            // 其他状态码，失败
            console.log(`❌ HTTP错误 ${response.statusCode}: ${url}`);
            resolve(false);
          }
        });

        request.on('error', (error: any) => {
          console.log(`❌ 请求错误: ${error.message}`);
          resolve(false);
        });

        request.setTimeout(30000, () => {
          request.destroy();
          console.log(`❌ 请求超时: ${url}`);
          resolve(false);
        });

      } catch (error: any) {
        console.log(`❌ 下载异常: ${error?.message || error}`);
        resolve(false);
      }
    });
  }

  /**
   * 执行单次API请求
   */
  private async makeApiRequest(url: string, requestOptions: any): Promise<any> {
    return new Promise((resolve, reject) => {
      const https = require('https');
      const http = require('http');

      const client = url.startsWith('https') ? https : http;

      const request = client.get(url, requestOptions, (response: any) => {
        let data = '';

        response.on('data', (chunk: any) => {
          data += chunk;
        });

        response.on('end', () => {
          try {
            if (response.statusCode === 200) {
              const jsonData = JSON.parse(data);
              resolve(jsonData);
            } else {
              console.error(`API请求失败，状态码: ${response.statusCode}`);
              reject(new Error(`API请求失败: ${response.statusCode}`));
            }
          } catch (parseError) {
            console.error(`JSON解析失败: ${(parseError as any)?.message || parseError}`);
            reject(parseError);
          }
        });
      });

      request.on('error', (error: any) => {
        reject(error);
      });

      request.setTimeout(10000, () => {
        request.destroy();
        reject(new Error('API请求超时'));
      });
    });
  }

  /**
   * 下载M3U8视频
   */
  private async downloadM3U8Video(
    url: string,
    config: any,
    taskId: string,
    onProgress?: (progress: any) => void
  ): Promise<string> {
    try {
      console.log(`🎥 开始M3U8视频下载: ${taskId}`);

      // 这里可以集成ffmpeg或其他M3U8下载工具
      // 目前先实现基础的M3U8列表文件下载
      const m3u8Content = await this.downloadM3U8Playlist(url);

      if (m3u8Content) {
        // 保存M3U8播放列表文件
        const outputDir = config.downloadPath || app.getPath('downloads');
        const fileName = `video_${taskId}.m3u8`;
        const filePath = path.join(outputDir, fileName);

        await fs.writeFile(filePath, m3u8Content);
        console.log(`✅ M3U8播放列表已保存: ${filePath}`);

        // 通知用户可以使用其他工具下载
        console.log(`💡 提示: 可以使用ffmpeg等工具通过此M3U8文件下载完整视频`);

        return taskId;
      } else {
        throw new Error('无法获取M3U8播放列表内容');
      }
    } catch (error) {
      console.error(`❌ M3U8视频下载失败: ${error}`);
      throw error;
    }
  }

  /**
   * 下载M3U8播放列表
   */
  private async downloadM3U8Playlist(url: string): Promise<string | null> {
    try {
      const headers = this.buildEnhancedHeaders(url);
      const response = await axios.get(url, {
        headers,
        timeout: 30000
      });

      if (response.data && typeof response.data === 'string') {
        return response.data;
      }

      return null;
    } catch (error) {
      console.error(`❌ 下载M3U8播放列表失败: ${error}`);
      return null;
    }
  }

  /**
   * 下载SuperBoard课件
   */
  private async downloadSuperBoardFile(
    url: string,
    config: any,
    taskId: string,
    onProgress?: (progress: any) => void
  ): Promise<string> {
    try {
      console.log(`📊 开始SuperBoard课件下载: ${taskId}`);

      const outputDir = config.downloadPath || app.getPath('downloads');
      const fileName = `superboard_${taskId}.pptx`;
      const filePath = path.join(outputDir, fileName);

      // 尝试直接下载
      const success = await this.tryDownloadUrl(url, filePath, taskId);

      if (success) {
        console.log(`✅ SuperBoard课件下载完成: ${filePath}`);
        return taskId;
      } else {
        throw new Error('SuperBoard课件下载失败');
      }
    } catch (error) {
      console.error(`❌ SuperBoard课件下载失败: ${error}`);
      throw error;
    }
  }

  /**
   * 构建增强的请求头
   */
  private buildEnhancedHeaders(url: string): Record<string, string> {
    return {
      'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Accept': '*/*',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache',
      'Referer': 'https://basic.smartedu.cn/',
      'Origin': 'https://basic.smartedu.cn',
      'X-Nd-Auth': this.generateXNdAuth(url),
      'X-Nd-Checksum': this.generateXNdChecksum(url)
    };
  }

  /**
   * 生成X-Nd-Auth认证头
   */
  private generateXNdAuth(url: string, timestamp?: number): string {
    try {
      const ts = timestamp || Date.now();
      const urlPath = new URL(url).pathname;

      // 基于URL路径和时间戳生成认证字符串
      const baseString = `${urlPath}${ts}`;
      const hash = crypto.createHash('md5').update(baseString).digest('hex');

      // 构造认证头格式
      return `MAC id="anonymous",ts="${ts}",nonce="${hash.substring(0, 8)}",mac="${hash}"`;
    } catch (error) {
      console.warn('生成X-Nd-Auth失败，使用默认值:', error);
      return 'MAC id="anonymous",ts="' + Date.now() + '",nonce="12345678",mac="default"';
    }
  }

  /**
   * 生成X-Nd-Checksum校验头
   */
  private generateXNdChecksum(url: string, timestamp?: number): string {
    try {
      const ts = timestamp || Date.now();
      const urlPath = new URL(url).pathname;

      // 生成校验和
      const checksumString = `${urlPath}${ts}`;
      return crypto.createHash('sha256').update(checksumString).digest('hex').substring(0, 16);
    } catch (error) {
      console.warn('生成X-Nd-Checksum失败，使用默认值:', error);
      return 'default_checksum';
    }
  }

  /**
   * 设置键盘快捷键
   */
  private setupKeyboardShortcuts(): void {
    if (!this.mainWindow) return;

    // 监听键盘事件
    this.mainWindow.webContents.on('before-input-event', (event, input) => {
      // F12 或 Ctrl+Shift+I 切换 DevTools
      if (input.key === 'F12' ||
          (input.control && input.shift && input.key.toLowerCase() === 'i')) {
        if (process.env.NODE_ENV === 'development') {
          this.devToolsManager?.toggleDevTools();
        }
      }
    });
  }

  private setupIpcHandlers(): void {
    // App info handlers
    ipcMain.handle('app:getVersion', () => {
      return app.getVersion();
    });

    ipcMain.handle('app:getName', () => {
      return app.getName();
    });

    // Download related handlers
    ipcMain.handle('download:getDefaultPath', () => {
      return app.getPath('downloads');
    });

    ipcMain.handle('download:selectPath', async () => {
      const result = await dialog.showOpenDialog(this.mainWindow!, {
        properties: ['openDirectory'],
        title: '选择下载目录'
      });

      if (result.canceled) {
        return null;
      }

      return result.filePaths[0];
    });

    ipcMain.handle('download:openFolder', async (_, folderPath: string) => {
      try {
        await shell.openPath(folderPath);
        return true;
      } catch (error) {
        console.error('Failed to open folder:', error);
        return false;
      }
    });

    // File system handlers
    ipcMain.handle('fs:checkExists', async (_, filePath: string) => {
      try {
        return await fs.pathExists(filePath);
      } catch (error) {
        return false;
      }
    });

    ipcMain.handle('fs:getStats', async (_, filePath: string) => {
      try {
        const stats = await fs.stat(filePath);
        return {
          size: stats.size,
          isFile: stats.isFile(),
          isDirectory: stats.isDirectory(),
          mtime: stats.mtime,
          ctime: stats.ctime
        };
      } catch (error) {
        return null;
      }
    });

    // Notification handlers
    ipcMain.handle('notification:show', (_, title: string, body: string) => {
      if (Notification.isSupported()) {
        new Notification({
          title,
          body,
          icon: path.join(__dirname, '../assets/icon.png') // 如果有图标的话
        }).show();
        return true;
      }
      return false;
    });

    // Auto updater handlers
    ipcMain.handle('updater:checkForUpdates', () => {
      this.autoUpdater?.checkForUpdatesManually();
    });

    ipcMain.handle('updater:quitAndInstall', () => {
      this.autoUpdater?.quitAndInstall();
    });

    // DevTools handlers
    ipcMain.handle('devtools:toggle', () => {
      this.devToolsManager?.toggleDevTools();
    });

    ipcMain.handle('devtools:isOpened', () => {
      return this.devToolsManager?.isDevToolsOpened() || false;
    });

    // 增强的下载处理器
    ipcMain.handle('download:start', async (event, url: string, config: any) => {
      const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      console.log(`开始下载任务: ${taskId}, URL: ${url}`);

      // 验证URL
      if (!url || url.trim() === '') {
        console.error(`❌ 下载任务 ${taskId} 失败: URL为空`);
        return { error: 'URL为空', taskId };
      }

      try {
        // 验证URL格式
        const urlObj = new URL(url);
        console.log(`📥 下载URL验证通过: ${urlObj.hostname}`);

        // 检测资源类型
        const isM3U8 = url.includes('.m3u8') || url.includes('m3u8');
        const isSuperBoard = url.includes('.superboard') || url.includes('superboard');

        // 定义进度回调函数
        const onProgress = (progress: any) => {
          // 可以通过IPC发送进度更新到渲染进程
          console.log(`📊 下载进度: ${progress.progress || 0}%`);
        };

        if (isM3U8) {
          console.log(`🎥 检测到M3U8视频流: ${url}`);
          return await this.downloadM3U8Video(url, config, taskId, onProgress);
        } else if (isSuperBoard) {
          console.log(`📊 检测到SuperBoard课件: ${url}`);
          return await this.downloadSuperBoardFile(url, config, taskId, onProgress);
        } else {
          console.log(`📄 检测到普通文件: ${url}`);

          // 实现智慧教育平台资源下载逻辑
          try {
            const https = require('https');
            const http = require('http');
            const fs = require('fs');
            const path = require('path');

            // 确保下载目录存在
            const downloadDir = config.directory || path.join(require('os').homedir(), 'Downloads', '智慧教育资源');
            if (!fs.existsSync(downloadDir)) {
              fs.mkdirSync(downloadDir, { recursive: true });
            }

            // 生成文件名
            const fileName = config.filename || `resource_${taskId}.html`;
            const filePath = path.join(downloadDir, fileName);

            console.log(`📥 开始下载到: ${filePath}`);

            // 智慧教育平台资源下载 - 尝试多种URL格式
            console.log(`🎯 智慧教育平台资源下载: ${url}`);

            // 从URL中提取资源ID，尝试不同的下载路径
            const resourceIdMatch = url.match(/\/([a-f0-9-]{36})\/?$/);
            if (resourceIdMatch) {
              const resourceId = resourceIdMatch[1];
              console.log(`🔍 提取到资源ID: ${resourceId}`);

              // 尝试多种可能的下载URL
              const possibleUrls = [
                url, // 原始URL
                `${url}index.m3u8`, // M3U8视频流
                `${url}video.mp4`, // 直接MP4文件
                `${url}document.pdf`, // PDF文档
                `${url}courseware.pptx`, // 课件文件
                // 尝试不同的域名和路径
                url.replace('r2-ndr.ykt.cbern.com.cn', 'r3-ndr.ykt.cbern.com.cn'),
                url.replace('r2-ndr.ykt.cbern.com.cn', 'r1-ndr.ykt.cbern.com.cn'),
                url.replace('/video/', '/assets/'),
                url.replace('/video/', '/document/'),
              ];

              console.log(`🔄 将尝试 ${possibleUrls.length} 个可能的URL`);

              // 先尝试使用浏览器会话下载
              console.log(`🌐 尝试使用浏览器会话下载`);
              try {
                const browserSuccess = await this.downloadWithBrowser(url, filePath, taskId);
                if (browserSuccess) {
                  console.log(`✅ 浏览器下载成功: ${url}`);
                  return taskId;
                }
              } catch (browserError: any) {
                console.log(`❌ 浏览器下载失败: ${browserError?.message || browserError}`);
              }

              // 如果浏览器下载失败，尝试直接HTTP下载
              for (let i = 0; i < possibleUrls.length; i++) {
                const tryUrl = possibleUrls[i];
                console.log(`🔄 尝试URL ${i + 1}/${possibleUrls.length}: ${tryUrl}`);

                try {
                  const success = await this.tryDownloadUrl(tryUrl, filePath, taskId);
                  if (success) {
                    console.log(`✅ 成功下载: ${tryUrl}`);
                    return taskId;
                  }
                } catch (error) {
                  console.log(`❌ URL ${i + 1} 失败: ${(error as any)?.message || error}`);
                }
              }

              console.log(`❌ 所有自动下载方法都失败`);
              console.log(`💡 建议：在浏览器中手动下载此资源`);

              // 在默认浏览器中打开URL，让用户手动下载
              const { shell } = require('electron');
              shell.openExternal(url).then(() => {
                console.log(`🌐 已在浏览器中打开: ${url}`);
              }).catch((error: any) => {
                console.log(`❌ 打开浏览器失败: ${error.message}`);
              });

              return {
                error: '自动下载失败，已在浏览器中打开，请手动下载',
                taskId,
                browserOpened: true,
                url: url
              };
            }

            // 如果不是智慧教育平台URL，使用原始下载逻辑
            // 选择http或https
            const client = url.startsWith('https') ? https : http;

            // 开始下载
            const file = fs.createWriteStream(filePath);

            // 构建请求选项
            const requestOptions = await this.buildRequestOptions();

            const request = client.get(url, requestOptions, (response: any) => {
              console.log(`📊 响应状态: ${response.statusCode}`);
              console.log(`📊 响应头:`, response.headers);

              // 处理重定向
              if (response.statusCode === 301 || response.statusCode === 302 || response.statusCode === 307 || response.statusCode === 308) {
                const redirectUrl = response.headers.location;
                if (redirectUrl) {
                  console.log(`🔄 重定向到: ${redirectUrl}`);
                  file.close();
                  fs.unlink(filePath, () => {}); // 删除空文件

                  // 递归调用下载重定向的URL
                  const redirectClient = redirectUrl.startsWith('https') ? https : http;
                  const redirectRequest = redirectClient.get(redirectUrl, requestOptions, (redirectResponse: any) => {
                    console.log(`📊 重定向响应状态: ${redirectResponse.statusCode}`);

                    if (redirectResponse.statusCode === 200) {
                      const newFile = fs.createWriteStream(filePath);
                      redirectResponse.pipe(newFile);

                      newFile.on('finish', () => {
                        newFile.close();
                        console.log(`✅ 下载完成: ${filePath}`);
                      });

                      newFile.on('error', (err: any) => {
                        fs.unlink(filePath, () => {});
                        console.error(`❌ 文件写入错误: ${err.message}`);
                      });
                    } else {
                      console.error(`❌ 重定向后HTTP错误: ${redirectResponse.statusCode}`);
                    }
                  });

                  redirectRequest.on('error', (err: any) => {
                    console.error(`❌ 重定向请求错误: ${err.message}`);
                  });

                  return;
                }
              }

              if (response.statusCode === 200) {
                response.pipe(file);

                file.on('finish', () => {
                  file.close();
                  console.log(`✅ 下载完成: ${filePath}`);
                });

                file.on('error', (err: any) => {
                  fs.unlink(filePath, () => {}); // 删除不完整的文件
                  console.error(`❌ 文件写入错误: ${err.message}`);
                });
              } else if (response.statusCode === 403) {
                file.close();
                fs.unlink(filePath, () => {}); // 删除空文件
                console.error(`❌ 访问被拒绝 (403): 可能需要登录认证或特殊权限`);
                console.log(`🔍 尝试的URL: ${url}`);
                console.log(`💡 建议: 这个资源可能需要在浏览器中登录后才能下载`);
              } else {
                file.close();
                fs.unlink(filePath, () => {}); // 删除空文件
                console.error(`❌ HTTP错误: ${response.statusCode}`);
              }
            });

            request.on('error', (err: any) => {
              console.error(`❌ 请求错误: ${err.message}`);
            });

            request.setTimeout(30000, () => {
              request.destroy();
              console.error(`❌ 请求超时`);
            });

            console.log(`🚀 下载任务已启动，任务ID: ${taskId}`);
            return taskId;

          } catch (downloadError) {
            console.error(`❌ 下载失败: ${downloadError}`);
            return { error: '下载失败', taskId };
          }
        }

      } catch (urlError) {
        console.error(`❌ 下载任务 ${taskId} 失败: URL格式无效`, urlError);
        return { error: 'URL格式无效', taskId, url };
      }
    });

    ipcMain.handle('download:pause', async (event, taskId: string) => {
      console.log(`暂停下载任务: ${taskId}`);
      return true;
    });

    ipcMain.handle('download:resume', async (event, taskId: string) => {
      console.log(`恢复下载任务: ${taskId}`);
      return true;
    });

    ipcMain.handle('download:cancel', async (event, taskId: string) => {
      console.log(`取消下载任务: ${taskId}`);
      return true;
    });

    ipcMain.handle('download:getAllTasks', async () => {
      // 返回空数组，后续可以实现真正的任务管理
      return [];
    });

    // 登录智慧教育平台
    ipcMain.handle('auth:login', async (event) => {
      try {
        const { BrowserWindow } = require('electron');

        // 创建登录窗口
        const loginWindow = new BrowserWindow({
          width: 1200,
          height: 800,
          webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            webSecurity: true
          },
          parent: this.mainWindow,
          modal: true,
          show: false
        });

        // 加载智慧教育登录页面
        await loginWindow.loadURL('https://basic.smartedu.cn/');
        loginWindow.show();

        // 监听页面导航，检测登录成功
        loginWindow.webContents.on('did-navigate', (event: any, navigationUrl: any) => {
          console.log(`🔍 导航到: ${navigationUrl}`);

          // 检测是否登录成功（通常会重定向到主页或个人中心）
          if (navigationUrl.includes('basic.smartedu.cn') && !navigationUrl.includes('login')) {
            console.log(`✅ 检测到登录成功`);

            // 延迟关闭窗口，让用户看到登录成功
            setTimeout(() => {
              if (!loginWindow.isDestroyed()) {
                loginWindow.close();
              }
            }, 2000);
          }
        });

        // 窗口关闭时返回结果
        return new Promise((resolve) => {
          loginWindow.on('closed', () => {
            resolve({ success: true, message: '登录窗口已关闭' });
          });
        });

      } catch (error) {
        console.error('登录失败:', error);
        return { success: false, error: (error as any)?.message || error };
      }
    });

    // API代理处理器 - 解决CORS问题，使用增强的认证逻辑
    ipcMain.handle('api:request', async (_, url: string, options: any = {}) => {
      try {
        console.log(`API代理请求: ${url}`);

        // 使用多种认证方法尝试请求
        const authMethods = [
          // 方法1：完整认证头（包含X-Nd-Auth）
          {
            method: options.method || 'GET',
            headers: {
              'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
              'Accept': 'application/json, text/plain, */*',
              'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache',
              'Referer': 'https://basic.smartedu.cn/',
              'Origin': 'https://basic.smartedu.cn',
              'X-Nd-Auth': this.generateXNdAuth(url),
              'X-Nd-Checksum': this.generateXNdChecksum(url),
              ...options.headers
            },
            timeout: 30000,
            ...options
          },
          // 方法2：简化认证头
          {
            method: options.method || 'GET',
            headers: {
              'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
              'Accept': 'application/json, text/plain, */*',
              'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'Referer': 'https://basic.smartedu.cn/',
              ...options.headers
            },
            timeout: 30000,
            ...options
          }
        ];

        // 尝试不同的认证方法
        for (let i = 0; i < authMethods.length; i++) {
          try {
            console.log(`🔄 尝试认证方法 ${i + 1}/${authMethods.length}`);
            const response = await axios({
              url,
              ...authMethods[i]
            });

            console.log(`✅ 认证方法 ${i + 1} 成功`);
            return {
              success: true,
              data: response.data,
              status: response.status,
              headers: response.headers
            };
          } catch (methodError: any) {
            console.log(`❌ 认证方法 ${i + 1} 失败: ${methodError.message}`);
            if (i === authMethods.length - 1) {
              throw methodError; // 所有方法都失败了，抛出最后一个错误
            }
          }
        }

      } catch (error: any) {
        console.error(`API代理请求失败 ${url}:`, error.message);

        return {
          success: false,
          error: {
            message: error.message,
            status: error.response?.status,
            data: error.response?.data
          }
        };
      }
    });
  }
}

// Initialize the application
new SmartEduDownloaderApp();
