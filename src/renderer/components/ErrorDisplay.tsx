import React from 'react';
import { <PERSON><PERSON>, Button, Collapse, Typography, Space, Tag } from 'antd';
import {
  ExclamationCircleOutlined,
  ReloadOutlined,
  CloseCircleOutlined,
  WarningOutlined
} from '@ant-design/icons';
import { SmartEduError, ErrorType } from '../../shared/types';
import { RetryState } from '../../shared/services/RetryManager';

const { Text, Paragraph } = Typography;
const { Panel } = Collapse;

/**
 * 错误显示组件属性
 */
export interface ErrorDisplayProps {
  error: Error | SmartEduError;
  retryState?: RetryState;
  onRetry?: () => void;
  onDismiss?: () => void;
  showDetails?: boolean;
  className?: string;
}

/**
 * 错误严重程度
 */
type ErrorSeverity = 'error' | 'warning' | 'info';

/**
 * 错误显示组件
 * 用于展示错误信息和提供用户操作选项
 */
export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  retryState,
  onRetry,
  onDismiss,
  showDetails = false,
  className
}) => {
  // 获取错误类型和严重程度
  const getErrorInfo = (error: Error | SmartEduError): {
    type: ErrorType | 'unknown';
    severity: ErrorSeverity;
    icon: React.ReactNode;
    color: string;
  } => {
    if (error instanceof SmartEduError) {
      const severity: ErrorSeverity = error.isRetryable ? 'warning' : 'error';
      
      switch (error.type) {
        case ErrorType.NETWORK_ERROR:
          return {
            type: error.type,
            severity,
            icon: <ExclamationCircleOutlined />,
            color: 'orange'
          };
        case ErrorType.API_ERROR:
          return {
            type: error.type,
            severity,
            icon: <WarningOutlined />,
            color: 'red'
          };
        case ErrorType.FILE_ERROR:
          return {
            type: error.type,
            severity: 'error',
            icon: <CloseCircleOutlined />,
            color: 'red'
          };
        case ErrorType.DOWNLOAD_ERROR:
          return {
            type: error.type,
            severity,
            icon: <ExclamationCircleOutlined />,
            color: 'orange'
          };
        default:
          return {
            type: error.type,
            severity: 'error',
            icon: <CloseCircleOutlined />,
            color: 'red'
          };
      }
    }

    return {
      type: 'unknown',
      severity: 'error',
      icon: <CloseCircleOutlined />,
      color: 'red'
    };
  };

  // 获取用户友好的错误消息
  const getUserFriendlyMessage = (error: Error | SmartEduError): string => {
    if (error instanceof SmartEduError) {
      switch (error.type) {
        case ErrorType.NETWORK_ERROR:
          return '网络连接出现问题，请检查网络设置后重试';
        case ErrorType.API_ERROR:
          return '服务器响应异常，请稍后重试';
        case ErrorType.FILE_ERROR:
          return '文件操作失败，请检查磁盘空间和权限';
        case ErrorType.DOWNLOAD_ERROR:
          return '下载过程中出现错误，可以尝试重新下载';
        case ErrorType.AUTH_ERROR:
          return '身份验证失败，请重新登录';
        case ErrorType.M3U8_ERROR:
          return '视频解析失败，请检查视频链接';
        case ErrorType.FFMPEG_ERROR:
          return '视频处理失败，请检查FFmpeg配置';
        case ErrorType.DISK_SPACE_ERROR:
          return '磁盘空间不足，请清理磁盘后重试';
        default:
          return error.message || '发生未知错误';
      }
    }

    return error.message || '发生未知错误';
  };

  // 获取解决建议
  const getSuggestions = (error: Error | SmartEduError): string[] => {
    if (error instanceof SmartEduError) {
      switch (error.type) {
        case ErrorType.NETWORK_ERROR:
          return [
            '检查网络连接是否正常',
            '尝试切换网络环境',
            '检查防火墙设置',
            '稍后重试'
          ];
        case ErrorType.API_ERROR:
          return [
            '检查服务器状态',
            '稍后重试',
            '联系技术支持'
          ];
        case ErrorType.FILE_ERROR:
          return [
            '检查磁盘空间是否充足',
            '检查文件权限',
            '尝试更换存储位置'
          ];
        case ErrorType.DOWNLOAD_ERROR:
          return [
            '检查网络连接',
            '重新开始下载',
            '尝试单个文件下载'
          ];
        case ErrorType.AUTH_ERROR:
          return [
            '重新登录账户',
            '检查账户状态',
            '清除浏览器缓存'
          ];
        case ErrorType.M3U8_ERROR:
          return [
            '检查视频链接有效性',
            '尝试重新获取视频信息',
            '联系技术支持'
          ];
        case ErrorType.FFMPEG_ERROR:
          return [
            '检查FFmpeg是否正确安装',
            '检查视频文件完整性',
            '尝试重新下载视频'
          ];
        case ErrorType.DISK_SPACE_ERROR:
          return [
            '清理磁盘空间',
            '更换存储位置',
            '删除不必要的文件'
          ];
        default:
          return ['请联系技术支持'];
      }
    }

    return ['请联系技术支持'];
  };

  const errorInfo = getErrorInfo(error);
  const userMessage = getUserFriendlyMessage(error);
  const suggestions = getSuggestions(error);

  // 渲染重试信息
  const renderRetryInfo = () => {
    if (!retryState) return null;

    return (
      <div style={{ marginTop: 12 }}>
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          <div>
            <Text type="secondary">重试次数: </Text>
            <Tag color="blue">{retryState.attempt}</Tag>
          </div>
          
          {retryState.nextRetryAt && (
            <div>
              <Text type="secondary">下次重试: </Text>
              <Text code>{retryState.nextRetryAt.toLocaleTimeString()}</Text>
            </div>
          )}
          
          {retryState.totalDelay > 0 && (
            <div>
              <Text type="secondary">总等待时间: </Text>
              <Text code>{Math.round(retryState.totalDelay / 1000)}秒</Text>
            </div>
          )}
        </Space>
      </div>
    );
  };

  // 渲染错误详情
  const renderErrorDetails = () => {
    if (!showDetails) return null;

    return (
      <Collapse ghost>
        <Panel header="错误详情" key="details">
          <Space direction="vertical" size="small" style={{ width: '100%' }}>
            <div>
              <Text strong>错误类型: </Text>
              <Tag color={errorInfo.color}>{errorInfo.type}</Tag>
            </div>
            
            <div>
              <Text strong>原始消息: </Text>
              <Paragraph code copyable style={{ margin: 0 }}>
                {error.message}
              </Paragraph>
            </div>
            
            {error instanceof SmartEduError && error.details && Object.keys(error.details).length > 0 && (
              <div>
                <Text strong>详细信息: </Text>
                <Paragraph code style={{ margin: 0 }}>
                  {JSON.stringify(error.details, null, 2)}
                </Paragraph>
              </div>
            )}
            
            {error.stack && (
              <div>
                <Text strong>堆栈跟踪: </Text>
                <Paragraph code style={{ margin: 0, fontSize: '12px' }}>
                  {error.stack}
                </Paragraph>
              </div>
            )}
            
            {retryState && retryState.errorHistory.length > 0 && (
              <div>
                <Text strong>错误历史: </Text>
                <div style={{ maxHeight: 200, overflow: 'auto' }}>
                  {retryState.errorHistory.map((record, index) => (
                    <div key={index} style={{ marginBottom: 8 }}>
                      <Text type="secondary">
                        第{record.attempt}次 - {record.timestamp.toLocaleString()}
                      </Text>
                      <br />
                      <Text code style={{ fontSize: '12px' }}>
                        {record.error.message}
                      </Text>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </Space>
        </Panel>
      </Collapse>
    );
  };

  // 渲染解决建议
  const renderSuggestions = () => {
    if (suggestions.length === 0) return null;

    return (
      <div style={{ marginTop: 12 }}>
        <Text strong>解决建议:</Text>
        <ul style={{ marginTop: 8, marginBottom: 0 }}>
          {suggestions.map((suggestion, index) => (
            <li key={index}>
              <Text>{suggestion}</Text>
            </li>
          ))}
        </ul>
      </div>
    );
  };

  // 渲染操作按钮
  const renderActions = () => {
    const actions = [];

    if (onRetry && (error instanceof SmartEduError ? error.isRetryable : true)) {
      actions.push(
        <Button
          key="retry"
          type="primary"
          icon={<ReloadOutlined />}
          onClick={onRetry}
          size="small"
        >
          重试
        </Button>
      );
    }

    if (onDismiss) {
      actions.push(
        <Button
          key="dismiss"
          onClick={onDismiss}
          size="small"
        >
          忽略
        </Button>
      );
    }

    return actions.length > 0 ? (
      <div style={{ marginTop: 12 }}>
        <Space>{actions}</Space>
      </div>
    ) : null;
  };

  return (
    <div className={className}>
      <Alert
        message={userMessage}
        type={errorInfo.severity}
        icon={errorInfo.icon}
        showIcon
        description={
          <div>
            {renderRetryInfo()}
            {renderSuggestions()}
            {renderActions()}
            {renderErrorDetails()}
          </div>
        }
        closable={!!onDismiss}
        onClose={onDismiss}
      />
    </div>
  );
};

export default ErrorDisplay;