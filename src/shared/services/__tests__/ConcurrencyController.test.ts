import { ConcurrencyController, ConcurrencyConfig } from '../ConcurrencyController';

describe('ConcurrencyController', () => {
  let controller: ConcurrencyController;
  let config: Partial<ConcurrencyConfig>;

  beforeEach(() => {
    config = {
      maxConcurrent: 2,
      enableDynamicAdjustment: false, // 禁用动态调整以便测试
      resourceThresholds: {
        maxMemoryUsage: 1024,
        maxCpuUsage: 80,
        maxNetworkBandwidth: 50,
        maxDiskIO: 100
      },
      adaptiveStrategy: 'balanced'
    };
    controller = new ConcurrencyController(config);
  });

  afterEach(() => {
    controller.destroy();
  });

  describe('slot management', () => {
    it('should acquire available slot', async () => {
      const taskId = 'task1';
      const slotId = await controller.acquireSlot(taskId);
      
      expect(slotId).toBeTruthy();
      expect(controller.getSlotForTask(taskId)).toBe(slotId);
      expect(controller.getActiveTaskIds()).toContain(taskId);
    });

    it('should return null when no slots available', async () => {
      // 占满所有槽位
      await controller.acquireSlot('task1');
      await controller.acquireSlot('task2');
      
      // 第三个任务应该无法获取槽位
      const slotId = await controller.acquireSlot('task3');
      
      expect(slotId).toBeNull();
      expect(controller.getWaitingTasks()).toContain('task3');
    });

    it('should release slot and process waiting queue', async () => {
      // 占满所有槽位
      const slot1 = await controller.acquireSlot('task1');
      const slot2 = await controller.acquireSlot('task2');
      
      // 第三个任务进入等待队列
      await controller.acquireSlot('task3');
      expect(controller.getWaitingTasks()).toContain('task3');
      
      // 释放一个槽位
      const released = controller.releaseSlot(slot1!);
      
      expect(released).toBe(true);
      expect(controller.getActiveTaskIds()).not.toContain('task1');
      
      // 等待队列中的任务应该被处理
      setTimeout(() => {
        expect(controller.getActiveTaskIds()).toContain('task3');
        expect(controller.getWaitingTasks()).not.toContain('task3');
      }, 10);
    });

    it('should update slot activity', async () => {
      const taskId = 'task1';
      const slotId = await controller.acquireSlot(taskId);
      
      // 更新活动时间不应该抛出错误
      expect(() => {
        controller.updateSlotActivity(slotId!);
      }).not.toThrow();
    });

    it('should emit slot events', (done) => {
      const taskId = 'task1';
      let eventCount = 0;

      controller.on('slot-acquired', (slotId, id) => {
        expect(id).toBe(taskId);
        eventCount++;
        if (eventCount === 2) done();
      });

      controller.on('slot-released', (slotId, id) => {
        expect(id).toBe(taskId);
        eventCount++;
        if (eventCount === 2) done();
      });

      controller.acquireSlot(taskId).then((slotId) => {
        controller.releaseSlot(slotId!);
      });
    });
  });

  describe('waiting queue management', () => {
    it('should manage waiting queue', async () => {
      // 占满所有槽位
      await controller.acquireSlot('task1');
      await controller.acquireSlot('task2');
      
      // 添加等待任务
      await controller.acquireSlot('task3');
      await controller.acquireSlot('task4');
      
      expect(controller.getWaitingQueueLength()).toBe(2);
      expect(controller.getWaitingTasks()).toEqual(['task3', 'task4']);
    });

    it('should remove task from waiting queue', async () => {
      // 占满所有槽位
      await controller.acquireSlot('task1');
      await controller.acquireSlot('task2');
      
      // 添加等待任务
      await controller.acquireSlot('task3');
      
      const removed = controller.removeFromWaitingQueue('task3');
      
      expect(removed).toBe(true);
      expect(controller.getWaitingTasks()).not.toContain('task3');
    });

    it('should return false when removing non-existent task from queue', () => {
      const removed = controller.removeFromWaitingQueue('nonexistent');
      expect(removed).toBe(false);
    });
  });

  describe('concurrency adjustment', () => {
    it('should increase concurrency and add slots', async () => {
      const oldLimit = controller.getConfig().maxConcurrent;
      
      controller.adjustConcurrency(4, '测试增加');
      
      const newConfig = controller.getConfig();
      expect(newConfig.maxConcurrent).toBe(4);
      
      // 应该能够获取更多槽位
      const slots = [];
      for (let i = 0; i < 4; i++) {
        const slotId = await controller.acquireSlot(`task${i + 1}`);
        if (slotId) slots.push(slotId);
      }
      
      expect(slots).toHaveLength(4);
    });

    it('should decrease concurrency', () => {
      controller.adjustConcurrency(1, '测试减少');
      
      const newConfig = controller.getConfig();
      expect(newConfig.maxConcurrent).toBe(1);
    });

    it('should emit concurrency-adjusted event', (done) => {
      controller.on('concurrency-adjusted', (oldLimit, newLimit, reason) => {
        expect(oldLimit).toBe(2);
        expect(newLimit).toBe(3);
        expect(reason).toBe('测试调整');
        done();
      });
      
      controller.adjustConcurrency(3, '测试调整');
    });

    it('should not adjust if new limit equals current limit', () => {
      const spy = jest.fn();
      controller.on('concurrency-adjusted', spy);
      
      controller.adjustConcurrency(2, '相同限制');
      
      expect(spy).not.toHaveBeenCalled();
    });
  });

  describe('statistics', () => {
    it('should provide accurate statistics', async () => {
      // 获取一些槽位
      await controller.acquireSlot('task1');
      const slot2 = await controller.acquireSlot('task2');
      
      // 添加等待任务
      await controller.acquireSlot('task3');
      
      // 完成一个任务
      controller.releaseSlot(slot2!);
      
      const stats = controller.getStats();
      
      expect(stats.totalSlots).toBe(2);
      expect(stats.activeSlots).toBe(1);
      expect(stats.availableSlots).toBe(1);
      expect(stats.utilizationRate).toBe(50); // 1/2 * 100
    });

    it('should calculate average task duration', async () => {
      const taskId = 'task1';
      const slotId = await controller.acquireSlot(taskId);
      
      // 等待一段时间后释放
      setTimeout(() => {
        controller.releaseSlot(slotId!);
        
        const stats = controller.getStats();
        expect(stats.averageTaskDuration).toBeGreaterThan(0);
      }, 100);
    });

    it('should emit stats-updated event', (done) => {
      controller.on('stats-updated', (stats) => {
        expect(stats.totalSlots).toBe(2);
        done();
      });

      controller.acquireSlot('task1');
    });
  });

  describe('configuration', () => {
    it('should update configuration', () => {
      const newConfig: Partial<ConcurrencyConfig> = {
        maxConcurrent: 5,
        adaptiveStrategy: 'aggressive'
      };
      
      controller.updateConfig(newConfig);
      const currentConfig = controller.getConfig();
      
      expect(currentConfig.maxConcurrent).toBe(5);
      expect(currentConfig.adaptiveStrategy).toBe('aggressive');
    });

    it('should adjust concurrency when maxConcurrent changes in config', () => {
      const spy = jest.fn();
      controller.on('concurrency-adjusted', spy);
      
      controller.updateConfig({ maxConcurrent: 4 });
      
      expect(spy).toHaveBeenCalledWith(2, 4, '配置更新');
    });
  });

  describe('utility methods', () => {
    it('should check if slot is available', async () => {
      expect(controller.hasAvailableSlot()).toBe(true);
      
      // 占满所有槽位
      await controller.acquireSlot('task1');
      await controller.acquireSlot('task2');
      
      expect(controller.hasAvailableSlot()).toBe(false);
    });

    it('should get slot for specific task', async () => {
      const taskId = 'task1';
      const slotId = await controller.acquireSlot(taskId);
      
      expect(controller.getSlotForTask(taskId)).toBe(slotId);
      expect(controller.getSlotForTask('nonexistent')).toBeNull();
    });
  });

  describe('resource monitoring', () => {
    it('should enable resource monitoring when configured', () => {
      const monitoringController = new ConcurrencyController({
        ...config,
        enableDynamicAdjustment: true
      });
      
      // 应该启动资源监控
      expect(monitoringController.getConfig().enableDynamicAdjustment).toBe(true);
      
      monitoringController.destroy();
    });

    it('should emit resource threshold exceeded event', (done) => {
      const monitoringController = new ConcurrencyController({
        ...config,
        enableDynamicAdjustment: true,
        resourceThresholds: {
          maxMemoryUsage: 1, // 设置很低的阈值
          maxCpuUsage: 1,
          maxNetworkBandwidth: 1,
          maxDiskIO: 1
        }
      });
      
      monitoringController.on('resource-threshold-exceeded', (resource, current, threshold) => {
        expect(resource).toBe('maxMemoryUsage');
        expect(current).toBeGreaterThan(threshold);
        done();
        monitoringController.destroy();
      });
      
      // 等待资源检查触发
    });
  });

  describe('edge cases', () => {
    it('should handle releasing non-existent slot', () => {
      const released = controller.releaseSlot('nonexistent');
      expect(released).toBe(false);
    });

    it('should handle updating activity for non-existent slot', () => {
      expect(() => {
        controller.updateSlotActivity('nonexistent');
      }).not.toThrow();
    });

    it('should handle destroying controller with active slots', async () => {
      await controller.acquireSlot('task1');
      await controller.acquireSlot('task2');
      
      expect(() => {
        controller.destroy();
      }).not.toThrow();
    });
  });
});
