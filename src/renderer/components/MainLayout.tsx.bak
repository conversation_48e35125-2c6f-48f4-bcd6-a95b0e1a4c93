import React, { useState, useEffect, useCallback } from 'react';
import {
  Layout,
  Menu,
  Typo<PERSON>,
  Space,
  Button,
  Badge,
  Dropdown,
  Avatar,
  Tooltip,
  notification,
  Drawer,
  FloatButton
} from 'antd';
import {
  DownloadOutlined,
  FilterOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  LoginOutlined,
  BookOutlined,
  VideoCameraOutlined,
  DashboardOutlined,
  MonitorOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  BellOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import { FilterPanel } from './FilterPanel';
import { ResourceList } from './ResourceList';
import { DownloadManager } from './DownloadManager';
import { SystemResourceMonitor } from './SystemResourceMonitor';
import { SettingsPanel } from './SettingsPanel';
import { useAuth } from '../hooks/useAuth';
import { useDownload } from '../hooks/useDownload';
import { 
  CourseFilters,
  CourseResource,
  AppConfig
} from '../../shared/types';

const { Header, Sider, Content } = Layout;
const { Title, Text } = Typography;

type ViewType = 'dashboard' | 'resources' | 'downloads' | 'monitor';

interface MainLayoutProps {
  config: AppConfig;
  onConfigChange: (config: AppConfig) => void;
}

/**
 * 主界面布局组件
 */
export const MainLayout: React.FC<MainLayoutProps> = ({
  config,
  onConfigChange
}) => {
  const [currentView, setCurrentView] = useState<ViewType>('dashboard');
  const [collapsed, setCollapsed] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showFilterDrawer, setShowFilterDrawer] = useState(false);
  const [currentFilters, setCurrentFilters] = useState<Partial<CourseFilters>>({});
  const [resources, setResources] = useState<CourseResource[]>([]);
  const [selectedResources, setSelectedResources] = useState<string[]>([]);
  const [searchLoading, setSearchLoading] = useState(false);

  const {
    // authState,
    user,
    isLoggedIn,
    // isGuestMode,
    logout
  } = useAuth();

  const {
    tasks,
    stats,
    pauseTask,
    resumeTask,
    cancelTask,
    retryTask,
    clearTask,
    pauseAll,
    resumeAll,
    cancelAll,
    clearCompleted,
    downloadResource,
    downloadBatch
  } = useDownload();

  // 监听下载任务状态变化，显示通知
  useEffect(() => {
    const completedTasks = tasks.filter(task => task.status === 'completed');
    const failedTasks = tasks.filter(task => task.status === 'failed');
    
    // 这里可以添加通知逻辑
    if (completedTasks.length > 0) {
      // 显示完成通知
    }
    
    if (failedTasks.length > 0) {
      // 显示失败通知
    }
  }, [tasks]);

  /**
   * 处理筛选条件变更
   */
  const handleFilterChange = useCallback(async (filters: CourseFilters) => {
    setCurrentFilters(filters);
    setSearchLoading(true);
    
    try {
      // 模拟搜索资源
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 这里应该调用实际的搜索API
      const mockResources: CourseResource[] = [
        {
          id: '1',
          title: '数学三年级上册第一章',
          type: 'textbook',
          url: 'https://example.com/textbook1',
          metadata: {
            stage: filters.stage,
            grade: filters.grade,
            subject: filters.subject,
            version: filters.version,
            volume: filters.volume,
            chapter: '第一章',
            fileSize: 15 * 1024 * 1024
          },
          requiresAuth: false,
          accessLevel: 'public'
        },
        {
          id: '2',
          title: '数学三年级上册教学视频',
          type: 'video',
          url: 'https://example.com/video1',
          metadata: {
            stage: filters.stage,
            grade: filters.grade,
            subject: filters.subject,
            version: filters.version,
            volume: filters.volume,
            duration: 1800,
            fileSize: 120 * 1024 * 1024
          },
          requiresAuth: true,
          accessLevel: 'registered'
        }
      ];
      
      setResources(mockResources);
      setCurrentView('resources');
      
      notification.success({
        message: '搜索完成',
        description: `找到 ${mockResources.length} 个资源`,
        placement: 'topRight'
      });
    } catch (error) {
      console.error('搜索资源失败:', error);
      notification.error({
        message: '搜索失败',
        description: '搜索资源时发生错误，请重试',
        placement: 'topRight'
      });
    } finally {
      setSearchLoading(false);
      setShowFilterDrawer(false);
    }
  }, []);

  /**
   * 处理资源下载
   */
  const handleDownload = useCallback((resource: CourseResource) => {
    downloadResource(resource);
    notification.info({
      message: '下载已开始',
      description: `正在下载: ${resource.title}`,
      placement: 'topRight'
    });
  }, [downloadResource]);

  /**
   * 处理批量下载
   */
  const handleBatchDownload = useCallback((resources: CourseResource[]) => {
    downloadBatch(resources);
    notification.info({
      message: '批量下载已开始',
      description: `正在下载 ${resources.length} 个资源`,
      placement: 'topRight'
    });
  }, [downloadBatch]);

  /**
   * 用户菜单项
   */
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息',
      onClick: () => {
        // TODO: 打开个人信息页面
        notification.info({ message: '个人信息功能开发中' });
      }
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
      onClick: () => setShowSettings(true)
    },
    {
      type: 'divider' as const
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: logout
    }
  ];

  /**
   * 侧边栏菜单项
   */
  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板'
    },
    {
      key: 'resources',
      icon: <BookOutlined />,
      label: '资源管理',
      children: [
        {
          key: 'textbooks',
          icon: <BookOutlined />,
          label: '电子教材'
        },
        {
          key: 'videos',
          icon: <VideoCameraOutlined />,
          label: '教学视频'
        }
      ]
    },
    {
      key: 'downloads',
      icon: <DownloadOutlined />,
      label: (
        <Space>
          下载管理
          {stats.activeTasks > 0 && (
            <Badge count={stats.activeTasks} size="small" />
          )}
        </Space>
      )
    },
    {
      key: 'monitor',
      icon: <MonitorOutlined />,
      label: '系统监控'
    }
  ];

  /**
   * 渲染页面内容
   */
  const renderContent = () => {
    switch (currentView) {
      case 'dashboard':
        return (
          <div style={{ padding: '24px' }}>
            <Title level={2}>仪表板</Title>
            <div style={{ 
              display: 'grid', 
              gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', 
              gap: '24px',
              marginTop: '24px'
            }}>
              {/* 快速统计卡片 */}
              <div style={{ 
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                padding: '24px',
                borderRadius: '12px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
              }}>
                <Title level={4} style={{ color: 'white', margin: 0 }}>
                  总下载任务
                </Title>
                <div style={{ fontSize: '32px', fontWeight: 'bold', marginTop: '8px' }}>
                  {stats.totalTasks}
                </div>
                <Text style={{ color: 'rgba(255,255,255,0.8)' }}>
                  已完成 {stats.completedTasks} 个
                </Text>
              </div>

              <div style={{ 
                background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                color: 'white',
                padding: '24px',
                borderRadius: '12px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
              }}>
                <Title level={4} style={{ color: 'white', margin: 0 }}>
                  进行中任务
                </Title>
                <div style={{ fontSize: '32px', fontWeight: 'bold', marginTop: '8px' }}>
                  {stats.activeTasks}
                </div>
                <Text style={{ color: 'rgba(255,255,255,0.8)' }}>
                  平均速度 {(stats.averageSpeed / 1024 / 1024).toFixed(1)} MB/s
                </Text>
              </div>

              <div style={{ 
                background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                color: 'white',
                padding: '24px',
                borderRadius: '12px',
                boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
              }}>
                <Title level={4} style={{ color: 'white', margin: 0 }}>
                  已下载大小
                </Title>
                <div style={{ fontSize: '32px', fontWeight: 'bold', marginTop: '8px' }}>
                  {(stats.totalDownloadedBytes / 1024 / 1024 / 1024).toFixed(1)}
                </div>
                <Text style={{ color: 'rgba(255,255,255,0.8)' }}>
                  GB
                </Text>
              </div>
            </div>

            {/* 快速操作 */}
            <div style={{ marginTop: '32px' }}>
              <Title level={4}>快速操作</Title>
              <Space size="large" wrap>
                <Button
                  type="primary"
                  size="large"
                  icon={<FilterOutlined />}
                  onClick={() => setShowFilterDrawer(true)}
                >
                  搜索资源
                </Button>
                <Button
                  size="large"
                  icon={<DownloadOutlined />}
                  onClick={() => setCurrentView('downloads')}
                >
                  查看下载 ({stats.activeTasks})
                </Button>
                <Button
                  size="large"
                  icon={<MonitorOutlined />}
                  onClick={() => setCurrentView('monitor')}
                >
                  系统监控
                </Button>
                <Button
                  size="large"
                  icon={<SettingOutlined />}
                  onClick={() => setShowSettings(true)}
                >
                  应用设置
                </Button>
              </Space>
            </div>
          </div>
        );

      case 'resources':
        return (
          <div style={{ padding: '24px' }}>
            <div style={{ 
              display: 'flex', 
              justifyContent: 'space-between', 
              alignItems: 'center',
              marginBottom: '24px'
            }}>
              <Title level={2} style={{ margin: 0 }}>资源管理</Title>
              <Button
                type="primary"
                icon={<FilterOutlined />}
                onClick={() => setShowFilterDrawer(true)}
              >
                重新筛选
              </Button>
            </div>
            
            <ResourceList
              resources={resources}
              onDownload={handleDownload}
              onBatchDownload={handleBatchDownload}
              userLoggedIn={isLoggedIn}
              loading={searchLoading}
              selectedResources={selectedResources}
              onSelectionChange={setSelectedResources}
            />
          </div>
        );

      case 'downloads':
        return (
          <div style={{ padding: '24px' }}>
            <DownloadManager
              tasks={tasks}
              onPause={pauseTask}
              onResume={resumeTask}
              onCancel={cancelTask}
              onRetry={retryTask}
              onClear={clearTask}
              onPauseAll={pauseAll}
              onResumeAll={resumeAll}
              onCancelAll={cancelAll}
              onClearCompleted={clearCompleted}
              stats={stats}
              enableNotifications={true}
              showOverallProgress={true}
            />
          </div>
        );

      case 'monitor':
        return (
          <SystemResourceMonitor
            onOptimize={() => {
              notification.info({ message: '系统优化功能开发中' });
            }}
            onCleanup={() => {
              notification.info({ message: '磁盘清理功能开发中' });
            }}
          />
        );

      default:
        return <div>页面未找到</div>;
    }
  };

  return (
    <Layout style={{ height: '100vh' }}>
      {/* 侧边栏 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        width={240}
        style={{
          background: '#fff',
          boxShadow: '2px 0 8px rgba(0,0,0,0.1)'
        }}
      >
        <div style={{ 
          padding: '16px', 
          textAlign: 'center',
          borderBottom: '1px solid #f0f0f0'
        }}>
          {!collapsed ? (
            <Space>
              <DownloadOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
              <Title level={4} style={{ margin: 0, color: '#1890ff' }}>
                智慧教育下载器
              </Title>
            </Space>
          ) : (
            <DownloadOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
          )}
        </div>

        <Menu
          mode="inline"
          selectedKeys={[currentView]}
          items={menuItems}
          onClick={({ key }) => setCurrentView(key as ViewType)}
          style={{ border: 'none', marginTop: '8px' }}
        />
      </Sider>

      <Layout>
        {/* 顶部导航栏 */}
        <Header style={{
          background: '#fff',
          padding: '0 24px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          <Space>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ fontSize: '16px' }}
            />
            
            {/* 面包屑导航可以在这里添加 */}
          </Space>

          <Space>
            {/* 通知按钮 */}
            <Tooltip title="通知">
              <Button
                type="text"
                icon={<BellOutlined />}
                onClick={() => {
                  notification.info({ message: '通知功能开发中' });
                }}
              />
            </Tooltip>

            {/* 帮助按钮 */}
            <Tooltip title="帮助">
              <Button
                type="text"
                icon={<QuestionCircleOutlined />}
                onClick={() => {
                  notification.info({ message: '帮助功能开发中' });
                }}
              />
            </Tooltip>

            {/* 用户信息 */}
            {isLoggedIn ? (
              <Dropdown
                menu={{ items: userMenuItems }}
                placement="bottomRight"
              >
                <Space style={{ cursor: 'pointer' }}>
                  <Avatar icon={<UserOutlined />} />
                  <span>{user?.displayName || user?.username}</span>
                </Space>
              </Dropdown>
            ) : (
              <Space>
                <Badge status="default" />
                <Text type="secondary">访客模式</Text>
                <Button
                  type="link"
                  icon={<LoginOutlined />}
                  onClick={() => window.location.reload()}
                >
                  登录
                </Button>
              </Space>
            )}
          </Space>
        </Header>

        {/* 主内容区域 */}
        <Content style={{ 
          overflow: 'auto',
          background: '#f5f5f5'
        }}>
          {renderContent()}
        </Content>
      </Layout>

      {/* 筛选抽屉 */}
      <Drawer
        title="资源筛选"
        placement="right"
        width={400}
        open={showFilterDrawer}
        onClose={() => setShowFilterDrawer(false)}
      >
        <FilterPanel
          onFilterChange={handleFilterChange}
          loading={searchLoading}
          initialFilters={currentFilters}
        />
      </Drawer>

      {/* 设置面板 */}
      <SettingsPanel
        visible={showSettings}
        onClose={() => setShowSettings(false)}
        config={config}
        onConfigChange={onConfigChange}
      />

      {/* 浮动操作按钮 */}
      <FloatButton.Group
        trigger="hover"
        type="primary"
        style={{ right: 24 }}
        icon={<DownloadOutlined />}
      >
        <FloatButton
          icon={<FilterOutlined />}
          tooltip="搜索资源"
          onClick={() => setShowFilterDrawer(true)}
        />
        <FloatButton
          icon={<DownloadOutlined />}
          tooltip="下载管理"
          onClick={() => setCurrentView('downloads')}
          badge={{ count: stats.activeTasks }}
        />
        <FloatButton
          icon={<SettingOutlined />}
          tooltip="设置"
          onClick={() => setShowSettings(true)}
        />
      </FloatButton.Group>
    </Layout>
  );
};

export default MainLayout;