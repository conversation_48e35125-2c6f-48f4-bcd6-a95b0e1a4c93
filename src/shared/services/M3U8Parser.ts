import * as m3u8Parser from 'm3u8-parser';
import axios from 'axios';
import { URL } from 'url';
import { 
  M3U8Playlist, 
  M3U8Segment, 
  M3U8Error,
  NetworkError 
} from '../types';

/**
 * M3U8解析器配置
 */
export interface M3U8ParserConfig {
  timeout: number;
  maxRetries: number;
  retryDelay: number;
  userAgent: string;
}

/**
 * M3U8播放列表解析器
 * 负责解析M3U8播放列表文件，提取视频片段信息
 */
export class M3U8Parser {
  private config: M3U8ParserConfig;

  private readonly defaultConfig: M3U8ParserConfig = {
    timeout: 10000, // 10 seconds
    maxRetries: 3,
    retryDelay: 1000, // 1 second
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
  };

  constructor(config?: Partial<M3U8ParserConfig>) {
    this.config = { ...this.defaultConfig, ...config };
  }

  /**
   * 解析M3U8播放列表
   * @param playlistUrl M3U8播放列表URL
   * @returns 解析后的播放列表对象
   */
  async parsePlaylist(playlistUrl: string): Promise<M3U8Playlist> {
    try {
      // 下载M3U8文件内容
      const playlistContent = await this.downloadPlaylistContent(playlistUrl);
      
      // 解析M3U8内容
      const parser = new m3u8Parser.Parser();
      parser.push(playlistContent);
      parser.end();

      const parsedManifest = parser.manifest;

      // 检查是否是主播放列表（包含多个子播放列表）
      if (parsedManifest.playlists && parsedManifest.playlists.length > 0) {
        // 选择最高质量的播放列表
        const bestPlaylist = this.selectBestPlaylist(parsedManifest.playlists);
        const subPlaylistUrl = this.resolveUrl(playlistUrl, bestPlaylist.uri);
        return await this.parsePlaylist(subPlaylistUrl);
      }

      // 处理媒体播放列表
      if (!parsedManifest.segments || parsedManifest.segments.length === 0) {
        throw new M3U8Error('播放列表中没有找到视频片段');
      }

      // 转换为标准格式
      const baseUrl = this.getBaseUrl(playlistUrl);
      const segments: M3U8Segment[] = parsedManifest.segments.map((segment: any, index: number) => ({
        url: this.resolveUrl(baseUrl, segment.uri),
        duration: segment.duration,
        sequence: index,
        byteRange: segment.byterange ? {
          start: segment.byterange.offset || 0,
          length: segment.byterange.length
        } : undefined
      }));

      const totalDuration = segments.reduce((sum, segment) => sum + segment.duration, 0);

      // 检查加密信息 - 从segments中查找key信息
      const hasEncryption = parsedManifest.segments.some((segment: any) => segment.key);
      const firstKeySegment = parsedManifest.segments.find((segment: any) => segment.key);
      const firstKey = firstKeySegment ? firstKeySegment.key : null;

      const playlist: M3U8Playlist = {
        baseUrl,
        segments,
        totalDuration,
        isEncrypted: hasEncryption,
        keyUrl: firstKey && firstKey.uri
          ? this.resolveUrl(baseUrl, firstKey.uri)
          : undefined
      };

      return playlist;
    } catch (error) {
      if (error instanceof M3U8Error) {
        throw error;
      }
      throw new M3U8Error(`解析M3U8播放列表失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 下载播放列表内容
   */
  private async downloadPlaylistContent(url: string): Promise<string> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt < this.config.maxRetries; attempt++) {
      try {
        const response = await axios.get(url, {
          timeout: this.config.timeout,
          headers: {
            'User-Agent': this.config.userAgent,
            'Accept': 'application/vnd.apple.mpegurl, application/x-mpegURL, application/octet-stream, */*'
          },
          responseType: 'text'
        });

        if (response.status !== 200) {
          throw new NetworkError(`HTTP ${response.status}: ${response.statusText}`);
        }

        return response.data;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('未知错误');
        
        if (attempt < this.config.maxRetries - 1) {
          await this.delay(this.config.retryDelay * (attempt + 1));
        }
      }
    }

    throw new NetworkError(`下载播放列表失败，已重试${this.config.maxRetries}次: ${lastError?.message}`);
  }

  /**
   * 选择最佳播放列表（最高质量）
   */
  private selectBestPlaylist(playlists: any[]): any {
    // 按带宽排序，选择最高质量
    return playlists.reduce((best, current) => {
      const bestBandwidth = best.attributes?.BANDWIDTH || 0;
      const currentBandwidth = current.attributes?.BANDWIDTH || 0;
      return currentBandwidth > bestBandwidth ? current : best;
    });
  }

  /**
   * 解析相对URL为绝对URL
   */
  private resolveUrl(baseUrl: string, relativeUrl: string): string {
    try {
      // 如果已经是绝对URL，直接返回
      if (relativeUrl.startsWith('http://') || relativeUrl.startsWith('https://')) {
        return relativeUrl;
      }

      // 使用URL构造器解析相对路径
      const base = new URL(baseUrl);
      const resolved = new URL(relativeUrl, base);
      return resolved.toString();
    } catch (error) {
      throw new M3U8Error(`URL解析失败: ${baseUrl} + ${relativeUrl}`);
    }
  }

  /**
   * 获取基础URL
   */
  private getBaseUrl(url: string): string {
    try {
      const parsedUrl = new URL(url);
      const pathParts = parsedUrl.pathname.split('/');
      pathParts.pop(); // 移除文件名
      parsedUrl.pathname = pathParts.join('/') + '/';
      return parsedUrl.toString();
    } catch (error) {
      throw new M3U8Error(`获取基础URL失败: ${url}`);
    }
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 验证M3U8播放列表格式
   */
  static isValidM3U8Content(content: string): boolean {
    return content.trim().startsWith('#EXTM3U');
  }

  /**
   * 从URL判断是否为M3U8文件
   */
  static isM3U8Url(url: string): boolean {
    try {
      const parsedUrl = new URL(url);
      const pathname = parsedUrl.pathname.toLowerCase();
      return pathname.endsWith('.m3u8') || pathname.endsWith('.m3u');
    } catch {
      return false;
    }
  }
}

export default M3U8Parser;
