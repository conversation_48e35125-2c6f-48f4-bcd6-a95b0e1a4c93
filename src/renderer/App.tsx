import React, { useEffect } from 'react';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { SimpleMainApp } from './components/SimpleMainApp';

/**
 * 简化的应用组件
 * 用于验证 DevTools 错误修复效果
 */
const App: React.FC = () => {
  useEffect(() => {
    // 验证electronAPI可用性
    if (!(window as any).electronAPI?.apiRequest) {
      console.error('❌ electronAPI不可用，请检查preload脚本配置');
    } else {
      console.log('✅ electronAPI已就绪');
    }
  }, []);

  return (
    <ConfigProvider locale={zhCN}>
      <SimpleMainApp />
    </ConfigProvider>
  );
};

export default App;
