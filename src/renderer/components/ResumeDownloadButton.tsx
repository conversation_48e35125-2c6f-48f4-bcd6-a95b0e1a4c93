import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, message } from 'antd';
import { PlayCircleOutlined, PauseCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import { DownloadTask } from '../../shared/types';

interface ResumeDownloadButtonProps {
  task: DownloadTask;
  onPause: (taskId: string) => Promise<void>;
  onResume: (taskId: string) => Promise<void>;
  onRetry: (taskId: string) => Promise<void>;
  disabled?: boolean;
}

/**
 * 断点续传下载按钮组件
 * 根据任务状态显示不同的操作按钮
 */
export const ResumeDownloadButton: React.FC<ResumeDownloadButtonProps> = ({
  task,
  onPause,
  onResume,
  onRetry,
  disabled = false
}) => {
  const [loading, setLoading] = useState(false);

  const handlePause = async () => {
    if (loading) return;
    
    setLoading(true);
    try {
      await onPause(task.id);
      message.success('任务已暂停');
    } catch (error) {
      message.error(`暂停失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleResume = async () => {
    if (loading) return;
    
    setLoading(true);
    try {
      await onResume(task.id);
      message.success('任务已恢复');
    } catch (error) {
      message.error(`恢复失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  const handleRetry = async () => {
    if (loading) return;
    
    setLoading(true);
    try {
      await onRetry(task.id);
      message.success('任务已重试');
    } catch (error) {
      message.error(`重试失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  // 根据任务状态渲染不同的按钮
  const renderButton = () => {
    switch (task.status) {
      case 'downloading':
      case 'resuming':
        return (
          <Tooltip title="暂停下载">
            <Button
              type="text"
              icon={<PauseCircleOutlined />}
              onClick={handlePause}
              loading={loading}
              disabled={disabled}
              size="small"
            />
          </Tooltip>
        );

      case 'paused':
        if (task.canResume && task.resource.type === 'video') {
          return (
            <Tooltip title={`断点续传 (已完成 ${task.progress}%)`}>
              <Button
                type="text"
                icon={<PlayCircleOutlined />}
                onClick={handleResume}
                loading={loading}
                disabled={disabled}
                size="small"
                style={{ color: '#52c41a' }}
              />
            </Tooltip>
          );
        } else {
          return (
            <Tooltip title="重新开始下载">
              <Button
                type="text"
                icon={<PlayCircleOutlined />}
                onClick={handleResume}
                loading={loading}
                disabled={disabled}
                size="small"
              />
            </Tooltip>
          );
        }

      case 'failed':
        return (
          <Tooltip title="重试下载">
            <Button
              type="text"
              icon={<ReloadOutlined />}
              onClick={handleRetry}
              loading={loading}
              disabled={disabled}
              size="small"
              danger
            />
          </Tooltip>
        );

      case 'completed':
        return (
          <Tooltip title="下载已完成">
            <Button
              type="text"
              icon={<PlayCircleOutlined />}
              disabled
              size="small"
              style={{ color: '#52c41a' }}
            />
          </Tooltip>
        );

      default:
        return null;
    }
  };

  return <>{renderButton()}</>;
};

/**
 * 断点续传状态指示器
 */
interface ResumeStatusIndicatorProps {
  task: DownloadTask;
}

export const ResumeStatusIndicator: React.FC<ResumeStatusIndicatorProps> = ({ task }) => {
  if (task.resource.type !== 'video' || !task.resumeState) {
    return null;
  }

  const { resumeState } = task;
  const segmentProgress = resumeState.totalSegments > 0 
    ? Math.round((resumeState.downloadedSegments / resumeState.totalSegments) * 100)
    : 0;

  const getStatusColor = () => {
    switch (task.status) {
      case 'downloading':
      case 'resuming':
        return '#1890ff';
      case 'paused':
        return '#faad14';
      case 'completed':
        return '#52c41a';
      case 'failed':
        return '#ff4d4f';
      default:
        return '#d9d9d9';
    }
  };

  const getStatusText = () => {
    switch (task.status) {
      case 'downloading':
        return '下载中';
      case 'resuming':
        return '恢复中';
      case 'paused':
        return task.canResume ? '已暂停 (可续传)' : '已暂停';
      case 'completed':
        return '已完成';
      case 'failed':
        return '下载失败';
      default:
        return '等待中';
    }
  };

  return (
    <div style={{ fontSize: '12px', color: '#666' }}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <div
          style={{
            width: '8px',
            height: '8px',
            borderRadius: '50%',
            backgroundColor: getStatusColor()
          }}
        />
        <span>{getStatusText()}</span>
      </div>
      
      {resumeState.totalSegments > 0 && (
        <div style={{ marginTop: '4px' }}>
          <span>片段: {resumeState.downloadedSegments}/{resumeState.totalSegments}</span>
          {resumeState.resumeCount > 0 && (
            <span style={{ marginLeft: '8px', color: '#faad14' }}>
              (已续传 {resumeState.resumeCount} 次)
            </span>
          )}
        </div>
      )}
      
      {task.lastResumeAt && (
        <div style={{ marginTop: '2px', fontSize: '11px', color: '#999' }}>
          最后恢复: {task.lastResumeAt.toLocaleString()}
        </div>
      )}
    </div>
  );
};

/**
 * 网络状态指示器
 */
interface NetworkStatusIndicatorProps {
  isOnline: boolean;
  quality?: number;
  connectionType?: string;
}

export const NetworkStatusIndicator: React.FC<NetworkStatusIndicatorProps> = ({
  isOnline,
  quality = 0,
  connectionType = 'unknown'
}) => {
  const getStatusColor = () => {
    if (!isOnline) return '#ff4d4f';
    if (quality >= 80) return '#52c41a';
    if (quality >= 60) return '#faad14';
    return '#ff7a45';
  };

  const getStatusText = () => {
    if (!isOnline) return '离线';
    if (quality >= 80) return '网络良好';
    if (quality >= 60) return '网络一般';
    if (quality >= 40) return '网络较差';
    return '网络很差';
  };

  return (
    <Tooltip title={`连接类型: ${connectionType}, 质量评分: ${quality}`}>
      <div style={{ display: 'flex', alignItems: 'center', gap: '4px', fontSize: '12px' }}>
        <div
          style={{
            width: '6px',
            height: '6px',
            borderRadius: '50%',
            backgroundColor: getStatusColor()
          }}
        />
        <span style={{ color: getStatusColor() }}>{getStatusText()}</span>
      </div>
    </Tooltip>
  );
};

export default ResumeDownloadButton;
