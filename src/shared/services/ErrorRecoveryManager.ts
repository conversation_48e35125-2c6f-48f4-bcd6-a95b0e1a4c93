import { EventEmitter } from 'events';
import { SmartEduError, ErrorType } from '../types';
import { RetryManager, RetryConfig, RetryResult } from './RetryManager';

/**
 * 恢复策略类型
 */
export type RecoveryStrategy = 'retry' | 'fallback' | 'skip' | 'abort';

/**
 * 恢复动作
 */
export interface RecoveryAction {
  strategy: RecoveryStrategy;
  description: string;
  execute: () => Promise<any>;
  condition?: (error: Error) => boolean;
  priority: number;
}

/**
 * 恢复配置
 */
export interface RecoveryConfig {
  maxRecoveryAttempts: number;
  recoveryTimeout: number;
  enableAutoRecovery: boolean;
  fallbackStrategies: RecoveryStrategy[];
  errorThreshold: number;
  cooldownPeriod: number;
}

/**
 * 恢复状态
 */
export interface RecoveryState {
  errorCount: number;
  lastErrorTime: Date;
  recoveryAttempts: number;
  isInCooldown: boolean;
  cooldownUntil?: Date;
  activeStrategy?: RecoveryStrategy;
  recoveryHistory: Array<{
    error: Error;
    strategy: RecoveryStrategy;
    success: boolean;
    timestamp: Date;
  }>;
}

/**
 * 错误恢复管理器事件
 */
export interface ErrorRecoveryManagerEvents {
  'recovery-started': (error: Error, strategy: RecoveryStrategy) => void;
  'recovery-success': (error: Error, strategy: RecoveryStrategy, result: any) => void;
  'recovery-failed': (error: Error, strategy: RecoveryStrategy, reason: string) => void;
  'recovery-exhausted': (error: Error, attempts: number) => void;
  'cooldown-started': (duration: number) => void;
  'cooldown-ended': () => void;
}

/**
 * 错误恢复管理器
 * 提供智能错误恢复机制，支持多种恢复策略
 */
export class ErrorRecoveryManager extends EventEmitter {
  private retryManager: RetryManager;
  private config: RecoveryConfig;
  private state: RecoveryState;
  private recoveryActions: Map<ErrorType, RecoveryAction[]>;
  private cooldownTimer?: NodeJS.Timeout;

  private readonly defaultConfig: RecoveryConfig = {
    maxRecoveryAttempts: 3,
    recoveryTimeout: 30000, // 30 seconds
    enableAutoRecovery: true,
    fallbackStrategies: ['retry', 'fallback', 'skip'],
    errorThreshold: 5,
    cooldownPeriod: 60000 // 1 minute
  };

  constructor(
    retryManager?: RetryManager,
    config?: Partial<RecoveryConfig>
  ) {
    super();
    this.retryManager = retryManager || new RetryManager();
    this.config = { ...this.defaultConfig, ...config };
    this.state = this.initializeState();
    this.recoveryActions = new Map();
    
    this.setupDefaultRecoveryActions();
  }

  /**
   * 初始化状态
   */
  private initializeState(): RecoveryState {
    return {
      errorCount: 0,
      lastErrorTime: new Date(),
      recoveryAttempts: 0,
      isInCooldown: false,
      recoveryHistory: []
    };
  }

  /**
   * 设置默认恢复动作
   */
  private setupDefaultRecoveryActions(): void {
    // 网络错误恢复动作
    this.addRecoveryAction(ErrorType.NETWORK_ERROR, {
      strategy: 'retry',
      description: '网络重试',
      execute: async () => {
        // 等待网络恢复
        await this.waitForNetworkRecovery();
        return true;
      },
      priority: 1
    });

    this.addRecoveryAction(ErrorType.NETWORK_ERROR, {
      strategy: 'fallback',
      description: '降级网络请求',
      execute: async () => {
        // 使用更保守的网络设置
        return this.useFallbackNetworkSettings();
      },
      priority: 2
    });

    // API错误恢复动作
    this.addRecoveryAction(ErrorType.API_ERROR, {
      strategy: 'retry',
      description: 'API重试',
      execute: async () => {
        // 等待API服务恢复
        await this.delay(5000);
        return true;
      },
      condition: (error) => error.message.includes('5'),
      priority: 1
    });

    this.addRecoveryAction(ErrorType.API_ERROR, {
      strategy: 'fallback',
      description: '使用备用API',
      execute: async () => {
        // 切换到备用API端点
        return this.switchToFallbackAPI();
      },
      priority: 2
    });

    // 下载错误恢复动作
    this.addRecoveryAction(ErrorType.DOWNLOAD_ERROR, {
      strategy: 'retry',
      description: '重新下载',
      execute: async () => {
        // 清理部分下载的文件
        await this.cleanupPartialDownload();
        return true;
      },
      priority: 1
    });

    this.addRecoveryAction(ErrorType.DOWNLOAD_ERROR, {
      strategy: 'fallback',
      description: '降低下载并发数',
      execute: async () => {
        // 减少并发下载数
        return this.reduceConcurrency();
      },
      priority: 2
    });

    // 文件错误恢复动作
    this.addRecoveryAction(ErrorType.FILE_ERROR, {
      strategy: 'fallback',
      description: '更换存储位置',
      execute: async () => {
        // 尝试使用临时目录
        return this.useTemporaryDirectory();
      },
      priority: 1
    });

    // M3U8错误恢复动作
    this.addRecoveryAction(ErrorType.M3U8_ERROR, {
      strategy: 'retry',
      description: '重新解析M3U8',
      execute: async () => {
        // 清理缓存并重新解析
        await this.clearM3U8Cache();
        return true;
      },
      priority: 1
    });

    this.addRecoveryAction(ErrorType.M3U8_ERROR, {
      strategy: 'fallback',
      description: '使用备用解析器',
      execute: async () => {
        // 使用更宽松的解析规则
        return this.useFallbackM3U8Parser();
      },
      priority: 2
    });
  }

  /**
   * 添加恢复动作
   */
  addRecoveryAction(errorType: ErrorType, action: RecoveryAction): void {
    if (!this.recoveryActions.has(errorType)) {
      this.recoveryActions.set(errorType, []);
    }
    
    const actions = this.recoveryActions.get(errorType)!;
    actions.push(action);
    
    // 按优先级排序
    actions.sort((a, b) => a.priority - b.priority);
  }

  /**
   * 移除恢复动作
   */
  removeRecoveryAction(errorType: ErrorType, strategy: RecoveryStrategy): void {
    const actions = this.recoveryActions.get(errorType);
    if (actions) {
      const index = actions.findIndex(action => action.strategy === strategy);
      if (index !== -1) {
        actions.splice(index, 1);
      }
    }
  }

  /**
   * 尝试恢复错误
   */
  async attemptRecovery<T>(
    error: Error,
    originalOperation: () => Promise<T>
  ): Promise<T> {
    // 检查是否在冷却期
    if (this.state.isInCooldown) {
      throw new Error('错误恢复管理器正在冷却期，请稍后重试');
    }

    // 更新错误统计
    this.updateErrorStats(error);

    // 检查是否需要进入冷却期
    if (this.shouldEnterCooldown()) {
      await this.enterCooldown();
      throw new Error('错误频率过高，进入冷却期');
    }

    const errorType = this.getErrorType(error);
    const actions = this.recoveryActions.get(errorType) || [];

    // 过滤适用的恢复动作
    const applicableActions = actions.filter(action => 
      !action.condition || action.condition(error)
    );

    if (applicableActions.length === 0) {
      throw new Error(`没有适用的恢复策略: ${errorType}`);
    }

    // 尝试每个恢复动作
    for (const action of applicableActions) {
      if (this.state.recoveryAttempts >= this.config.maxRecoveryAttempts) {
        this.emit('recovery-exhausted', error, this.state.recoveryAttempts);
        throw new Error('恢复尝试次数已达上限');
      }

      try {
        this.state.recoveryAttempts++;
        this.state.activeStrategy = action.strategy;
        
        this.emit('recovery-started', error, action.strategy);

        // 执行恢复动作
        await Promise.race([
          action.execute(),
          this.createTimeoutPromise(this.config.recoveryTimeout)
        ]);

        // 恢复成功，重新尝试原始操作
        const result = await originalOperation();
        
        this.recordRecoverySuccess(error, action.strategy);
        this.emit('recovery-success', error, action.strategy, result);
        
        return result;
      } catch (recoveryError) {
        this.recordRecoveryFailure(error, action.strategy);
        this.emit('recovery-failed', error, action.strategy, 
          recoveryError instanceof Error ? recoveryError.message : '未知错误');
        
        // 继续尝试下一个恢复动作
        continue;
      }
    }

    // 所有恢复动作都失败了
    throw new Error('所有恢复策略都已失败');
  }

  /**
   * 自动恢复包装器
   */
  wrapWithAutoRecovery<T extends any[], R>(
    fn: (...args: T) => Promise<R>
  ): (...args: T) => Promise<R> {
    return async (...args: T): Promise<R> => {
      try {
        return await fn(...args);
      } catch (error) {
        if (this.config.enableAutoRecovery && error instanceof Error) {
          return await this.attemptRecovery(error, () => fn(...args));
        }
        throw error;
      }
    };
  }

  /**
   * 批量恢复操作
   */
  async recoverBatch<T>(
    operations: Array<() => Promise<T>>,
    continueOnError: boolean = true
  ): Promise<Array<{ success: boolean; result?: T; error?: Error }>> {
    const results: Array<{ success: boolean; result?: T; error?: Error }> = [];

    for (const operation of operations) {
      try {
        const result = await this.attemptRecovery(
          new Error('批量操作预检'), 
          operation
        );
        results.push({ success: true, result });
      } catch (error) {
        results.push({ 
          success: false, 
          error: error instanceof Error ? error : new Error('未知错误') 
        });
        
        if (!continueOnError) {
          break;
        }
      }
    }

    return results;
  }

  /**
   * 获取错误类型
   */
  private getErrorType(error: Error): ErrorType {
    if (error instanceof SmartEduError) {
      return error.type;
    }

    // 基于错误消息推断类型
    const message = error.message.toLowerCase();
    
    if (message.includes('network') || message.includes('网络')) {
      return ErrorType.NETWORK_ERROR;
    }
    if (message.includes('api') || message.includes('服务器')) {
      return ErrorType.API_ERROR;
    }
    if (message.includes('file') || message.includes('文件')) {
      return ErrorType.FILE_ERROR;
    }
    if (message.includes('download') || message.includes('下载')) {
      return ErrorType.DOWNLOAD_ERROR;
    }
    if (message.includes('m3u8') || message.includes('视频')) {
      return ErrorType.M3U8_ERROR;
    }

    return ErrorType.VALIDATION_ERROR; // 使用 VALIDATION_ERROR 作为未知错误的默认类型
  }

  /**
   * 更新错误统计
   */
  private updateErrorStats(error: Error): void {
    this.state.errorCount++;
    this.state.lastErrorTime = new Date();
  }

  /**
   * 检查是否应该进入冷却期
   */
  private shouldEnterCooldown(): boolean {
    const now = Date.now();
    const timeSinceLastError = now - this.state.lastErrorTime.getTime();
    
    // 如果错误频率过高，进入冷却期
    return this.state.errorCount >= this.config.errorThreshold && 
           timeSinceLastError < this.config.cooldownPeriod;
  }

  /**
   * 进入冷却期
   */
  private async enterCooldown(): Promise<void> {
    this.state.isInCooldown = true;
    this.state.cooldownUntil = new Date(Date.now() + this.config.cooldownPeriod);
    
    this.emit('cooldown-started', this.config.cooldownPeriod);

    this.cooldownTimer = setTimeout(() => {
      this.exitCooldown();
    }, this.config.cooldownPeriod);
  }

  /**
   * 退出冷却期
   */
  private exitCooldown(): void {
    this.state.isInCooldown = false;
    this.state.cooldownUntil = undefined;
    this.state.errorCount = 0;
    this.state.recoveryAttempts = 0;
    
    if (this.cooldownTimer) {
      clearTimeout(this.cooldownTimer);
      this.cooldownTimer = undefined;
    }
    
    this.emit('cooldown-ended');
  }

  /**
   * 记录恢复成功
   */
  private recordRecoverySuccess(error: Error, strategy: RecoveryStrategy): void {
    this.state.recoveryHistory.push({
      error,
      strategy,
      success: true,
      timestamp: new Date()
    });
    
    // 重置错误计数
    this.state.errorCount = Math.max(0, this.state.errorCount - 1);
  }

  /**
   * 记录恢复失败
   */
  private recordRecoveryFailure(error: Error, strategy: RecoveryStrategy): void {
    this.state.recoveryHistory.push({
      error,
      strategy,
      success: false,
      timestamp: new Date()
    });
  }

  /**
   * 创建超时Promise
   */
  private createTimeoutPromise(timeout: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => reject(new Error('恢复操作超时')), timeout);
    });
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 以下是具体的恢复动作实现

  /**
   * 等待网络恢复
   */
  private async waitForNetworkRecovery(): Promise<boolean> {
    // 简单的网络检查
    const maxAttempts = 5;
    for (let i = 0; i < maxAttempts; i++) {
      try {
        // 尝试访问一个可靠的网站
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);
        
        const response = await fetch('https://www.baidu.com', { 
          method: 'HEAD',
          signal: controller.signal
        });
        
        clearTimeout(timeoutId);
        if (response.ok) {
          return true;
        }
      } catch {
        // 继续等待
      }
      await this.delay(2000);
    }
    return false;
  }

  /**
   * 使用备用网络设置
   */
  private async useFallbackNetworkSettings(): Promise<boolean> {
    // 这里可以实现更保守的网络设置
    // 例如：减少超时时间、降低并发数等
    return true;
  }

  /**
   * 切换到备用API
   */
  private async switchToFallbackAPI(): Promise<boolean> {
    // 这里可以实现API端点切换逻辑
    return true;
  }

  /**
   * 清理部分下载的文件
   */
  private async cleanupPartialDownload(): Promise<void> {
    // 这里可以实现清理逻辑
  }

  /**
   * 减少并发数
   */
  private async reduceConcurrency(): Promise<boolean> {
    // 这里可以实现并发控制逻辑
    return true;
  }

  /**
   * 使用临时目录
   */
  private async useTemporaryDirectory(): Promise<boolean> {
    // 这里可以实现临时目录切换逻辑
    return true;
  }

  /**
   * 清理M3U8缓存
   */
  private async clearM3U8Cache(): Promise<void> {
    // 这里可以实现缓存清理逻辑
  }

  /**
   * 使用备用M3U8解析器
   */
  private async useFallbackM3U8Parser(): Promise<boolean> {
    // 这里可以实现备用解析器逻辑
    return true;
  }

  /**
   * 获取恢复统计信息
   */
  getRecoveryStats(): {
    totalAttempts: number;
    successRate: number;
    errorCount: number;
    isInCooldown: boolean;
    cooldownRemaining?: number;
    strategyStats: Record<RecoveryStrategy, { attempts: number; successes: number }>;
  } {
    const strategyStats: Record<RecoveryStrategy, { attempts: number; successes: number }> = {
      retry: { attempts: 0, successes: 0 },
      fallback: { attempts: 0, successes: 0 },
      skip: { attempts: 0, successes: 0 },
      abort: { attempts: 0, successes: 0 }
    };

    this.state.recoveryHistory.forEach(record => {
      strategyStats[record.strategy].attempts++;
      if (record.success) {
        strategyStats[record.strategy].successes++;
      }
    });

    const totalAttempts = this.state.recoveryHistory.length;
    const totalSuccesses = this.state.recoveryHistory.filter(r => r.success).length;
    const successRate = totalAttempts > 0 ? totalSuccesses / totalAttempts : 0;

    const cooldownRemaining = this.state.cooldownUntil ? 
      Math.max(0, this.state.cooldownUntil.getTime() - Date.now()) : undefined;

    return {
      totalAttempts,
      successRate,
      errorCount: this.state.errorCount,
      isInCooldown: this.state.isInCooldown,
      cooldownRemaining,
      strategyStats
    };
  }

  /**
   * 重置状态
   */
  reset(): void {
    if (this.cooldownTimer) {
      clearTimeout(this.cooldownTimer);
      this.cooldownTimer = undefined;
    }
    
    this.state = this.initializeState();
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<RecoveryConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 获取当前配置
   */
  getConfig(): RecoveryConfig {
    return { ...this.config };
  }
}

export default ErrorRecoveryManager;