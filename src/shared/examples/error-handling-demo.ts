/**
 * 错误处理和重试机制演示
 * 展示如何使用 RetryManager 和 ErrorRecoveryManager
 */

import { RetryManager } from '../services/RetryManager';
import { ErrorRecoveryManager } from '../services/ErrorRecoveryManager';
import { NetworkError, ApiError, DownloadError } from '../types';

/**
 * 模拟网络请求函数
 */
async function simulateNetworkRequest(shouldFail: boolean = false): Promise<string> {
  if (shouldFail) {
    throw new NetworkError('网络连接失败');
  }
  return 'success';
}

/**
 * 模拟下载操作
 */
async function simulateDownload(url: string, failCount: number = 0): Promise<string> {
  if (failCount > 0) {
    throw new DownloadError(`下载失败: ${url}`);
  }
  return `下载完成: ${url}`;
}

/**
 * 演示基本重试功能
 */
export async function demonstrateBasicRetry(): Promise<void> {
  console.log('=== 基本重试功能演示 ===');
  
  const retryManager = new RetryManager({
    maxRetries: 3,
    baseDelay: 1000,
    strategy: 'exponential'
  });

  // 监听重试事件
  retryManager.on('retry-attempt', (state, error) => {
    console.log(`重试第 ${state.attempt} 次，错误: ${error.message}`);
  });

  retryManager.on('retry-success', (state, result) => {
    console.log(`重试成功，总尝试次数: ${state.attempt + 1}`);
  });

  try {
    let attemptCount = 0;
    const result = await retryManager.executeWithRetry(async () => {
      attemptCount++;
      if (attemptCount < 3) {
        throw new NetworkError('网络暂时不可用');
      }
      return '操作成功';
    });

    console.log('结果:', result);
  } catch (error) {
    console.error('最终失败:', error);
  }
}

/**
 * 演示错误恢复功能
 */
export async function demonstrateErrorRecovery(): Promise<void> {
  console.log('\n=== 错误恢复功能演示 ===');
  
  const retryManager = new RetryManager();
  const recoveryManager = new ErrorRecoveryManager(retryManager);

  // 监听恢复事件
  recoveryManager.on('recovery-started', (error, strategy) => {
    console.log(`开始恢复，策略: ${strategy}，错误: ${error.message}`);
  });

  recoveryManager.on('recovery-success', (error, strategy, result) => {
    console.log(`恢复成功，策略: ${strategy}，结果: ${result}`);
  });

  recoveryManager.on('recovery-failed', (error, strategy, reason) => {
    console.log(`恢复失败，策略: ${strategy}，原因: ${reason}`);
  });

  try {
    let attemptCount = 0;
    const result = await recoveryManager.attemptRecovery(
      new NetworkError('网络连接失败'),
      async () => {
        attemptCount++;
        if (attemptCount < 2) {
          throw new Error('操作仍然失败');
        }
        return '恢复后操作成功';
      }
    );

    console.log('恢复结果:', result);
  } catch (error) {
    console.error('恢复失败:', error);
  }
}

/**
 * 演示批量操作重试
 */
export async function demonstrateBatchRetry(): Promise<void> {
  console.log('\n=== 批量操作重试演示 ===');
  
  const retryManager = new RetryManager({
    maxRetries: 2,
    baseDelay: 500
  });

  const operations = [
    () => simulateNetworkRequest(false), // 成功
    () => simulateDownload('file1.mp4', 1), // 失败1次后成功
    () => simulateDownload('file2.mp4', 0), // 直接成功
  ];

  const results = await retryManager.executeBatchWithRetry(operations);

  results.forEach((result, index) => {
    console.log(`操作 ${index + 1}:`, {
      成功: result.success,
      结果: result.result,
      错误: result.error?.message,
      尝试次数: result.attempts
    });
  });
}

/**
 * 演示自动恢复包装器
 */
export async function demonstrateAutoRecoveryWrapper(): Promise<void> {
  console.log('\n=== 自动恢复包装器演示 ===');
  
  const retryManager = new RetryManager();
  const recoveryManager = new ErrorRecoveryManager(retryManager);

  // 原始函数
  const originalFunction = async (data: string): Promise<string> => {
    if (Math.random() < 0.7) { // 70% 概率失败
      throw new NetworkError('随机网络错误');
    }
    return `处理完成: ${data}`;
  };

  // 包装为自动恢复函数
  const wrappedFunction = recoveryManager.wrapWithAutoRecovery(originalFunction);

  try {
    const result = await wrappedFunction('测试数据');
    console.log('包装函数结果:', result);
  } catch (error) {
    console.error('包装函数失败:', error);
  }
}

/**
 * 演示错误统计
 */
export async function demonstrateErrorStats(): Promise<void> {
  console.log('\n=== 错误统计演示 ===');
  
  const retryManager = new RetryManager();
  const recoveryManager = new ErrorRecoveryManager(retryManager);

  // 执行一些操作来生成统计数据
  const operations = [
    () => Promise.resolve('success1'),
    () => Promise.reject(new NetworkError('网络错误1')),
    () => Promise.reject(new ApiError('API错误1')),
    () => Promise.resolve('success2'),
  ];

  for (const operation of operations) {
    try {
      await recoveryManager.attemptRecovery(new NetworkError('测试错误'), operation);
    } catch {
      // 忽略错误，只是为了生成统计数据
    }
  }

  const stats = recoveryManager.getRecoveryStats();
  console.log('恢复统计:', {
    总尝试次数: stats.totalAttempts,
    成功率: `${(stats.successRate * 100).toFixed(1)}%`,
    错误计数: stats.errorCount,
    是否在冷却期: stats.isInCooldown,
    策略统计: stats.strategyStats
  });
}

/**
 * 运行所有演示
 */
export async function runAllDemonstrations(): Promise<void> {
  try {
    await demonstrateBasicRetry();
    await demonstrateErrorRecovery();
    await demonstrateBatchRetry();
    await demonstrateAutoRecoveryWrapper();
    await demonstrateErrorStats();
    
    console.log('\n=== 所有演示完成 ===');
  } catch (error) {
    console.error('演示过程中出现错误:', error);
  }
}

// 如果直接运行此文件，执行演示
if (require.main === module) {
  runAllDemonstrations();
}