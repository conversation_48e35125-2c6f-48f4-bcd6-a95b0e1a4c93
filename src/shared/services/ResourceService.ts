import { ResourceDetail } from '../types';
import { SmartEduClient } from './SmartEduClient';

/**
 * 资源服务
 * 提供资源相关的操作功能
 */
export class ResourceService {
  private static instance: ResourceService;
  private smartEduClient: SmartEduClient;

  private constructor() {
    this.smartEduClient = new SmartEduClient();
  }

  public static getInstance(): ResourceService {
    if (!ResourceService.instance) {
      ResourceService.instance = new ResourceService();
    }
    return ResourceService.instance;
  }

  /**
   * 获取资源详情
   */
  async getResourceDetail(resourceId: string): Promise<ResourceDetail> {
    try {
      console.log('🔍 获取资源详情:', resourceId);

      const electronAPI = (window as any).electronAPI;
      if (!electronAPI?.apiRequest) {
        throw new Error('Electron API代理不可用');
      }

      // 尝试从多个API端点获取资源详情
      const apiEndpoints = [
        `https://s-file-1.ykt.cbern.com.cn/zxx/ndrv2/national_lesson/resources/details/${resourceId}.json`,
        `https://s-file-2.ykt.cbern.com.cn/zxx/ndrv2/national_lesson/resources/details/${resourceId}.json`,
        `https://s-file-1.ykt.cbern.com.cn/zxx/ndrv2/resources/${resourceId}.json`
      ];

      for (const url of apiEndpoints) {
        try {
          const response = await electronAPI.apiRequest(url);
          if (response.success && response.data) {
            return this.formatResourceDetail(response.data);
          }
        } catch (error) {
          console.log(`API端点失败: ${url}`, error);
          continue;
        }
      }

      throw new Error('无法获取资源详情');
    } catch (error) {
      console.error('获取资源详情失败:', error);
      throw error;
    }
  }

  /**
   * 格式化资源详情
   */
  private formatResourceDetail(data: any): ResourceDetail {
    return {
      id: data.id || data.resource_id || '',
      title: data.title || data.name || '未知资源',
      description: data.description || data.summary || '',
      type: this.mapResourceType(data.type),
      url: data.url || data.download_url || data.file_url || '',
      tags: data.tags || [],
      metadata: {
        stage: data.stage || '',
        grade: data.grade || '',
        subject: data.subject || '',
        version: data.version || '',
        volume: data.volume || '',
        fileSize: data.size || data.fileSize || 0
      },
      requiresAuth: data.requires_auth || false,
      accessLevel: data.access_level || 'public',
      createdAt: data.created_at ? new Date(data.created_at) : new Date(),
      updatedAt: data.updated_at ? new Date(data.updated_at) : new Date()
    };
  }

  /**
   * 映射资源类型
   */
  private mapResourceType(type: string): 'textbook' | 'video' | 'teachingmaterial' {
    if (type && typeof type === 'string') {
      const lowerType = type.toLowerCase();
      if (lowerType.includes('video') || lowerType.includes('mp4') || lowerType.includes('m3u8')) {
        return 'video';
      }
      if (lowerType.includes('textbook') || lowerType.includes('book') || lowerType.includes('pdf')) {
        return 'textbook';
      }
    }
    return 'teachingmaterial';
  }

  /**
   * 检查资源访问权限 - 暂未实现
   */
  async checkResourceAccess(resourceId: string): Promise<{
    hasAccess: boolean;
    requiresAuth: boolean;
    accessLevel: 'public' | 'premium' | 'restricted';
  }> {
    return {
      hasAccess: true,
      requiresAuth: false,
      accessLevel: 'public'
    };
  }

  /**
   * 获取资源下载URL - 暂未实现
   */
  async getDownloadUrl(resourceId: string): Promise<string> {
    throw new Error('下载URL获取功能暂未实现');
  }

  /**
   * 搜索资源 - 暂未实现
   */
  async searchResources(filters: any): Promise<any[]> {
    console.log('⚠️ ResourceService.searchResources 暂未实现，返回空数组');
    return [];
  }
}

export default ResourceService;
