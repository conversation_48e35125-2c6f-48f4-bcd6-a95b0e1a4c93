import { contextBridge, ipc<PERSON>enderer } from 'electron';

// 定义 API 对象
const electronAPI = {
  // App info
  getAppVersion: () => ipcRenderer.invoke('app:getVersion'),
  getAppName: () => ipcRenderer.invoke('app:getName'),

  // Download related APIs
  getDefaultDownloadPath: () => ipcRenderer.invoke('download:getDefaultPath'),
  selectDownloadPath: () => ipcRenderer.invoke('download:selectPath'),
  openDownloadFolder: (path: string) => ipcRenderer.invoke('download:openFolder', path),

  // File system APIs
  checkFileExists: (path: string) => ipcRenderer.invoke('fs:checkExists', path),
  getFileStats: (path: string) => ipcRenderer.invoke('fs:getStats', path),

  // Notification APIs
  showNotification: (title: string, body: string) => ipcRenderer.invoke('notification:show', title, body),

  // Auto updater APIs
  checkForUpdates: () => ipcRenderer.invoke('updater:checkForUpdates'),
  quitAndInstall: () => ipcRenderer.invoke('updater:quitAndInstall'),

  // DevTools APIs (仅在开发环境可用)
  toggleDevTools: () => ipcRenderer.invoke('devtools:toggle'),
  isDevToolsOpened: () => ipcRenderer.invoke('devtools:isOpened'),

  // Download APIs
  startDownload: (url: string, config: any): Promise<string | { error: string; taskId: string; url?: string }> =>
    ipcRenderer.invoke('download:start', url, config),
  pauseDownload: (taskId: string) => ipcRenderer.invoke('download:pause', taskId),
  resumeDownload: (taskId: string) => ipcRenderer.invoke('download:resume', taskId),
  cancelDownload: (taskId: string) => ipcRenderer.invoke('download:cancel', taskId),
  getAllDownloadTasks: () => ipcRenderer.invoke('download:getAllTasks'),

  // Download event listeners
  onDownloadProgress: (callback: (taskId: string, progress: any) => void) => {
    ipcRenderer.on('download:progress', (event, taskId, progress) => callback(taskId, progress));
  },
  onDownloadStatusChange: (callback: (taskId: string, status: string) => void) => {
    ipcRenderer.on('download:statusChange', (event, taskId, status) => callback(taskId, status));
  },

  // API代理 - 解决CORS问题
  apiRequest: (url: string, options?: any) => ipcRenderer.invoke('api:request', url, options),

  // 认证功能
  login: () => ipcRenderer.invoke('auth:login'),
};

// 根据环境选择不同的暴露方式
if (process.env.NODE_ENV === 'development') {
  // 开发环境：直接暴露到 window 对象（因为 contextIsolation: false）
  (window as any).electronAPI = electronAPI;
} else {
  // 生产环境：使用 contextBridge（因为 contextIsolation: true）
  contextBridge.exposeInMainWorld('electronAPI', electronAPI);
}

// Type definitions are in src/shared/types/electron.d.ts
