import * as fs from 'fs-extra';
import * as path from 'path';
import axios from 'axios';
import { CourseResource, DownloadProgress } from '../types';
import { AuthService } from './AuthService';

/**
 * SuperBoard课件下载器
 * 专门处理智慧教育平台的SuperBoard格式课件
 */
export class SuperBoardDownloader {
  private authService: AuthService;

  constructor() {
    this.authService = AuthService.getInstance();
  }

  /**
   * 下载SuperBoard课件
   */
  async downloadSuperBoard(
    resource: CourseResource,
    onProgress?: (progress: DownloadProgress) => void
  ): Promise<string> {
    try {
      console.log(`📊 开始下载SuperBoard课件: ${resource.title}`);
      console.log(`📍 课件URL: ${resource.url}`);

      // 检查是否为SuperBoard资源
      if (!this.isSuperBoardResource(resource.url)) {
        throw new Error('不是有效的SuperBoard课件资源');
      }

      // 生成输出路径
      const outputPath = this.generateOutputPath(resource);
      await fs.ensureDir(path.dirname(outputPath));

      // 检查文件是否已存在
      if (await fs.pathExists(outputPath)) {
        console.log(`✅ 文件已存在，跳过下载: ${outputPath}`);
        return outputPath;
      }

      // 尝试解析SuperBoard资源的真实下载链接
      const realDownloadUrl = await this.parseSuperBoardUrl(resource.url);
      
      if (!realDownloadUrl) {
        // 如果无法解析真实链接，尝试直接下载
        console.log(`⚠️ 无法解析真实下载链接，尝试直接下载`);
        return await this.directDownload(resource.url, outputPath, onProgress);
      }

      // 下载文件
      return await this.downloadFile(realDownloadUrl, outputPath, onProgress);

    } catch (error) {
      console.error(`❌ SuperBoard课件下载失败:`, error);
      throw error;
    }
  }

  /**
   * 检测是否为SuperBoard资源
   */
  private isSuperBoardResource(url: string): boolean {
    const urlLower = url.toLowerCase();
    return urlLower.includes('.superboard') || 
           urlLower.includes('superboard') ||
           urlLower.includes('courseware') ||
           (urlLower.includes('ykt.cbern.com.cn') && urlLower.includes('ppt'));
  }

  /**
   * 解析SuperBoard资源的真实下载链接
   */
  private async parseSuperBoardUrl(url: string): Promise<string | null> {
    try {
      console.log(`🔍 解析SuperBoard资源链接: ${url}`);

      // 构建请求头
      const headers = this.authService.buildRequestHeaders(url);

      // 发送HEAD请求获取资源信息
      const response = await axios.head(url, { 
        headers,
        timeout: 10000,
        maxRedirects: 5
      });

      // 检查响应头中的重定向或真实链接
      const location = response.headers.location;
      if (location) {
        console.log(`🔄 发现重定向链接: ${location}`);
        return location;
      }

      // 检查Content-Type是否为可下载的文件类型
      const contentType = response.headers['content-type'];
      if (contentType && this.isDownloadableContentType(contentType)) {
        console.log(`✅ 确认为可下载文件类型: ${contentType}`);
        return url;
      }

      // 如果是HTML页面，可能需要进一步解析
      if (contentType && contentType.includes('text/html')) {
        console.log(`📄 检测到HTML页面，尝试解析下载链接`);
        return await this.parseHtmlForDownloadLink(url);
      }

      return null;
    } catch (error) {
      console.warn(`⚠️ 解析SuperBoard链接失败: ${error}`);
      return null;
    }
  }

  /**
   * 从HTML页面解析下载链接
   */
  private async parseHtmlForDownloadLink(url: string): Promise<string | null> {
    try {
      const headers = this.authService.buildRequestHeaders(url);
      const response = await axios.get(url, { 
        headers,
        timeout: 10000
      });

      const html = response.data;
      
      // 使用正则表达式查找可能的下载链接
      const downloadLinkPatterns = [
        /href=["']([^"']*\.(?:pptx?|pdf|zip|rar))["']/gi,
        /src=["']([^"']*\.(?:pptx?|pdf|zip|rar))["']/gi,
        /url\s*:\s*["']([^"']*\.(?:pptx?|pdf|zip|rar))["']/gi,
        /"download_url"\s*:\s*"([^"]+)"/gi,
        /"file_url"\s*:\s*"([^"]+)"/gi
      ];

      for (const pattern of downloadLinkPatterns) {
        const matches = html.matchAll(pattern);
        for (const match of matches) {
          if (match[1]) {
            const downloadUrl = this.resolveUrl(url, match[1]);
            console.log(`🔗 找到潜在下载链接: ${downloadUrl}`);
            return downloadUrl;
          }
        }
      }

      return null;
    } catch (error) {
      console.warn(`⚠️ 解析HTML下载链接失败: ${error}`);
      return null;
    }
  }

  /**
   * 检查Content-Type是否为可下载的文件类型
   */
  private isDownloadableContentType(contentType: string): boolean {
    const downloadableTypes = [
      'application/vnd.openxmlformats-officedocument.presentationml.presentation', // pptx
      'application/vnd.ms-powerpoint', // ppt
      'application/pdf',
      'application/zip',
      'application/x-rar-compressed',
      'application/octet-stream'
    ];

    return downloadableTypes.some(type => contentType.includes(type));
  }

  /**
   * 直接下载文件
   */
  private async directDownload(
    url: string, 
    outputPath: string, 
    onProgress?: (progress: DownloadProgress) => void
  ): Promise<string> {
    const headers = this.authService.buildRequestHeaders(url);
    
    const response = await axios({
      method: 'GET',
      url,
      headers,
      responseType: 'stream',
      timeout: 60000
    });

    const totalSize = parseInt(response.headers['content-length'] || '0', 10);
    let downloadedSize = 0;

    const writer = fs.createWriteStream(outputPath);
    
    response.data.on('data', (chunk: Buffer) => {
      downloadedSize += chunk.length;
      
      if (onProgress && totalSize > 0) {
        const progress = (downloadedSize / totalSize) * 100;
        onProgress({
          taskId: 'superboard_download',
          status: 'downloading',
          progress,
          downloadedBytes: downloadedSize,
          totalBytes: totalSize,
          speed: 0, // 简化实现，不计算速度
          estimatedTime: 0
        });
      }
    });

    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', () => {
        console.log(`✅ SuperBoard课件下载完成: ${outputPath}`);
        resolve(outputPath);
      });
      
      writer.on('error', (error) => {
        console.error(`❌ 文件写入失败: ${error}`);
        reject(error);
      });
    });
  }

  /**
   * 下载文件
   */
  private async downloadFile(
    url: string, 
    outputPath: string, 
    onProgress?: (progress: DownloadProgress) => void
  ): Promise<string> {
    return this.directDownload(url, outputPath, onProgress);
  }

  /**
   * 生成输出路径
   */
  private generateOutputPath(resource: CourseResource): string {
    // 简化的路径生成逻辑
    const fileName = this.sanitizeFileName(resource.title);
    const extension = this.getFileExtension(resource.url) || '.pptx';
    
    return path.join(
      process.cwd(),
      'downloads',
      'superboard',
      `${fileName}${extension}`
    );
  }

  /**
   * 清理文件名
   */
  private sanitizeFileName(fileName: string): string {
    return fileName.replace(/[<>:"/\\|?*]/g, '_').trim();
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(url: string): string | null {
    try {
      const pathname = new URL(url).pathname;
      const match = pathname.match(/\.([^.]+)$/);
      return match ? `.${match[1]}` : null;
    } catch {
      return null;
    }
  }

  /**
   * 解析相对URL为绝对URL
   */
  private resolveUrl(baseUrl: string, relativeUrl: string): string {
    try {
      if (relativeUrl.startsWith('http://') || relativeUrl.startsWith('https://')) {
        return relativeUrl;
      }
      return new URL(relativeUrl, baseUrl).toString();
    } catch {
      return relativeUrl;
    }
  }
}

export default SuperBoardDownloader;
