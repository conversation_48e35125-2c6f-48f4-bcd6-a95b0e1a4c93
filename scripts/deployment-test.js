#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs-extra');
const path = require('path');
const os = require('os');

/**
 * 完整的部署测试脚本
 * 包含配置检查、构建测试、打包测试等
 */
class DeploymentTester {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.releaseDir = path.join(this.projectRoot, 'release');
    this.distDir = path.join(this.projectRoot, 'dist');
    this.platform = os.platform();
    this.testResults = [];
    this.errors = [];
  }

  /**
   * 运行完整的部署测试
   */
  async runDeploymentTests() {
    console.log('🚀 开始完整部署测试...\n');
    console.log(`测试平台: ${this.platform}`);
    console.log(`Node.js 版本: ${process.version}`);
    console.log(`工作目录: ${this.projectRoot}\n`);

    try {
      // 1. 环境检查
      await this.checkEnvironment();
      
      // 2. 配置检查
      await this.checkConfiguration();
      
      // 3. 依赖检查
      await this.checkDependencies();
      
      // 4. 构建资源检查
      await this.checkBuildResources();
      
      // 5. 清理旧文件
      await this.cleanupOldBuilds();
      
      // 6. 测试打包（不生成安装包）
      await this.testPackaging();
      
      this.printResults();
    } catch (error) {
      console.error('❌ 部署测试失败:', error.message);
      this.printResults();
      process.exit(1);
    }
  }

  /**
   * 检查环境
   */
  async checkEnvironment() {
    console.log('🔍 检查环境...');

    // 检查 Node.js 版本
    const nodeVersion = process.version;
    const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
    if (majorVersion >= 16) {
      this.addResult('✅', `Node.js 版本: ${nodeVersion} (满足要求)`);
    } else {
      this.addResult('❌', `Node.js 版本: ${nodeVersion} (需要 16.0 或更高)`);
    }

    // 检查 npm
    try {
      const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
      this.addResult('✅', `npm 版本: ${npmVersion}`);
    } catch (error) {
      this.addResult('❌', 'npm 未安装或不可用');
    }

    // 检查 Python（node-gyp 需要）
    try {
      const pythonVersion = execSync('python --version 2>&1 || python3 --version 2>&1', { encoding: 'utf8' }).trim();
      this.addResult('✅', `Python: ${pythonVersion}`);
    } catch (error) {
      this.addResult('⚠️', 'Python 未找到（某些原生模块可能需要）');
    }

    // 检查平台特定工具
    if (this.platform === 'darwin') {
      try {
        execSync('xcode-select -p', { stdio: 'pipe' });
        this.addResult('✅', 'Xcode Command Line Tools 已安装');
      } catch (error) {
        this.addResult('⚠️', 'Xcode Command Line Tools 未安装（代码签名需要）');
      }
    }
  }

  /**
   * 检查配置
   */
  async checkConfiguration() {
    console.log('⚙️  检查配置...');

    try {
      const packageJson = await fs.readJson(path.join(this.projectRoot, 'package.json'));
      
      // 基本信息检查
      const requiredFields = ['name', 'version', 'description', 'main', 'author'];
      for (const field of requiredFields) {
        if (packageJson[field]) {
          this.addResult('✅', `${field}: ${packageJson[field]}`);
        } else {
          this.addResult('❌', `缺少必要字段: ${field}`);
        }
      }

      // build 配置检查
      if (packageJson.build) {
        this.addResult('✅', 'electron-builder 配置存在');
        
        const build = packageJson.build;
        if (build.appId && build.productName) {
          this.addResult('✅', `应用配置: ${build.appId} - ${build.productName}`);
        } else {
          this.addResult('❌', '缺少 appId 或 productName');
        }

        // 检查平台配置
        const platforms = ['mac', 'win', 'linux'];
        let configuredPlatforms = 0;
        for (const platform of platforms) {
          if (build[platform]) {
            configuredPlatforms++;
            this.addResult('✅', `${platform} 平台配置完整`);
          }
        }
        
        if (configuredPlatforms === 0) {
          this.addResult('❌', '没有配置任何平台');
        }

      } else {
        this.addResult('❌', '缺少 electron-builder 配置');
      }

    } catch (error) {
      this.addResult('❌', `配置检查失败: ${error.message}`);
    }
  }

  /**
   * 检查依赖
   */
  async checkDependencies() {
    console.log('📦 检查依赖...');

    try {
      const packageJson = await fs.readJson(path.join(this.projectRoot, 'package.json'));
      
      // 检查关键依赖
      const criticalDeps = {
        'electron': 'Electron 框架',
        'electron-builder': '打包工具'
      };

      for (const [dep, description] of Object.entries(criticalDeps)) {
        let found = false;
        let version = '';
        
        if (packageJson.dependencies && packageJson.dependencies[dep]) {
          found = true;
          version = packageJson.dependencies[dep];
        } else if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
          found = true;
          version = packageJson.devDependencies[dep];
        }
        
        if (found) {
          this.addResult('✅', `${description}: ${version}`);
        } else {
          this.addResult('❌', `缺少关键依赖: ${dep}`);
        }
      }

      // 检查 node_modules
      const nodeModulesPath = path.join(this.projectRoot, 'node_modules');
      if (await fs.pathExists(nodeModulesPath)) {
        this.addResult('✅', 'node_modules 目录存在');
        
        // 检查关键模块是否实际安装
        for (const dep of Object.keys(criticalDeps)) {
          const depPath = path.join(nodeModulesPath, dep);
          if (await fs.pathExists(depPath)) {
            this.addResult('✅', `${dep} 模块已安装`);
          } else {
            this.addResult('❌', `${dep} 模块未安装`);
          }
        }
      } else {
        this.addResult('❌', 'node_modules 目录不存在，请运行 npm install');
      }

    } catch (error) {
      this.addResult('❌', `依赖检查失败: ${error.message}`);
    }
  }

  /**
   * 检查构建资源
   */
  async checkBuildResources() {
    console.log('📁 检查构建资源...');

    const buildDir = path.join(this.projectRoot, 'build');
    
    if (await fs.pathExists(buildDir)) {
      this.addResult('✅', 'build 目录存在');
      
      // 检查配置文件
      const configFiles = [
        'entitlements.mac.plist',
        'installer.nsh',
        'app-metadata.json'
      ];
      
      for (const file of configFiles) {
        const filePath = path.join(buildDir, file);
        if (await fs.pathExists(filePath)) {
          this.addResult('✅', `配置文件: ${file}`);
        } else {
          this.addResult('⚠️', `配置文件缺失: ${file}`);
        }
      }

      // 检查图标文件
      const iconFiles = ['icon.icns', 'icon.ico', 'icon.png'];
      for (const file of iconFiles) {
        const filePath = path.join(buildDir, file);
        if (await fs.pathExists(filePath)) {
          const stats = await fs.stat(filePath);
          if (stats.size > 100) { // 简单检查文件不是空的
            this.addResult('✅', `图标文件: ${file}`);
          } else {
            this.addResult('⚠️', `图标文件可能是占位符: ${file}`);
          }
        } else {
          this.addResult('❌', `图标文件缺失: ${file}`);
        }
      }

    } else {
      this.addResult('❌', 'build 目录不存在');
    }
  }

  /**
   * 清理旧构建
   */
  async cleanupOldBuilds() {
    console.log('🧹 清理旧构建...');

    const dirsToClean = [this.distDir, this.releaseDir];
    
    for (const dir of dirsToClean) {
      if (await fs.pathExists(dir)) {
        try {
          await fs.remove(dir);
          this.addResult('✅', `清理目录: ${path.basename(dir)}`);
        } catch (error) {
          this.addResult('⚠️', `清理目录失败: ${path.basename(dir)} - ${error.message}`);
        }
      }
    }
  }

  /**
   * 测试打包
   */
  async testPackaging() {
    console.log('📦 测试打包...');

    try {
      // 首先尝试构建源代码（如果可能的话）
      console.log('   正在构建源代码...');
      try {
        execSync('npm run build', { 
          cwd: this.projectRoot, 
          stdio: 'pipe',
          timeout: 120000 // 2分钟超时
        });
        this.addResult('✅', '源代码构建成功');
      } catch (error) {
        this.addResult('⚠️', '源代码构建失败，但继续测试打包配置');
        this.addResult('ℹ️', '构建失败通常是由于 TypeScript 错误，不影响打包配置测试');
      }

      // 测试打包配置（不实际构建，只验证配置）
      console.log('   正在验证打包配置...');
      try {
        // 使用 --help 参数来验证 electron-builder 配置
        const helpOutput = execSync('npx electron-builder --help', { 
          cwd: this.projectRoot, 
          encoding: 'utf8',
          stdio: 'pipe'
        });
        
        if (helpOutput.includes('electron-builder')) {
          this.addResult('✅', 'electron-builder 可用');
        }

        // 尝试读取和验证配置
        const packageJson = await fs.readJson(path.join(this.projectRoot, 'package.json'));
        if (packageJson.build) {
          this.addResult('✅', '打包配置格式正确');
          
          // 检查输出目录配置
          const outputDir = packageJson.build.directories?.output || 'dist';
          this.addResult('✅', `输出目录配置: ${outputDir}`);
          
          // 检查文件包含配置
          if (packageJson.build.files) {
            this.addResult('✅', `文件包含配置: ${packageJson.build.files.length} 项规则`);
          }
        }

      } catch (error) {
        this.addResult('❌', `打包配置验证失败: ${error.message}`);
      }

    } catch (error) {
      this.addResult('❌', `打包测试失败: ${error.message}`);
    }
  }

  /**
   * 添加测试结果
   */
  addResult(status, message) {
    this.testResults.push({ status, message });
    console.log(`   ${status} ${message}`);
    
    if (status === '❌') {
      this.errors.push(message);
    }
  }

  /**
   * 打印测试结果摘要
   */
  printResults() {
    console.log('\n📊 部署测试结果摘要:');
    console.log('='.repeat(60));

    const passed = this.testResults.filter(r => r.status === '✅').length;
    const failed = this.testResults.filter(r => r.status === '❌').length;
    const warnings = this.testResults.filter(r => r.status === '⚠️').length;
    const info = this.testResults.filter(r => r.status === 'ℹ️').length;

    console.log(`✅ 通过: ${passed}`);
    console.log(`❌ 失败: ${failed}`);
    console.log(`⚠️  警告: ${warnings}`);
    console.log(`ℹ️  信息: ${info}`);
    console.log(`📊 总计: ${this.testResults.length}`);

    if (this.errors.length > 0) {
      console.log('\n❌ 关键问题列表:');
      this.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error}`);
      });
    }

    console.log('\n📋 下一步建议:');
    
    if (failed === 0) {
      console.log('🎉 部署配置完整！可以进行以下操作：');
      console.log('1. 运行 npm run pack 进行测试打包');
      console.log('2. 运行 npm run dist 生成安装包');
      console.log('3. 运行 npm run dist:mac/win/linux 生成特定平台安装包');
      
      if (warnings > 0) {
        console.log('\n⚠️  建议处理警告项目以获得更好的用户体验：');
        console.log('- 添加真实的应用图标');
        console.log('- 配置代码签名证书（用于发布）');
        console.log('- 完善应用元数据信息');
      }
    } else {
      console.log('❌ 请先修复上述关键问题：');
      console.log('1. 安装缺失的依赖：npm install');
      console.log('2. 检查 package.json 配置');
      console.log('3. 确保构建资源文件存在');
      console.log('4. 修复后重新运行此测试');
    }

    console.log('\n📚 更多信息请参考:');
    console.log('- 部署指南: docs/deployment-guide.md');
    console.log('- Electron Builder 文档: https://www.electron.build/');
  }
}

// 运行测试
if (require.main === module) {
  const tester = new DeploymentTester();
  tester.runDeploymentTests().catch(console.error);
}

module.exports = DeploymentTester;