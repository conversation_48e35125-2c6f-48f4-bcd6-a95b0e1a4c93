/**
 * Polyfills for Electron renderer process
 * 解决 Node.js 全局变量在渲染进程中的兼容性问题
 */

// 修复 global 变量未定义的问题
if (typeof global === 'undefined') {
  (window as any).global = window;
}

// 修复 process 变量（如果需要）
if (typeof process === 'undefined') {
  (window as any).process = {
    env: {
      NODE_ENV: 'development'
    }
  };
}

// 修复 Buffer 变量（如果需要）
if (typeof Buffer === 'undefined') {
  (window as any).Buffer = {};
}

// 确保 globalThis 可用（现代浏览器支持）
if (typeof globalThis === 'undefined') {
  (window as any).globalThis = window;
}

console.log('✅ Renderer polyfills loaded successfully');
