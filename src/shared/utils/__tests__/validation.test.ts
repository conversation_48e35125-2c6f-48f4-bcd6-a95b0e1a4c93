import {
  validateLoginCredentials,
  validateLoginCredentialsStrict,
  ValidationResult,
  isValidString,
  isValidUrl,
  isValidEmail
} from '../validation';
import { LoginCredentials, ValidationError } from '../../types';

describe('Validation Utils', () => {
  describe('基础验证函数', () => {
    describe('isValidString', () => {
      it('应该验证有效字符串', () => {
        expect(isValidString('hello')).toBe(true);
        expect(isValidString('hello', 3)).toBe(true);
        expect(isValidString('hello world', 5)).toBe(true);
      });

      it('应该拒绝无效字符串', () => {
        expect(isValidString('')).toBe(false);
        expect(isValidString('  ')).toBe(false);
        expect(isValidString('hi', 3)).toBe(false);
        expect(isValidString(null)).toBe(false);
        expect(isValidString(undefined)).toBe(false);
        expect(isValidString(123)).toBe(false);
      });
    });

    describe('isValidUrl', () => {
      it('应该验证有效URL', () => {
        expect(isValidUrl('https://example.com')).toBe(true);
        expect(isValidUrl('http://localhost:3000')).toBe(true);
        expect(isValidUrl('ftp://files.example.com')).toBe(true);
      });

      it('应该拒绝无效URL', () => {
        expect(isValidUrl('not-a-url')).toBe(false);
        expect(isValidUrl('http://')).toBe(false);
        expect(isValidUrl('')).toBe(false);
        expect(isValidUrl(null)).toBe(false);
      });
    });

    describe('isValidEmail', () => {
      it('应该验证有效邮箱', () => {
        expect(isValidEmail('<EMAIL>')).toBe(true);
        expect(isValidEmail('<EMAIL>')).toBe(true);
      });

      it('应该拒绝无效邮箱', () => {
        expect(isValidEmail('invalid-email')).toBe(false);
        expect(isValidEmail('user@')).toBe(false);
        expect(isValidEmail('@domain.com')).toBe(false);
        expect(isValidEmail('')).toBe(false);
      });
    });
  });

  describe('登录凭据验证', () => {
    describe('validateLoginCredentials', () => {
      it('应该验证有效的登录凭据', () => {
        const credentials = {
          username: 'testuser',
          password: 'password123'
        };

        const result = validateLoginCredentials(credentials);

        expect(result.isValid).toBe(true);
        expect(result.data).toEqual({
          username: 'testuser',
          password: 'password123'
        });
        expect(result.errors).toHaveLength(0);
      });

      it('应该验证带验证码的登录凭据', () => {
        const credentials = {
          username: 'testuser',
          password: 'password123',
          captcha: '1234',
          captchaToken: 'token123'
        };

        const result = validateLoginCredentials(credentials);

        expect(result.isValid).toBe(true);
        expect(result.data).toEqual({
          username: 'testuser',
          password: 'password123',
          captcha: '1234',
          captchaToken: 'token123'
        });
      });

      it('应该处理用户名过短', () => {
        const credentials = {
          username: 'ab',
          password: 'password123'
        };

        const result = validateLoginCredentials(credentials);

        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('用户名长度至少为3个字符');
      });

      it('应该处理密码过短', () => {
        const credentials = {
          username: 'testuser',
          password: '123'
        };

        const result = validateLoginCredentials(credentials);

        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('密码长度至少为6个字符');
      });

      it('应该处理空验证码', () => {
        const credentials = {
          username: 'testuser',
          password: 'password123',
          captcha: ''
        };

        const result = validateLoginCredentials(credentials);

        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('验证码不能为空');
      });

      it('应该处理多个验证错误', () => {
        const credentials = {
          username: 'ab',
          password: '123'
        };

        const result = validateLoginCredentials(credentials);

        expect(result.isValid).toBe(false);
        expect(result.errors).toHaveLength(2);
        expect(result.errors).toContain('用户名长度至少为3个字符');
        expect(result.errors).toContain('密码长度至少为6个字符');
      });

      it('应该处理无效输入', () => {
        const result = validateLoginCredentials(null);

        expect(result.isValid).toBe(false);
        expect(result.errors).toContain('登录凭据必须是一个对象');
      });

      it('应该修剪空白字符', () => {
        const credentials = {
          username: '  testuser  ',
          password: '  password123  ',
          captcha: '  1234  '
        };

        const result = validateLoginCredentials(credentials);

        expect(result.isValid).toBe(true);
        expect(result.data?.username).toBe('testuser');
        expect(result.data?.password).toBe('password123');
        expect(result.data?.captcha).toBe('1234');
      });
    });

    describe('validateLoginCredentialsStrict', () => {
      it('应该验证有效的登录凭据', () => {
        const credentials = {
          username: 'testuser',
          password: 'password123'
        };

        const result = validateLoginCredentialsStrict(credentials);

        expect(result).toEqual({
          username: 'testuser',
          password: 'password123'
        });
      });

      it('应该抛出验证错误', () => {
        const credentials = {
          username: 'ab',
          password: 'password123'
        };

        expect(() => validateLoginCredentialsStrict(credentials))
          .toThrow(ValidationError);
        expect(() => validateLoginCredentialsStrict(credentials))
          .toThrow('用户名长度至少为3个字符');
      });

      it('应该处理无效输入', () => {
        expect(() => validateLoginCredentialsStrict(null))
          .toThrow(ValidationError);
        expect(() => validateLoginCredentialsStrict(null))
          .toThrow('登录凭据必须是一个对象');
      });
    });
  });
});