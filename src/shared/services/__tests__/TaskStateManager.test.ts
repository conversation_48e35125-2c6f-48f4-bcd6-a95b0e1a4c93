import { TaskStateManager, StateManagerConfig } from '../TaskStateManager';
import { TaskStatus } from '../../types';

describe('TaskStateManager', () => {
  let stateManager: TaskStateManager;
  let config: Partial<StateManagerConfig>;

  beforeEach(() => {
    config = {
      enableHistory: true,
      maxHistorySize: 100,
      persistHistory: false,
      validateTransitions: true
    };
    stateManager = new TaskStateManager(config);
  });

  afterEach(() => {
    stateManager.destroy();
  });

  describe('initializeTask', () => {
    it('should initialize task with default pending status', () => {
      const taskId = 'task1';
      stateManager.initializeTask(taskId);
      
      const status = stateManager.getTaskStatus(taskId);
      expect(status).toBe('pending');
    });

    it('should initialize task with specified status', () => {
      const taskId = 'task1';
      stateManager.initializeTask(taskId, 'downloading');
      
      const status = stateManager.getTaskStatus(taskId);
      expect(status).toBe('downloading');
    });

    it('should add initial state to history', () => {
      const taskId = 'task1';
      stateManager.initializeTask(taskId);
      
      const history = stateManager.getTaskHistory(taskId);
      expect(history).toHaveLength(1);
      expect(history[0].taskId).toBe(taskId);
      expect(history[0].fromStatus).toBe('pending');
      expect(history[0].toStatus).toBe('pending');
    });
  });

  describe('transitionState', () => {
    it('should transition task state when valid', () => {
      const taskId = 'task1';
      stateManager.initializeTask(taskId, 'pending');
      
      const result = stateManager.transitionState(taskId, 'downloading', '开始下载');
      
      expect(result).toBe(true);
      expect(stateManager.getTaskStatus(taskId)).toBe('downloading');
    });

    it('should not transition to invalid state', () => {
      const taskId = 'task1';
      stateManager.initializeTask(taskId, 'pending');
      
      // 从 pending 直接到 completed 应该是无效的
      const result = stateManager.transitionState(taskId, 'completed', '直接完成');
      
      expect(result).toBe(false);
      expect(stateManager.getTaskStatus(taskId)).toBe('pending');
    });

    it('should throw error for non-initialized task', () => {
      expect(() => {
        stateManager.transitionState('nonexistent', 'downloading');
      }).toThrow('任务 nonexistent 未初始化');
    });

    it('should return true without changes if target state is same as current', () => {
      const taskId = 'task1';
      stateManager.initializeTask(taskId, 'pending');
      
      const result = stateManager.transitionState(taskId, 'pending');
      
      expect(result).toBe(true);
      // 不应该添加新的历史记录
      expect(stateManager.getTaskHistory(taskId)).toHaveLength(1);
    });

    it('should emit state-changed event', (done) => {
      const taskId = 'task1';
      stateManager.initializeTask(taskId, 'pending');
      
      stateManager.on('state-changed', (id, fromStatus, toStatus, reason) => {
        expect(id).toBe(taskId);
        expect(fromStatus).toBe('pending');
        expect(toStatus).toBe('downloading');
        expect(reason).toBe('开始下载');
        done();
      });

      stateManager.transitionState(taskId, 'downloading', '开始下载');
    });

    it('should emit invalid-transition event', (done) => {
      const taskId = 'task1';
      stateManager.initializeTask(taskId, 'pending');
      
      stateManager.on('invalid-transition', (id, fromStatus, toStatus) => {
        expect(id).toBe(taskId);
        expect(fromStatus).toBe('pending');
        expect(toStatus).toBe('completed');
        done();
      });

      stateManager.transitionState(taskId, 'completed');
    });

    it('should add transition to history with metadata', () => {
      const taskId = 'task1';
      stateManager.initializeTask(taskId, 'pending');
      
      const metadata = { retryCount: 2, error: 'Connection timeout' };
      stateManager.transitionState(taskId, 'downloading', '开始下载', metadata);
      
      const history = stateManager.getTaskHistory(taskId);
      expect(history).toHaveLength(2);
      expect(history[1].metadata).toEqual(metadata);
    });
  });

  describe('task state queries', () => {
    beforeEach(() => {
      stateManager.initializeTask('task1', 'pending');
      stateManager.initializeTask('task2', 'downloading');
      stateManager.initializeTask('task3', 'completed');
      stateManager.initializeTask('task4', 'failed');
      stateManager.initializeTask('task5', 'downloading');
    });

    it('should get task status', () => {
      expect(stateManager.getTaskStatus('task1')).toBe('pending');
      expect(stateManager.getTaskStatus('task2')).toBe('downloading');
      expect(stateManager.getTaskStatus('nonexistent')).toBeUndefined();
    });

    it('should get all task states', () => {
      const allStates = stateManager.getAllTaskStates();
      
      expect(allStates.size).toBe(5);
      expect(allStates.get('task1')).toBe('pending');
      expect(allStates.get('task2')).toBe('downloading');
    });

    it('should get tasks by status', () => {
      const downloadingTasks = stateManager.getTasksByStatus('downloading');
      
      expect(downloadingTasks).toHaveLength(2);
      expect(downloadingTasks).toContain('task2');
      expect(downloadingTasks).toContain('task5');
    });

    it('should get status statistics', () => {
      const stats = stateManager.getStatusStats();
      
      expect(stats.pending).toBe(1);
      expect(stats.downloading).toBe(2);
      expect(stats.completed).toBe(1);
      expect(stats.failed).toBe(1);
      expect(stats.paused).toBe(0);
    });
  });

  describe('history management', () => {
    it('should get task history', () => {
      const taskId = 'task1';
      stateManager.initializeTask(taskId, 'pending');
      stateManager.transitionState(taskId, 'downloading', '开始下载');
      stateManager.transitionState(taskId, 'paused', '用户暂停');
      
      const history = stateManager.getTaskHistory(taskId);
      
      expect(history).toHaveLength(3);
      expect(history[0].toStatus).toBe('pending');
      expect(history[1].toStatus).toBe('downloading');
      expect(history[2].toStatus).toBe('paused');
    });

    it('should get all history', () => {
      stateManager.initializeTask('task1', 'pending');
      stateManager.initializeTask('task2', 'pending');
      stateManager.transitionState('task1', 'downloading');
      
      const allHistory = stateManager.getAllHistory();
      
      expect(allHistory).toHaveLength(3);
    });

    it('should clear history', () => {
      stateManager.initializeTask('task1', 'pending');
      stateManager.transitionState('task1', 'downloading');
      
      stateManager.clearHistory();
      
      expect(stateManager.getAllHistory()).toHaveLength(0);
    });

    it('should limit history size', () => {
      // 创建一个小容量的状态管理器
      const smallManager = new TaskStateManager({
        maxHistorySize: 3,
        enableHistory: true
      });
      
      smallManager.initializeTask('task1', 'pending');
      smallManager.transitionState('task1', 'downloading', '1');
      smallManager.transitionState('task1', 'paused', '2');
      smallManager.transitionState('task1', 'downloading', '3');
      smallManager.transitionState('task1', 'completed', '4');
      
      const history = smallManager.getTaskHistory('task1');
      
      expect(history).toHaveLength(3); // 只保留最新的3条
      expect(history[0].reason).toBe('3'); // 最早的记录应该是第3条
      expect(history[2].reason).toBe('4'); // 最新的记录应该是第4条
      
      smallManager.destroy();
    });
  });

  describe('transition rules', () => {
    it('should validate transitions based on rules', () => {
      expect(stateManager.isValidTransition('pending', 'downloading')).toBe(true);
      expect(stateManager.isValidTransition('pending', 'completed')).toBe(false);
    });

    it('should get possible transitions', () => {
      const pendingTransitions = stateManager.getPossibleTransitions('pending');
      
      expect(pendingTransitions).toContain('downloading');
      expect(pendingTransitions).toContain('cancelled');
      expect(pendingTransitions).toContain('failed');
      expect(pendingTransitions).not.toContain('completed');
    });

    it('should add custom transition rule', () => {
      stateManager.addTransitionRule({
        from: 'pending',
        to: 'completed',
        condition: () => true
      });
      
      expect(stateManager.isValidTransition('pending', 'completed')).toBe(true);
      
      // 现在可以直接从 pending 到 completed
      const taskId = 'task1';
      stateManager.initializeTask(taskId, 'pending');
      const result = stateManager.transitionState(taskId, 'completed', '直接完成');
      
      expect(result).toBe(true);
      expect(stateManager.getTaskStatus(taskId)).toBe('completed');
    });

    it('should remove transition rule', () => {
      // 移除从 pending 到 downloading 的规则
      const removed = stateManager.removeTransitionRule('pending', 'downloading');
      
      expect(removed).toBe(true);
      expect(stateManager.isValidTransition('pending', 'downloading')).toBe(false);
    });
  });

  describe('task management', () => {
    it('should remove task', () => {
      const taskId = 'task1';
      stateManager.initializeTask(taskId);
      
      stateManager.removeTask(taskId);
      
      expect(stateManager.getTaskStatus(taskId)).toBeUndefined();
      expect(stateManager.getTaskHistory(taskId)).toHaveLength(0);
    });
  });

  describe('configuration', () => {
    it('should update configuration', () => {
      const newConfig: Partial<StateManagerConfig> = {
        enableHistory: false,
        validateTransitions: false
      };
      
      stateManager.updateConfig(newConfig);
      const currentConfig = stateManager.getConfig();
      
      expect(currentConfig.enableHistory).toBe(false);
      expect(currentConfig.validateTransitions).toBe(false);
    });

    it('should clear history when disabling history', () => {
      const taskId = 'task1';
      stateManager.initializeTask(taskId);
      stateManager.transitionState(taskId, 'downloading');
      
      stateManager.updateConfig({ enableHistory: false });
      
      expect(stateManager.getAllHistory()).toHaveLength(0);
    });
  });
});
