import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ResourceList } from '../ResourceList';
import { CourseResource } from '../../../shared/types';

// Mock antd components that might cause issues in tests
jest.mock('antd', () => {
  const antd = jest.requireActual('antd');
  return {
    ...antd,
    message: {
      success: jest.fn(),
      error: jest.fn(),
      warning: jest.fn(),
      info: jest.fn()
    }
  };
});

describe('ResourceList', () => {
  const mockResources: CourseResource[] = [
    {
      id: 'resource1',
      title: '小学语文教材',
      type: 'textbook',
      url: 'https://example.com/textbook1',
      metadata: {
        stage: 'primary',
        grade: 'grade1',
        subject: 'chinese',
        version: 'renjiao',
        volume: 'volume1',
        fileSize: 1024000
      },
      requiresAuth: false,
      accessLevel: 'public'
    },
    {
      id: 'resource2',
      title: '小学语文视频课程',
      type: 'video',
      url: 'https://example.com/video1',
      metadata: {
        stage: 'primary',
        grade: 'grade1',
        subject: 'chinese',
        version: 'renjiao',
        volume: 'volume1',
        duration: 1800
      },
      requiresAuth: true,
      accessLevel: 'registered'
    },
    {
      id: 'resource3',
      title: '高级数学课程',
      type: 'video',
      url: 'https://example.com/video2',
      metadata: {
        stage: 'high',
        grade: 'grade12',
        subject: 'math',
        version: 'advanced',
        volume: 'volume2',
        duration: 3600,
        fileSize: 2048000
      },
      requiresAuth: true,
      accessLevel: 'premium'
    }
  ];

  const defaultProps = {
    resources: mockResources,
    onDownload: jest.fn(),
    onBatchDownload: jest.fn(),
    userLoggedIn: false,
    loading: false,
    selectedResources: [],
    onSelectionChange: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('rendering', () => {
    it('should render resource list with resources', () => {
      render(<ResourceList {...defaultProps} />);
      
      expect(screen.getByText('资源列表')).toBeInTheDocument();
      expect(screen.getByText('小学语文教材')).toBeInTheDocument();
      expect(screen.getByText('小学语文视频课程')).toBeInTheDocument();
      expect(screen.getByText('高级数学课程')).toBeInTheDocument();
    });

    it('should show loading state', () => {
      render(<ResourceList {...defaultProps} loading={true} />);
      
      expect(screen.getByLabelText('loading')).toBeInTheDocument();
    });

    it('should show empty state when no resources', () => {
      render(<ResourceList {...defaultProps} resources={[]} />);
      
      expect(screen.getByText('暂无资源')).toBeInTheDocument();
    });

    it('should display resource statistics', () => {
      render(<ResourceList {...defaultProps} />);
      
      expect(screen.getByText(/共 3 个资源/)).toBeInTheDocument();
      expect(screen.getByText(/教材 1 个，视频 2 个/)).toBeInTheDocument();
    });
  });

  describe('view mode switching', () => {
    it('should switch between card and list view modes', () => {
      render(<ResourceList {...defaultProps} />);
      
      const viewModeSwitch = screen.getByRole('switch');
      
      // 默认应该是卡片模式
      expect(viewModeSwitch).toBeChecked();
      
      // 切换到列表模式
      fireEvent.click(viewModeSwitch);
      expect(viewModeSwitch).not.toBeChecked();
    });
  });

  describe('resource selection', () => {
    it('should handle individual resource selection', () => {
      const onSelectionChange = jest.fn();
      render(<ResourceList {...defaultProps} onSelectionChange={onSelectionChange} />);
      
      const checkboxes = screen.getAllByRole('checkbox');
      const firstResourceCheckbox = checkboxes[1]; // 第一个是全选checkbox
      
      fireEvent.click(firstResourceCheckbox);
      
      expect(onSelectionChange).toHaveBeenCalledWith(['resource1']);
    });

    it('should handle select all functionality', () => {
      const onSelectionChange = jest.fn();
      render(<ResourceList {...defaultProps} onSelectionChange={onSelectionChange} />);
      
      const selectAllCheckbox = screen.getByRole('checkbox', { name: '全选' });
      
      fireEvent.click(selectAllCheckbox);
      
      expect(onSelectionChange).toHaveBeenCalledWith(['resource1', 'resource2', 'resource3']);
    });

    it('should show indeterminate state for partial selection', () => {
      render(<ResourceList {...defaultProps} selectedResources={['resource1']} />);
      
      const selectAllCheckbox = screen.getByRole('checkbox', { name: '全选' });
      
      expect(selectAllCheckbox).toHaveProperty('indeterminate', true);
    });
  });

  describe('download functionality', () => {
    it('should handle individual resource download', () => {
      const onDownload = jest.fn();
      render(<ResourceList {...defaultProps} onDownload={onDownload} />);
      
      const downloadButtons = screen.getAllByText('下载');
      fireEvent.click(downloadButtons[0]);
      
      expect(onDownload).toHaveBeenCalledWith(mockResources[0]);
    });

    it('should handle batch download', () => {
      const onBatchDownload = jest.fn();
      render(<ResourceList 
        {...defaultProps} 
        onBatchDownload={onBatchDownload}
        selectedResources={['resource1', 'resource2']}
      />);
      
      const batchDownloadButton = screen.getByText(/批量下载 \(2\)/);
      fireEvent.click(batchDownloadButton);
      
      expect(onBatchDownload).toHaveBeenCalledWith([mockResources[0], mockResources[1]]);
    });

    it('should disable batch download when no resources selected', () => {
      render(<ResourceList {...defaultProps} />);
      
      const batchDownloadButton = screen.getByText(/批量下载 \(0\)/);
      expect(batchDownloadButton).toBeDisabled();
    });
  });

  describe('access control', () => {
    it('should show access level tags', () => {
      render(<ResourceList {...defaultProps} />);
      
      expect(screen.getByText('公开')).toBeInTheDocument();
      expect(screen.getByText('需登录')).toBeInTheDocument();
      expect(screen.getByText('高级')).toBeInTheDocument();
    });

    it('should disable download for restricted resources when user not logged in', () => {
      render(<ResourceList {...defaultProps} userLoggedIn={false} />);
      
      const downloadButtons = screen.getAllByText('下载');
      
      // 第一个资源是公开的，应该可以下载
      expect(downloadButtons[0]).not.toBeDisabled();
      
      // 第二个资源需要登录，应该被禁用
      expect(downloadButtons[1]).toBeDisabled();
      
      // 第三个资源是高级的，应该被禁用
      expect(downloadButtons[2]).toBeDisabled();
    });

    it('should enable download for registered resources when user logged in', () => {
      render(<ResourceList {...defaultProps} userLoggedIn={true} />);
      
      const downloadButtons = screen.getAllByText('下载');
      
      // 前两个资源应该可以下载
      expect(downloadButtons[0]).not.toBeDisabled();
      expect(downloadButtons[1]).not.toBeDisabled();
      
      // 高级资源仍然应该被禁用
      expect(downloadButtons[2]).toBeDisabled();
    });

    it('should show login prompt for restricted resources', () => {
      render(<ResourceList {...defaultProps} userLoggedIn={false} />);
      
      expect(screen.getByText('部分资源需要登录后才能下载')).toBeInTheDocument();
    });
  });

  describe('resource metadata display', () => {
    it('should display resource metadata tags', () => {
      render(<ResourceList {...defaultProps} />);
      
      // 检查第一个资源的元数据标签
      expect(screen.getByText('primary')).toBeInTheDocument();
      expect(screen.getByText('grade1')).toBeInTheDocument();
      expect(screen.getByText('chinese')).toBeInTheDocument();
      expect(screen.getByText('renjiao')).toBeInTheDocument();
      expect(screen.getByText('volume1')).toBeInTheDocument();
    });

    it('should display file size for textbooks', () => {
      render(<ResourceList {...defaultProps} />);
      
      expect(screen.getByText(/大小: 1000.0 KB/)).toBeInTheDocument();
    });

    it('should display duration for videos', () => {
      render(<ResourceList {...defaultProps} />);
      
      expect(screen.getByText(/时长: 30:00/)).toBeInTheDocument();
      expect(screen.getByText(/时长: 60:00/)).toBeInTheDocument();
    });
  });

  describe('resource icons', () => {
    it('should display correct icons for different resource types', () => {
      render(<ResourceList {...defaultProps} />);
      
      // 检查是否有书本和播放图标
      const bookIcons = document.querySelectorAll('[data-icon="book"]');
      const playIcons = document.querySelectorAll('[data-icon="play-circle"]');
      
      expect(bookIcons.length).toBeGreaterThan(0);
      expect(playIcons.length).toBeGreaterThan(0);
    });
  });

  describe('accessibility', () => {
    it('should have proper ARIA labels and roles', () => {
      render(<ResourceList {...defaultProps} />);
      
      // 检查复选框的可访问性
      const checkboxes = screen.getAllByRole('checkbox');
      expect(checkboxes.length).toBeGreaterThan(0);
      
      // 检查按钮的可访问性
      const buttons = screen.getAllByRole('button');
      expect(buttons.length).toBeGreaterThan(0);
    });

    it('should provide tooltips for disabled download buttons', async () => {
      render(<ResourceList {...defaultProps} userLoggedIn={false} />);
      
      const downloadButtons = screen.getAllByText('下载');
      const disabledButton = downloadButtons[1]; // 需要登录的资源
      
      fireEvent.mouseEnter(disabledButton);
      
      await waitFor(() => {
        expect(screen.getByText('请先登录以下载此资源')).toBeInTheDocument();
      });
    });
  });

  describe('error handling', () => {
    it('should handle missing metadata gracefully', () => {
      const resourcesWithMissingData: CourseResource[] = [
        {
          id: 'resource1',
          title: '测试资源',
          type: 'textbook',
          url: 'https://example.com/test',
          metadata: {
            stage: 'primary',
            grade: 'grade1',
            subject: 'chinese',
            version: 'renjiao',
            volume: 'volume1'
            // 缺少 fileSize 和 duration
          },
          requiresAuth: false,
          accessLevel: 'public'
        }
      ];
      
      render(<ResourceList {...defaultProps} resources={resourcesWithMissingData} />);
      
      expect(screen.getByText('测试资源')).toBeInTheDocument();
      expect(screen.getByText(/大小: 未知/)).toBeInTheDocument();
    });
  });
});