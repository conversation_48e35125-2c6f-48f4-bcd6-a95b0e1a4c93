import React, { useState } from 'react';
import { Button, Input, Card, message, Space, Typography } from 'antd';

const { Title, Text } = Typography;

/**
 * 简化的测试应用
 * 专注于测试核心下载功能
 */
export const SimpleTestApp: React.FC = () => {
  const [testUrl, setTestUrl] = useState('');
  const [isDownloading, setIsDownloading] = useState(false);
  const [downloadResult, setDownloadResult] = useState<string>('');

  /**
   * 测试下载功能
   */
  const handleTestDownload = async () => {
    if (!testUrl.trim()) {
      message.error('请输入测试URL');
      return;
    }

    setIsDownloading(true);
    setDownloadResult('');

    try {
      console.log('🧪 开始测试下载:', testUrl);
      
      // 调用主进程的下载功能
      const electronAPI = (window as any).electronAPI;
      if (!electronAPI?.startDownload) {
        throw new Error('Electron API不可用');
      }

      const result = await electronAPI.startDownload(testUrl, {
        downloadPath: './downloads/test',
        filename: `test_${Date.now()}`
      });

      console.log('✅ 下载测试结果:', result);
      setDownloadResult(`下载任务已启动，任务ID: ${result}`);
      message.success('下载任务已启动');

    } catch (error: any) {
      console.error('❌ 下载测试失败:', error);
      setDownloadResult(`下载失败: ${error?.message || error}`);
      message.error('下载测试失败');
    } finally {
      setIsDownloading(false);
    }
  };

  /**
   * 测试API连接
   */
  const handleTestAPI = async () => {
    try {
      console.log('🧪 开始测试API连接');
      
      const electronAPI = (window as any).electronAPI;
      if (!electronAPI?.apiRequest) {
        throw new Error('Electron API不可用');
      }

      // 测试教材API
      const testApiUrl = 'https://s-file-1.ykt.cbern.com.cn/zxx/ndrs/national_lesson/teachingmaterials/part_100.json';
      const result = await electronAPI.apiRequest(testApiUrl);

      console.log('✅ API测试结果:', result);
      
      if (result.success) {
        message.success(`API连接成功，获取到 ${result.data?.length || 0} 条数据`);
        setDownloadResult(`API测试成功: 获取到 ${result.data?.length || 0} 条教材数据`);
      } else {
        message.error('API连接失败');
        setDownloadResult(`API测试失败: ${result.error?.message || '未知错误'}`);
      }

    } catch (error: any) {
      console.error('❌ API测试失败:', error);
      message.error('API测试失败');
      setDownloadResult(`API测试失败: ${error?.message || error}`);
    }
  };

  return (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>智慧教育下载器 - 功能测试</Title>
      
      <Card title="下载功能测试" style={{ marginBottom: '20px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text>输入要测试的资源URL：</Text>
          <Input
            placeholder="例如：https://example.com/resource.pdf"
            value={testUrl}
            onChange={(e) => setTestUrl(e.target.value)}
            style={{ marginBottom: '10px' }}
          />
          
          <Space>
            <Button 
              type="primary" 
              onClick={handleTestDownload}
              loading={isDownloading}
            >
              测试下载
            </Button>
            
            <Button onClick={handleTestAPI}>
              测试API连接
            </Button>
          </Space>
        </Space>
      </Card>

      <Card title="测试预设URL" style={{ marginBottom: '20px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text>快速测试常见资源类型：</Text>
          
          <Space wrap>
            <Button 
              onClick={() => setTestUrl('https://s-file-1.ykt.cbern.com.cn/zxx/ndrs/national_lesson/teachingmaterials/part_100.json')}
            >
              教材API
            </Button>
            
            <Button 
              onClick={() => setTestUrl('https://example.com/test.pdf')}
            >
              PDF文档
            </Button>
            
            <Button 
              onClick={() => setTestUrl('https://example.com/test.m3u8')}
            >
              M3U8视频
            </Button>
            
            <Button 
              onClick={() => setTestUrl('https://example.com/test.pptx')}
            >
              SuperBoard课件
            </Button>
          </Space>
        </Space>
      </Card>

      {downloadResult && (
        <Card title="测试结果">
          <Text code style={{ whiteSpace: 'pre-wrap' }}>
            {downloadResult}
          </Text>
        </Card>
      )}

      <Card title="功能说明" style={{ marginTop: '20px' }}>
        <Space direction="vertical">
          <Text>✅ 主进程下载功能已修复</Text>
          <Text>✅ API认证头已更新</Text>
          <Text>✅ M3U8视频下载支持</Text>
          <Text>✅ SuperBoard课件下载支持</Text>
          <Text>✅ 多重认证方法回退</Text>
          <Text>⚠️ UI界面功能待完善</Text>
        </Space>
      </Card>
    </div>
  );
};

export default SimpleTestApp;
