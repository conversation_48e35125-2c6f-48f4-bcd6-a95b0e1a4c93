#!/usr/bin/env node

const { execSync, spawn } = require('child_process');
const fs = require('fs-extra');
const path = require('path');
const os = require('os');

/**
 * 安装测试脚本
 * 用于测试不同平台的安装包功能
 */
class InstallTester {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.releaseDir = path.join(this.projectRoot, 'release');
    this.platform = os.platform();
    this.testResults = [];
  }

  /**
   * 运行安装测试
   */
  async runInstallTests() {
    console.log('🧪 开始安装测试...\n');
    console.log(`当前平台: ${this.platform}`);

    try {
      await this.testInstallPackageExists();
      await this.testInstallPackageStructure();
      await this.testApplicationLaunch();
      
      this.printResults();
    } catch (error) {
      console.error('❌ 安装测试失败:', error.message);
      process.exit(1);
    }
  }

  /**
   * 测试安装包是否存在
   */
  async testInstallPackageExists() {
    console.log('📦 检查安装包...');

    if (!(await fs.pathExists(this.releaseDir))) {
      this.addResult('❌', '发布目录不存在，请先运行构建');
      return;
    }

    const releaseContents = await fs.readdir(this.releaseDir);
    
    // 根据平台查找对应的安装包
    let expectedPackages = [];
    switch (this.platform) {
      case 'darwin': // macOS
        expectedPackages = ['.dmg', '.zip', '-mac.zip'];
        break;
      case 'win32': // Windows
        expectedPackages = ['.exe', '-setup.exe', '.msi'];
        break;
      case 'linux':
        expectedPackages = ['.AppImage', '.deb', '.rpm'];
        break;
    }

    let foundPackages = [];
    for (const item of releaseContents) {
      const itemPath = path.join(this.releaseDir, item);
      const stats = await fs.stat(itemPath);
      
      if (stats.isFile()) {
        for (const ext of expectedPackages) {
          if (item.includes(ext)) {
            foundPackages.push(item);
            const size = this.formatBytes(stats.size);
            this.addResult('✅', `找到安装包: ${item} (${size})`);
          }
        }
      }
    }

    if (foundPackages.length === 0) {
      this.addResult('❌', `未找到 ${this.platform} 平台的安装包`);
      this.addResult('ℹ️', `期望的包格式: ${expectedPackages.join(', ')}`);
      this.addResult('ℹ️', `实际文件: ${releaseContents.join(', ')}`);
    }
  }

  /**
   * 测试安装包结构
   */
  async testInstallPackageStructure() {
    console.log('🔍 检查安装包结构...');

    // 查找未打包的应用目录（electron-builder --dir 的输出）
    const releaseContents = await fs.readdir(this.releaseDir);
    let appDir = null;

    for (const item of releaseContents) {
      const itemPath = path.join(this.releaseDir, item);
      const stats = await fs.stat(itemPath);
      
      if (stats.isDirectory()) {
        // 查找包含应用的目录
        const possibleAppNames = [
          '智慧教育下载器',
          'smart-edu-downloader',
          'Smart Edu Downloader'
        ];
        
        for (const appName of possibleAppNames) {
          if (item.includes(appName) || item.toLowerCase().includes('unpacked')) {
            appDir = itemPath;
            break;
          }
        }
        
        if (appDir) break;
      }
    }

    if (appDir) {
      this.addResult('✅', `找到应用目录: ${path.basename(appDir)}`);
      await this.checkAppDirectoryStructure(appDir);
    } else {
      this.addResult('❌', '未找到应用目录');
    }
  }

  /**
   * 检查应用目录结构
   */
  async checkAppDirectoryStructure(appDir) {
    const requiredFiles = [];
    const requiredDirs = ['resources'];

    // 根据平台设置不同的期望文件
    switch (this.platform) {
      case 'darwin':
        requiredFiles.push('智慧教育下载器.app');
        break;
      case 'win32':
        requiredFiles.push('智慧教育下载器.exe');
        break;
      case 'linux':
        requiredFiles.push('智慧教育下载器');
        break;
    }

    // 检查文件
    for (const file of requiredFiles) {
      const filePath = path.join(appDir, file);
      if (await fs.pathExists(filePath)) {
        this.addResult('✅', `应用文件存在: ${file}`);
      } else {
        this.addResult('❌', `应用文件缺失: ${file}`);
      }
    }

    // 检查目录
    for (const dir of requiredDirs) {
      const dirPath = path.join(appDir, dir);
      if (await fs.pathExists(dirPath)) {
        this.addResult('✅', `应用目录存在: ${dir}`);
      } else {
        this.addResult('⚠️', `应用目录缺失: ${dir}`);
      }
    }

    // 检查资源文件
    const resourcesDir = path.join(appDir, 'resources');
    if (await fs.pathExists(resourcesDir)) {
      const resourceContents = await fs.readdir(resourcesDir);
      this.addResult('ℹ️', `资源目录包含: ${resourceContents.join(', ')}`);
    }
  }

  /**
   * 测试应用启动
   */
  async testApplicationLaunch() {
    console.log('🚀 测试应用启动...');

    // 这里只做基本的可执行文件检查
    // 实际的启动测试需要在真实环境中进行
    
    const releaseContents = await fs.readdir(this.releaseDir);
    let executableFound = false;

    for (const item of releaseContents) {
      const itemPath = path.join(this.releaseDir, item);
      const stats = await fs.stat(itemPath);
      
      if (stats.isDirectory()) {
        const executablePath = await this.findExecutable(itemPath);
        if (executablePath) {
          executableFound = true;
          this.addResult('✅', `找到可执行文件: ${path.relative(this.releaseDir, executablePath)}`);
          
          // 检查文件权限（仅 Unix 系统）
          if (this.platform !== 'win32') {
            try {
              await fs.access(executablePath, fs.constants.X_OK);
              this.addResult('✅', '可执行文件具有执行权限');
            } catch (error) {
              this.addResult('❌', '可执行文件缺少执行权限');
            }
          }
        }
      }
    }

    if (!executableFound) {
      this.addResult('❌', '未找到可执行文件');
    }

    // 提供手动测试说明
    this.addResult('ℹ️', '请手动测试应用启动和基本功能');
    this.addResult('ℹ️', '检查项目：');
    this.addResult('ℹ️', '  - 应用能否正常启动');
    this.addResult('ℹ️', '  - 界面是否正确显示');
    this.addResult('ℹ️', '  - 基本功能是否可用');
    this.addResult('ℹ️', '  - 自动更新功能是否工作');
  }

  /**
   * 查找可执行文件
   */
  async findExecutable(dir) {
    const contents = await fs.readdir(dir);
    
    for (const item of contents) {
      const itemPath = path.join(dir, item);
      const stats = await fs.stat(itemPath);
      
      if (stats.isFile()) {
        // 根据平台查找可执行文件
        switch (this.platform) {
          case 'darwin':
            if (item.endsWith('.app')) {
              return itemPath;
            }
            break;
          case 'win32':
            if (item.endsWith('.exe')) {
              return itemPath;
            }
            break;
          case 'linux':
            if (!item.includes('.') && stats.mode & parseInt('111', 8)) {
              return itemPath;
            }
            break;
        }
      } else if (stats.isDirectory()) {
        const result = await this.findExecutable(itemPath);
        if (result) return result;
      }
    }
    
    return null;
  }

  /**
   * 添加测试结果
   */
  addResult(status, message) {
    this.testResults.push({ status, message });
    console.log(`   ${status} ${message}`);
  }

  /**
   * 打印测试结果摘要
   */
  printResults() {
    console.log('\n📊 安装测试结果摘要:');
    console.log('='.repeat(50));

    const passed = this.testResults.filter(r => r.status === '✅').length;
    const failed = this.testResults.filter(r => r.status === '❌').length;
    const warnings = this.testResults.filter(r => r.status === '⚠️').length;
    const info = this.testResults.filter(r => r.status === 'ℹ️').length;

    console.log(`✅ 通过: ${passed}`);
    console.log(`❌ 失败: ${failed}`);
    console.log(`⚠️  警告: ${warnings}`);
    console.log(`ℹ️  信息: ${info}`);
    console.log(`📊 总计: ${this.testResults.length}`);

    if (failed > 0) {
      console.log('\n❌ 存在失败的测试项，请检查安装包！');
      process.exit(1);
    } else {
      console.log('\n🎉 安装测试通过！');
    }
  }

  /**
   * 格式化字节大小
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// 运行测试
if (require.main === module) {
  const tester = new InstallTester();
  tester.runInstallTests().catch(console.error);
}

module.exports = InstallTester;