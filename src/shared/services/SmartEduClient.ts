import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import {
  CourseFilters,
  FilterOptions,
  CourseResource,
  ResourceDetail,
  M3U8Playlist,
  CaptchaInfo,
  AuthResult,
  ApiResponse,
  NetworkError,
  ApiError,
  ParseError,
  RetryOptions,
  RetryState
} from '../types';
import { getSmartEduConfig, SmartEduConfig } from '../config/smartedu.config';
import { AuthService } from './AuthService';

/**
 * 智慧平台API客户端 - 修复版本
 * 提供与国家中小学智慧平台的API交互功能
 */
export class SmartEduClient {
  private axiosInstance: AxiosInstance;
  private userAgents: string[];
  private currentUserAgentIndex: number = 0;
  private requestCount: number = 0;
  private lastRequestTime: number = 0;
  private readonly minRequestInterval: number = 1000; // 最小请求间隔 1秒
  private readonly maxRequestsPerMinute: number = 30; // 每分钟最大请求数
  private requestTimes: number[] = [];

  private readonly defaultRetryOptions: RetryOptions = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 10000,
    backoffFactor: 2,
    retryableErrors: ['NETWORK_ERROR', 'API_ERROR'] as any[]
  };

  // 智慧教育平台配置
  private readonly config: SmartEduConfig;
  
  // 认证服务实例
  private readonly authService: AuthService;

  // 生成完整的API URL
  private get tagsAPI() { return `${this.config.api.baseURL}${this.config.api.endpoints.tags}`; }
  private get materialsAPI() { return `${this.config.api.baseURL}${this.config.api.endpoints.materials}`; }
  private get versionAPI() { return `${this.config.api.baseURL}${this.config.api.endpoints.version}`; }

  constructor(baseURL?: string) {
    // 获取配置
    this.config = getSmartEduConfig();

    // 允许覆盖默认的baseURL
    if (baseURL) {
      this.config.api.baseURL = baseURL;
    }

    // 初始化认证服务
    this.authService = AuthService.getInstance();
    
    // 初始化User-Agent列表
    this.userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:121.0) Gecko/20100101 Firefox/121.0',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
    ];

    // 创建axios实例
    this.axiosInstance = axios.create({
      baseURL: this.config.api.baseURL,
      timeout: this.config.auth.timeout,
      headers: this.config.auth.defaultHeaders
    });

    // 设置拦截器
    this.setupRequestInterceptor();
    this.setupResponseInterceptor();
  }

  /**
   * 设置请求拦截器
   */
  private setupRequestInterceptor(): void {
    this.axiosInstance.interceptors.request.use(
      (config) => {
        // 使用认证服务构建完整的请求头
        const fullUrl = config.url ? (config.url.startsWith('http') ? config.url : `${config.baseURL}${config.url}`) : '';
        const authHeaders = this.authService.buildRequestHeaders(fullUrl);
        
        // 合并认证头和现有头
        Object.assign(config.headers, authHeaders);
        
        // 轮换User-Agent（覆盖认证服务的默认值）
        config.headers['User-Agent'] = this.getNextUserAgent();
        
        return config;
      },
      (error) => {
        return Promise.reject(new NetworkError('请求配置失败', { originalError: error }));
      }
    );
  }

  /**
   * 设置响应拦截器
   */
  private setupResponseInterceptor(): void {
    this.axiosInstance.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        if (error.response) {
          const status = error.response.status;
          if (status === 403) {
            throw new ApiError('访问被拒绝，可能需要登录', { statusCode: status });
          } else if (status === 429) {
            throw new ApiError('请求过于频繁，请稍后重试', { statusCode: status });
          } else if (status >= 500) {
            throw new ApiError('服务器错误', { statusCode: status });
          }
        }
        throw new NetworkError('网络请求失败', { originalError: error });
      }
    );
  }

  /**
   * 获取下一个User-Agent
   */
  private getNextUserAgent(): string {
    const userAgent = this.userAgents[this.currentUserAgentIndex];
    this.currentUserAgentIndex = (this.currentUserAgentIndex + 1) % this.userAgents.length;
    return userAgent;
  }

  /**
   * 搜索资源
   */
  async searchResources(filters: CourseFilters): Promise<CourseResource[]> {
    try {
      console.log('🔍 开始搜索资源');
      console.log('🔍 筛选条件:', JSON.stringify(filters));

      // 第一步：查找教材ID
      const materialId = await this.findMaterialId(filters);
      if (!materialId) {
        console.log('❌ 无法找到匹配的教材，返回空结果');
        return [];
      }

      console.log(`✅ 使用教材ID: ${materialId}`);

      // 第二步：获取资源数据
      const resourcesData = await this.getResourcesForMaterial(materialId);
      
      // 第三步：转换为标准格式
      const resources = this.convertToStandardFormat(resourcesData, filters);
      
      console.log(`✅ 搜索完成，找到 ${resources.length} 个资源`);
      return resources;

    } catch (error) {
      console.error('❌ 搜索资源失败:', error);
      throw new ApiError('搜索资源失败', { originalError: error as Error });
    }
  }

  /**
   * 查找教材ID
   */
  private async findMaterialId(filters: CourseFilters): Promise<string | null> {
    try {
      const materials = await this.getMaterialsData();
      console.log(`🔍 开始在 ${materials.length} 个教材中查找匹配项`);

      // 简化的匹配逻辑
      for (const material of materials) {
        const tags = material.tag_list || [];
        const tagIds = tags.map((tag: any) => tag.tag_id);
        
        // 检查是否匹配筛选条件
        const matchesStage = !filters.stage || tagIds.includes(filters.stage);
        const matchesGrade = !filters.grade || tagIds.includes(filters.grade);
        const matchesSubject = !filters.subject || tagIds.includes(filters.subject);
        const matchesVersion = !filters.version || tagIds.includes(filters.version);
        const matchesVolume = !filters.volume || tagIds.includes(filters.volume);
        
        if (matchesStage && matchesGrade && matchesSubject && matchesVersion && matchesVolume) {
          console.log(`✅ 找到匹配教材: ${material.title} (ID: ${material.id})`);
          return material.id;
        }
      }

      return null;
    } catch (error) {
      console.error('❌ 查找教材ID失败:', error);
      return null;
    }
  }

  /**
   * 获取教材数据
   */
  private async getMaterialsData(): Promise<any[]> {
    try {
      console.log('🔍 开始获取教材数据...');
      
      const electronAPI = (window as any).electronAPI;
      if (!electronAPI?.apiRequest) {
        throw new Error('Electron API代理不可用，请确保应用在Electron环境中运行');
      }

      // 使用更新后的API端点
      const materialsUrl = `${this.config.api.baseURL}${this.config.api.endpoints.materials}`;
      console.log(`📡 请求教材API: ${materialsUrl}`);

      const response = await electronAPI.apiRequest(materialsUrl);
      if (!response.success) {
        console.error('❌ 获取教材数据失败:', response.error);
        throw new Error(`获取教材数据失败: ${response.error?.message || '未知错误'}`);
      }

      const materials = response.data || [];
      console.log(`✅ 成功获取 ${materials.length} 个教材`);
      return materials;
    } catch (error) {
      console.error('❌ 获取教材数据异常:', error);
      throw error;
    }
  }

  /**
   * 获取教材的资源数据
   */
  private async getResourcesForMaterial(materialId: string): Promise<any[]> {
    try {
      const electronAPI = (window as any).electronAPI;
      if (!electronAPI?.apiRequest) {
        throw new Error('Electron API代理不可用');
      }

      // 尝试多个API端点获取资源数据
      const apiEndpoints = [
        `${this.config.api.fileBaseURL}${this.config.api.endpoints.materialResources.replace('{materialId}', materialId)}`,
        `${this.config.api.fileBaseURL}${this.config.api.endpoints.materialDetails.replace('{materialId}', materialId)}`,
        `${this.config.api.baseURL}${this.config.api.endpoints.materialResources.replace('{materialId}', materialId)}`
      ];

      for (const url of apiEndpoints) {
        try {
          console.log(`🔄 尝试API端点: ${url}`);
          const response = await electronAPI.apiRequest(url);
          
          if (response.success && response.data) {
            let resourcesData = [];
            
            if (Array.isArray(response.data)) {
              resourcesData = response.data;
            } else {
              resourcesData = this.extractResourcesFromDetails(response.data);
            }
            
            if (resourcesData.length > 0) {
              console.log(`✅ 成功获取 ${resourcesData.length} 个资源`);
              return resourcesData;
            }
          }
        } catch (error) {
          console.log(`⚠️ API端点失败: ${url}`, error);
          continue;
        }
      }

      console.warn(`⚠️ 所有API端点都无法获取教材 ${materialId} 的资源`);
      return [];
    } catch (error) {
      console.error(`❌ 获取教材资源失败:`, error);
      return [];
    }
  }

  /**
   * 从教材详情数据中提取资源信息
   */
  private extractResourcesFromDetails(detailsData: any): any[] {
    try {
      const resources: any[] = [];
      
      // 检查不同可能的数据结构
      if (detailsData.resources && Array.isArray(detailsData.resources)) {
        resources.push(...detailsData.resources);
      }
      
      if (detailsData.items && Array.isArray(detailsData.items)) {
        resources.push(...detailsData.items);
      }
      
      if (detailsData.content && Array.isArray(detailsData.content)) {
        resources.push(...detailsData.content);
      }
      
      // 如果是单个资源对象，包装成数组
      if (detailsData.title && detailsData.url) {
        resources.push(detailsData);
      }
      
      console.log(`📋 从详情数据中提取到 ${resources.length} 个资源`);
      return resources;
    } catch (error) {
      console.warn('⚠️ 提取资源信息失败:', error);
      return [];
    }
  }

  /**
   * 转换为标准格式
   */
  private convertToStandardFormat(resourcesData: any[], filters: CourseFilters): CourseResource[] {
    const resources: CourseResource[] = [];
    
    for (const item of resourcesData) {
      try {
        const resource: CourseResource = {
          id: item.id || item.resource_id || `resource_${Date.now()}_${Math.random()}`,
          title: item.title || item.name || '未知资源',
          type: this.detectResourceType(item),
          url: item.url || item.download_url || item.file_url || '',
          description: item.description || item.summary || '',
          requiresAuth: item.requires_auth || false,
          accessLevel: item.access_level || 'public',
          metadata: {
            stage: '',
            grade: '',
            subject: '',
            version: '',
            volume: ''
          }
        };
        
        resources.push(resource);
      } catch (error) {
        console.warn('⚠️ 转换资源格式失败:', error, item);
      }
    }
    
    return resources;
  }

  /**
   * 检测资源类型
   */
  private detectResourceType(item: any): 'textbook' | 'video' | 'teachingmaterial' {
    const url = item.url || item.download_url || item.file_url || '';
    const type = item.type || '';

    if (url.includes('.mp4') || url.includes('.m3u8') || type.includes('video')) {
      return 'video';
    }
    if (url.includes('.pdf') || url.includes('.doc') || url.includes('.ppt') || type.includes('document') || type.includes('textbook')) {
      return 'textbook';
    }

    return 'teachingmaterial';
  }

  /**
   * 从URL获取格式
   */
  private getFormatFromUrl(url: string): string {
    if (!url) return 'unknown';

    const match = url.match(/\.([^.?]+)(?:\?|$)/);
    return match ? match[1].toLowerCase() : 'unknown';
  }

  /**
   * 获取学段选项
   */
  async getStages(): Promise<any[]> {
    try {
      console.log('🔍 获取学段选项');

      const electronAPI = (window as any).electronAPI;
      if (!electronAPI?.apiRequest) {
        console.warn('⚠️ Electron API代理不可用，使用默认选项');
        return this.getDefaultStages();
      }

      // 使用标签API获取学段信息
      const tagsUrl = `${this.config.api.baseURL}${this.config.api.endpoints.tags}`;
      console.log('📡 请求标签API:', tagsUrl);

      const response = await electronAPI.apiRequest(tagsUrl);
      console.log('📡 标签API响应:', response);

      if (response.success && response.data) {
        // 从标签数据中提取学段信息
        const stages = this.extractStagesFromTags(response.data);
        console.log(`✅ 从API获取到 ${stages.length} 个学段:`, stages);

        if (stages.length > 0) {
          return stages;
        }
      }

      console.warn('⚠️ API未返回有效学段数据，使用默认选项');
      return this.getDefaultStages();
    } catch (error) {
      console.error('❌ 获取学段失败:', error);
      console.warn('⚠️ 使用默认学段选项');
      return this.getDefaultStages();
    }
  }

  /**
   * 获取默认学段选项
   */
  private getDefaultStages(): any[] {
    return [
      { value: 'xiaoxue', label: '小学' },
      { value: 'chuzhong', label: '初中' },
      { value: 'gaozhong', label: '高中' }
    ];
  }

  /**
   * 根据学段获取默认年级选项
   */
  private getDefaultGradesByStage(stage: string): any[] {
    switch (stage) {
      case 'xiaoxue':
        return [
          { value: 'grade1', label: '一年级' },
          { value: 'grade2', label: '二年级' },
          { value: 'grade3', label: '三年级' },
          { value: 'grade4', label: '四年级' },
          { value: 'grade5', label: '五年级' },
          { value: 'grade6', label: '六年级' }
        ];
      case 'chuzhong':
        return [
          { value: 'grade7', label: '七年级' },
          { value: 'grade8', label: '八年级' },
          { value: 'grade9', label: '九年级' }
        ];
      case 'gaozhong':
        return [
          { value: 'grade10', label: '高一' },
          { value: 'grade11', label: '高二' },
          { value: 'grade12', label: '高三' }
        ];
      default:
        return [];
    }
  }

  /**
   * 根据学段获取默认学科选项
   */
  private getDefaultSubjectsByStage(stage: string): any[] {
    const commonSubjects = [
      { value: 'chinese', label: '语文' },
      { value: 'math', label: '数学' },
      { value: 'english', label: '英语' }
    ];

    switch (stage) {
      case 'xiaoxue':
        return [
          ...commonSubjects,
          { value: 'science', label: '科学' },
          { value: 'morality', label: '道德与法治' },
          { value: 'music', label: '音乐' },
          { value: 'art', label: '美术' },
          { value: 'pe', label: '体育' }
        ];
      case 'chuzhong':
        return [
          ...commonSubjects,
          { value: 'physics', label: '物理' },
          { value: 'chemistry', label: '化学' },
          { value: 'biology', label: '生物' },
          { value: 'history', label: '历史' },
          { value: 'geography', label: '地理' },
          { value: 'politics', label: '道德与法治' }
        ];
      case 'gaozhong':
        return [
          ...commonSubjects,
          { value: 'physics', label: '物理' },
          { value: 'chemistry', label: '化学' },
          { value: 'biology', label: '生物' },
          { value: 'history', label: '历史' },
          { value: 'geography', label: '地理' },
          { value: 'politics', label: '思想政治' }
        ];
      default:
        return commonSubjects;
    }
  }

  /**
   * 获取年级选项
   */
  async getGradesByStage(stage: string): Promise<any[]> {
    try {
      console.log('🔍 获取年级选项:', stage);

      const electronAPI = (window as any).electronAPI;
      if (!electronAPI?.apiRequest) {
        console.warn('⚠️ Electron API代理不可用，使用默认选项');
        return this.getDefaultGradesByStage(stage);
      }

      const tagsUrl = `${this.config.api.baseURL}${this.config.api.endpoints.tags}`;
      const response = await electronAPI.apiRequest(tagsUrl);

      if (response.success && response.data) {
        const grades = this.extractGradesByStage(response.data, stage);
        console.log(`✅ 从API获取到 ${grades.length} 个年级:`, grades);

        if (grades.length > 0) {
          return grades;
        }
      }

      console.warn('⚠️ API未返回有效年级数据，使用默认选项');
      return this.getDefaultGradesByStage(stage);
    } catch (error) {
      console.error('❌ 获取年级失败:', error);
      return this.getDefaultGradesByStage(stage);
    }
  }

  /**
   * 获取学科选项
   */
  async getSubjectsByStageAndGrade(stage: string, grade: string): Promise<any[]> {
    try {
      console.log('🔍 获取学科选项:', { stage, grade });

      const electronAPI = (window as any).electronAPI;
      if (!electronAPI?.apiRequest) {
        console.warn('⚠️ Electron API代理不可用，使用默认选项');
        return this.getDefaultSubjectsByStage(stage);
      }

      const tagsUrl = `${this.config.api.baseURL}${this.config.api.endpoints.tags}`;
      const response = await electronAPI.apiRequest(tagsUrl);

      if (response.success && response.data) {
        const subjects = this.extractSubjectsByStageAndGrade(response.data, stage, grade);
        console.log(`✅ 从API获取到 ${subjects.length} 个学科:`, subjects);

        if (subjects.length > 0) {
          return subjects;
        }
      }

      console.warn('⚠️ API未返回有效学科数据，使用默认选项');
      return this.getDefaultSubjectsByStage(stage);
    } catch (error) {
      console.error('❌ 获取学科失败:', error);
      return this.getDefaultSubjectsByStage(stage);
    }
  }

  /**
   * 从标签数据中提取学段信息
   */
  private extractStagesFromTags(tagsData: any): any[] {
    try {
      console.log('🔍 开始解析标签数据，数据类型:', typeof tagsData);
      console.log('🔍 标签数据结构:', Object.keys(tagsData || {}));

      // 根据智慧教育平台的实际API结构解析
      let stageTagsArray = [];

      // 方法1: 直接从学段维度获取 (zxxxd)
      if (tagsData[this.config.tagDimensions.stage]) {
        stageTagsArray = tagsData[this.config.tagDimensions.stage];
        console.log('📋 从学段维度获取到标签:', stageTagsArray);
      }
      // 方法2: 从tags字段获取
      else if (tagsData.tags && tagsData.tags[this.config.tagDimensions.stage]) {
        stageTagsArray = tagsData.tags[this.config.tagDimensions.stage];
        console.log('📋 从tags.学段维度获取到标签:', stageTagsArray);
      }
      // 方法3: 如果是数组，直接筛选学段相关标签
      else if (Array.isArray(tagsData)) {
        stageTagsArray = tagsData.filter((tag: any) => {
          const tagName = tag.tag_name || tag.name || tag.label || '';
          const dimension = tag.dimension || tag.tag_dimension || '';

          return dimension === this.config.tagDimensions.stage ||
                 tagName.includes('小学') ||
                 tagName.includes('初中') ||
                 tagName.includes('高中');
        });
        console.log('📋 从数组中筛选到学段标签:', stageTagsArray);
      }
      // 方法4: 尝试其他可能的字段
      else {
        const possibleFields = ['data', 'list', 'items', 'content'];
        for (const field of possibleFields) {
          if (tagsData[field] && Array.isArray(tagsData[field])) {
            stageTagsArray = tagsData[field].filter((tag: any) => {
              const tagName = tag.tag_name || tag.name || tag.label || '';
              const dimension = tag.dimension || tag.tag_dimension || '';

              return dimension === this.config.tagDimensions.stage ||
                     tagName.includes('小学') ||
                     tagName.includes('初中') ||
                     tagName.includes('高中');
            });
            if (stageTagsArray.length > 0) {
              console.log(`📋 从${field}字段中筛选到学段标签:`, stageTagsArray);
              break;
            }
          }
        }
      }

      if (!Array.isArray(stageTagsArray) || stageTagsArray.length === 0) {
        console.warn('⚠️ 未找到学段标签数据，数据结构:', tagsData);
        return [];
      }

      // 转换为标准格式
      const stages = stageTagsArray.map((tag: any) => ({
        value: tag.tag_id || tag.id || tag.value || tag.code,
        label: tag.tag_name || tag.name || tag.label || tag.title
      })).filter(stage => stage.value && stage.label);

      console.log('🎯 解析出的学段选项:', stages);
      return stages;
    } catch (error) {
      console.warn('提取学段信息失败:', error);
      return [];
    }
  }

  /**
   * 从标签数据中提取年级信息
   */
  private extractGradesByStage(tagsData: any, stage: string): any[] {
    try {
      if (!Array.isArray(tagsData)) return [];

      // 查找年级相关的标签
      const grades = tagsData.filter((tag: any) =>
        tag.tag_name && (
          tag.tag_name.includes('年级') ||
          tag.tag_name.includes('一年级') ||
          tag.tag_name.includes('二年级') ||
          tag.tag_name.includes('三年级') ||
          tag.tag_name.includes('四年级') ||
          tag.tag_name.includes('五年级') ||
          tag.tag_name.includes('六年级') ||
          tag.tag_name.includes('七年级') ||
          tag.tag_name.includes('八年级') ||
          tag.tag_name.includes('九年级')
        )
      );

      return grades.map((grade: any) => ({
        value: grade.tag_id || grade.id,
        label: grade.tag_name || grade.name
      }));
    } catch (error) {
      console.warn('提取年级信息失败:', error);
      return [];
    }
  }

  /**
   * 从标签数据中提取学科信息
   */
  private extractSubjectsByStageAndGrade(tagsData: any, stage: string, grade: string): any[] {
    try {
      if (!Array.isArray(tagsData)) return [];

      // 查找学科相关的标签
      const subjects = tagsData.filter((tag: any) =>
        tag.tag_name && (
          tag.tag_name.includes('语文') ||
          tag.tag_name.includes('数学') ||
          tag.tag_name.includes('英语') ||
          tag.tag_name.includes('物理') ||
          tag.tag_name.includes('化学') ||
          tag.tag_name.includes('生物') ||
          tag.tag_name.includes('历史') ||
          tag.tag_name.includes('地理') ||
          tag.tag_name.includes('政治') ||
          tag.tag_name.includes('科学')
        )
      );

      return subjects.map((subject: any) => ({
        value: subject.tag_id || subject.id,
        label: subject.tag_name || subject.name
      }));
    } catch (error) {
      console.warn('提取学科信息失败:', error);
      return [];
    }
  }
}

export default SmartEduClient;
