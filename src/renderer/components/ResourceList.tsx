import React, { useState, useCallback, useMemo } from 'react';
import {
  Card,
  List,
  Button,
  Space,
  Tag,
  Typography,
  Checkbox,
  Alert,
  Tooltip,
  Badge,
  Empty,
  Spin,
  Row,
  Col,
  Switch,
  Divider,
  Menu
} from 'antd';
import { getSmartEduConfig } from '../../shared/config/smartedu.config';
import {
  DownloadOutlined,
  EyeOutlined,
  LockOutlined,
  UnlockOutlined,
  BookOutlined,
  PlayCircleOutlined,
  AppstoreOutlined,
  BarsOutlined,
  FolderOutlined
} from '@ant-design/icons';
import { 
  CourseResource, 
  ResourceListProps,
  ResourceDetail 
} from '../../shared/types';

const { Text, Title } = Typography;

type ViewMode = 'card' | 'list';

/**
 * 资源列表组件
 * 支持卡片和列表两种展示模式，提供资源下载和批量操作功能
 */
export const ResourceList: React.FC<ResourceListProps> = ({
  resources,
  onDownload,
  onBatchDownload,
  loading = false,
  selectedResources = [],
  onSelectionChange
}) => {
  const [viewMode, setViewMode] = useState<ViewMode>('card');
  const [selectedIds, setSelectedIds] = useState<string[]>(selectedResources);
  const [groupByUnit, setGroupByUnit] = useState<boolean>(true); // 默认开启单元分组
  const [selectedUnit, setSelectedUnit] = useState<string>('全部');
  const [expandedUnits, setExpandedUnits] = useState<Set<string>>(new Set(['第一单元']));
  // const [expandedDetails, setExpandedDetails] = useState<Record<string, ResourceDetail>>({});
  // const [loadingDetails, setLoadingDetails] = useState<Record<string, boolean>>({});

  /**
   * 处理资源选择变更
   */
  const handleSelectionChange = useCallback((resourceId: string, checked: boolean) => {
    const newSelectedIds = checked 
      ? [...selectedIds, resourceId]
      : selectedIds.filter(id => id !== resourceId);
    
    setSelectedIds(newSelectedIds);
    onSelectionChange?.(newSelectedIds);
  }, [selectedIds, onSelectionChange]);

  /**
   * 处理全选/取消全选
   */
  const handleSelectAll = useCallback((checked: boolean) => {
    const newSelectedIds = checked ? resources.map(r => r.id) : [];
    setSelectedIds(newSelectedIds);
    onSelectionChange?.(newSelectedIds);
  }, [resources, onSelectionChange]);

  /**
   * 按单元分组资源 - 基于智慧教育平台的实际课程结构
   */
  const groupedResources = useMemo(() => {
    const groups: Record<string, CourseResource[]> = {};

    // 获取配置
    const config = getSmartEduConfig();

    // 智能单元识别函数
    const extractUnitFromTitle = (title: string): { unit: string; order: number } => {
      // 从标题中提取单元信息的通用逻辑

      // 1. 直接匹配单元关键词
      const unitMatch = title.match(config.unitMapping.patterns.unitKeyword);
      if (unitMatch) {
        const unitNumber = unitMatch[1];
        const unitName = `第${unitNumber}单元`;
        return { unit: unitName, order: 1 };
      }

      // 2. 匹配语文园地
      const gardenMatch = title.match(config.unitMapping.patterns.gardenKeyword);
      if (gardenMatch) {
        const unitNumber = gardenMatch[1] || '一';
        const unitName = `第${unitNumber}单元`;
        return { unit: unitName, order: 99 }; // 语文园地通常在单元末尾
      }

      // 3. 匹配口语交际和习作
      const oralMatch = title.match(config.unitMapping.patterns.oralKeyword);
      if (oralMatch) {
        // 尝试从上下文推断单元，默认为第一单元
        return { unit: '第一单元', order: 90 };
      }

      // 4. 根据课程序号推断（如果标题包含数字）
      const numberMatch = title.match(config.unitMapping.patterns.lessonNumber);
      if (numberMatch) {
        const lessonNumber = parseInt(numberMatch[1]);
        // 数学通常用阿拉伯数字单元
        if (lessonNumber <= 4) return { unit: '第1单元', order: lessonNumber };
        if (lessonNumber <= 8) return { unit: '第2单元', order: lessonNumber - 4 };
        if (lessonNumber <= 12) return { unit: '第3单元', order: lessonNumber - 8 };
        if (lessonNumber <= 16) return { unit: '第4单元', order: lessonNumber - 12 };
        if (lessonNumber <= 20) return { unit: '第5单元', order: lessonNumber - 16 };
        if (lessonNumber <= 24) return { unit: '第6单元', order: lessonNumber - 20 };
        if (lessonNumber <= 28) return { unit: '第7单元', order: lessonNumber - 24 };
        return { unit: '第8单元', order: lessonNumber - 28 };
      }

      // 5. 根据课程内容关键词推断单元（已移除硬编码关键词）
      // 这里可以添加更通用的单元识别逻辑，但不再依赖硬编码关键词

      return { unit: '其他', order: 999 };
    };

    resources.forEach(resource => {
      let unit = '其他';
      let order = 999;

      // 优先级1: 使用API返回的unit字段
      if (resource.metadata?.unit) {
        unit = resource.metadata.unit;
        // 使用章节排序，如果没有则使用默认值
        order = resource.metadata?.chapterOrder || 999;
      }
      // 优先级2: 使用API返回的chapter字段
      else if (resource.metadata?.chapter) {
        unit = resource.metadata.chapter;
        order = resource.metadata?.chapterOrder || 999;
      }
      // 优先级3: 使用API返回的section字段
      else if (resource.metadata?.section) {
        unit = resource.metadata.section;
        order = resource.metadata?.chapterOrder || 999;
      }
      // 优先级4: 从标题中智能识别单元信息（作为回退方案）
      else {
        const extracted = extractUnitFromTitle(resource.title);
        unit = extracted.unit;
        order = extracted.order;
      }

      if (!groups[unit]) {
        groups[unit] = [];
      }

      // 添加排序信息到资源对象
      const resourceWithOrder = { ...resource, _order: order };
      groups[unit].push(resourceWithOrder);
    });

    // 动态排序单元，优先使用API数据的自然顺序
    const sortedGroups: Record<string, CourseResource[]> = {};

    // 获取所有单元名称并进行智能排序
    const allUnits = Object.keys(groups);
    const sortedUnits = allUnits.sort((a, b) => {
      // 优先级1: "其他" 总是排在最后
      if (a === '其他') return 1;
      if (b === '其他') return -1;

      // 优先级2: 按单元编号排序（第一单元、第二单元等）
      const unitNumberA = a.match(/第([一二三四五六七八九十\d]+)[单元]/)?.[1];
      const unitNumberB = b.match(/第([一二三四五六七八九十\d]+)[单元]/)?.[1];

      if (unitNumberA && unitNumberB) {
        // 转换中文数字为阿拉伯数字进行比较
        const numA = convertChineseNumber(unitNumberA);
        const numB = convertChineseNumber(unitNumberB);
        return numA - numB;
      }

      // 优先级3: 按字母顺序排序
      return a.localeCompare(b, 'zh-CN');
    });

    // 按排序后的单元顺序组织数据
    sortedUnits.forEach(unit => {
      if (groups[unit] && groups[unit].length > 0) {
        // 对每个单元内的课程按order排序
        const sortedCourses = groups[unit].sort((a: any, b: any) => {
          const orderA = a._order || 999;
          const orderB = b._order || 999;
          if (orderA !== orderB) {
            return orderA - orderB;
          }
          // 如果order相同，按标题排序
          return a.title.localeCompare(b.title, 'zh-CN');
        });

        // 移除临时的_order属性
        sortedGroups[unit] = sortedCourses.map(({ _order, ...resource }: any) => resource);
      }
    });

    // 辅助函数：转换中文数字为阿拉伯数字
    function convertChineseNumber(chinese: string): number {
      const chineseNumbers: Record<string, number> = {
        '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
        '六': 6, '七': 7, '八': 8, '九': 9, '十': 10
      };

      // 如果已经是阿拉伯数字，直接返回
      if (/^\d+$/.test(chinese)) {
        return parseInt(chinese);
      }

      return chineseNumbers[chinese] || 999;
    }

    console.log('📚 单元分组结果:', Object.keys(sortedGroups).map(unit => `${unit}: ${sortedGroups[unit].length}个课程`));

    // 调试每个单元的课程顺序
    Object.entries(sortedGroups).forEach(([unit, courses]) => {
      if (courses.length > 0) {
        console.log(`📖 ${unit} 课程顺序:`, courses.slice(0, 5).map(c => c.title));
      }
    });

    return sortedGroups;
  }, [resources]);

  /**
   * 根据选中单元过滤资源
   */
  const filteredResources = useMemo(() => {
    if (!groupByUnit || selectedUnit === '全部') {
      return resources;
    }
    return groupedResources[selectedUnit] || [];
  }, [resources, groupByUnit, selectedUnit, groupedResources]);

  /**
   * 处理单个资源下载
   */
  const handleDownload = useCallback((resource: CourseResource) => {
    onDownload(resource);
  }, [onDownload]);

  /**
   * 处理批量下载
   */
  const handleBatchDownload = useCallback(() => {
    const selectedResources = resources.filter(r => selectedIds.includes(r.id));
    onBatchDownload(selectedResources);
  }, [resources, selectedIds, onBatchDownload]);

  /**
   * 获取资源类型图标
   */
  const getResourceIcon = useCallback((type: string) => {
    switch (type) {
      case 'textbook':
        return <BookOutlined />;
      case 'video':
        return <PlayCircleOutlined />;
      default:
        return <BookOutlined />;
    }
  }, []);

  /**
   * 获取访问级别标签
   */
  const getAccessLevelTag = useCallback((resource: CourseResource) => {
    const { accessLevel, requiresAuth } = resource;
    
    if (!requiresAuth) {
      return <Tag color="green" icon={<UnlockOutlined />}>公开</Tag>;
    }
    
    switch (accessLevel) {
      case 'public':
        return <Tag color="green" icon={<UnlockOutlined />}>公开</Tag>;
      case 'registered':
        return <Tag color="blue" icon={<LockOutlined />}>需登录</Tag>;
      case 'premium':
        return <Tag color="gold" icon={<LockOutlined />}>高级</Tag>;
      default:
        return <Tag color="default" icon={<LockOutlined />}>未知</Tag>;
    }
  }, []);

  /**
   * 检查资源是否可下载
   */
  const isResourceDownloadable = useCallback((resource: CourseResource) => {
    // 简化逻辑：所有资源都可以尝试下载
    return true;
  }, []);

  /**
   * 获取下载按钮状态和提示
   */
  const getDownloadButtonProps = useCallback((resource: CourseResource) => {
    // 简化逻辑：所有资源都可以尝试下载
    return {
      disabled: false,
      tooltip: undefined
    };
  }, []);

  /**
   * 格式化文件大小
   */
  const formatFileSize = useCallback((bytes?: number) => {
    if (!bytes) return '未知';
    
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  }, []);

  /**
   * 格式化时长
   */
  const formatDuration = useCallback((seconds?: number) => {
    if (!seconds) return '未知';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const remainingSeconds = seconds % 60;
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }, []);

  /**
   * 渲染资源元数据
   */
  const renderResourceMetadata = useCallback((resource: CourseResource) => {
    const { metadata } = resource;
    
    return (
      <Space wrap>
        <Tag>{metadata.stage}</Tag>
        <Tag>{metadata.grade}</Tag>
        <Tag>{metadata.subject}</Tag>
        <Tag>{metadata.version}</Tag>
        <Tag>{metadata.volume}</Tag>
        {metadata.chapter && <Tag color="blue">{metadata.chapter}</Tag>}
        {metadata.lesson && <Tag color="green">{metadata.lesson}</Tag>}
      </Space>
    );
  }, []);

  /**
   * 渲染资源统计信息
   */
  const renderResourceStats = useCallback((resource: CourseResource) => {
    const { type, metadata } = resource;
    
    return (
      <Space split={<Divider type="vertical" />}>
        {type === 'textbook' && metadata.fileSize && (
          <Text type="secondary">大小: {formatFileSize(metadata.fileSize)}</Text>
        )}
        {type === 'video' && metadata.duration && (
          <Text type="secondary">时长: {formatDuration(metadata.duration)}</Text>
        )}
        {type === 'video' && metadata.fileSize && (
          <Text type="secondary">大小: {formatFileSize(metadata.fileSize)}</Text>
        )}
      </Space>
    );
  }, [formatFileSize, formatDuration]);

  /**
   * 渲染卡片模式的资源项
   */
  const renderCardItem = useCallback((resource: CourseResource) => {
    const downloadButtonProps = getDownloadButtonProps(resource);
    const isSelected = selectedIds.includes(resource.id);
    const isSubItem = resource.metadata?.isSubItem || false;

    return (
      <Card
        key={resource.id}
        size="small"
        hoverable
        className={isSelected ? 'selected-resource-card' : ''}
        style={{
          marginLeft: isSubItem ? '32px' : '0', // 子项缩进增加
          marginBottom: isSubItem ? '4px' : '8px', // 子项间距减小
          backgroundColor: isSubItem ? '#f8f9fa' : '#ffffff', // 子项背景色
          borderLeft: isSubItem ? '4px solid #52c41a' : 'none', // 子项左边框（绿色）
          borderRadius: isSubItem ? '4px' : '6px', // 子项圆角稍小
          boxShadow: isSubItem ? '0 1px 3px rgba(0,0,0,0.1)' : '0 2px 8px rgba(0,0,0,0.1)' // 子项阴影较浅
        }}
        title={
          <Space>
            <Checkbox
              checked={isSelected}
              onChange={(e) => handleSelectionChange(resource.id, e.target.checked)}
            />
            {isSubItem ? (
              <span style={{ color: '#52c41a', fontSize: '16px', fontWeight: 'bold' }}>├─</span>
            ) : (
              getResourceIcon(resource.type)
            )}
            <Text strong style={{
              fontSize: isSubItem ? '13px' : '14px',
              color: isSubItem ? '#595959' : '#262626'
            }}>
              {resource.title}
            </Text>
            {isSubItem && (
              <Tag color="green" style={{ fontSize: '11px' }}>
                {resource.metadata?.isVirtual ? '补充' : '子诗'}
              </Tag>
            )}
          </Space>
        }
        extra={getAccessLevelTag(resource)}
        actions={[
          <Tooltip title={downloadButtonProps.tooltip} key="download">
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              disabled={downloadButtonProps.disabled}
              onClick={() => handleDownload(resource)}
              size="small"
            >
              下载
            </Button>
          </Tooltip>,
          <Button
            icon={<EyeOutlined />}
            onClick={() => {/* TODO: 查看详情 */}}
            size="small"
            key="detail"
          >
            详情
          </Button>
        ]}
      >
        <div style={{ marginBottom: 8 }}>
          {renderResourceMetadata(resource)}
        </div>
        <div>
          {renderResourceStats(resource)}
        </div>
      </Card>
    );
  }, [selectedIds, getDownloadButtonProps, getResourceIcon, getAccessLevelTag, handleSelectionChange, handleDownload, renderResourceMetadata, renderResourceStats]);

  /**
   * 渲染带层级结构的资源列表
   */
  const renderHierarchicalResources = useCallback((resources: CourseResource[]) => {
    const hierarchicalItems: JSX.Element[] = [];
    const addedParents = new Set<string>(); // 记录已添加的父项占位符

    // 按照排序顺序处理资源
    const sortedResources = [...resources].sort((a, b) => {
      const aOrder = (a as any)._order || a.metadata?.chapterOrder || 999;
      const bOrder = (b as any)._order || b.metadata?.chapterOrder || 999;
      return aOrder - bOrder;
    });

    // 分组处理：先处理主课程，再处理子项
    const mainCourses: CourseResource[] = [];
    const subItems: CourseResource[] = [];

    sortedResources.forEach(resource => {
      if (resource.metadata?.isSubItem) {
        subItems.push(resource);
      } else {
        mainCourses.push(resource);
      }
    });

    // 渲染主课程和对应的子项
    mainCourses.forEach(mainCourse => {
      // 添加主课程
      hierarchicalItems.push(
        <Col key={mainCourse.id} xs={24} sm={12} md={8} lg={6}>
          {renderCardItem(mainCourse)}
        </Col>
      );

      // 查找并添加对应的子项
      const relatedSubItems = subItems.filter(subItem =>
        subItem.metadata?.parentChapter === mainCourse.metadata?.chapter
      );

      relatedSubItems.forEach(subItem => {
        hierarchicalItems.push(
          <Col key={subItem.id} xs={24} sm={12} md={8} lg={6}>
            {renderCardItem(subItem)}
          </Col>
        );
      });
    });

    // 添加没有对应主课程的子项（孤儿子项）
    const orphanSubItems = subItems.filter(subItem => {
      const parentChapter = subItem.metadata?.parentChapter;
      return !mainCourses.some(main => main.metadata?.chapter === parentChapter);
    });

    orphanSubItems.forEach(orphanSubItem => {
      const parentChapter = orphanSubItem.metadata?.parentChapter;

      // 为孤儿子项添加父项占位符（避免重复）
      if (parentChapter && !addedParents.has(parentChapter)) {
        addedParents.add(parentChapter);
        hierarchicalItems.push(
          <Col key={`parent-placeholder-${parentChapter}-${Date.now()}`} xs={24}>
            <Card
              size="small"
              style={{
                marginBottom: 8,
                backgroundColor: '#f0f8ff',
                borderLeft: '4px solid #1890ff'
              }}
              styles={{ body: { padding: '12px' } }}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                <div style={{ fontSize: '18px', color: '#1890ff' }}>
                  📚
                </div>
                <Text strong style={{ fontSize: '14px' }}>
                  {parentChapter}
                </Text>
                <Tag color="green">主课程</Tag>
              </div>
            </Card>
          </Col>
        );
      }

      // 添加孤儿子项
      hierarchicalItems.push(
        <Col key={orphanSubItem.id} xs={24} sm={12} md={8} lg={6}>
          {renderCardItem(orphanSubItem)}
        </Col>
      );
    });

    return hierarchicalItems;
  }, [renderCardItem]);

  /**
   * 渲染列表模式的资源项
   */
  const renderListItem = useCallback((resource: CourseResource) => {
    const downloadButtonProps = getDownloadButtonProps(resource);
    const isSelected = selectedIds.includes(resource.id);
    
    return (
      <List.Item
        key={resource.id}
        className={isSelected ? 'selected-resource-item' : ''}
        actions={[
          <Tooltip title={downloadButtonProps.tooltip} key="download">
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              disabled={downloadButtonProps.disabled}
              onClick={() => handleDownload(resource)}
              size="small"
            >
              下载
            </Button>
          </Tooltip>,
          <Button
            icon={<EyeOutlined />}
            onClick={() => {/* TODO: 查看详情 */}}
            size="small"
            key="detail"
          >
            详情
          </Button>
        ]}
      >
        <List.Item.Meta
          avatar={
            <Checkbox
              checked={isSelected}
              onChange={(e) => handleSelectionChange(resource.id, e.target.checked)}
            />
          }
          title={
            <Space>
              {getResourceIcon(resource.type)}
              <Text strong>{resource.title}</Text>
              {getAccessLevelTag(resource)}
            </Space>
          }
          description={
            <div>
              <div style={{ marginBottom: 4 }}>
                {renderResourceMetadata(resource)}
              </div>
              <div>
                {renderResourceStats(resource)}
              </div>
            </div>
          }
        />
      </List.Item>
    );
  }, [selectedIds, getDownloadButtonProps, getResourceIcon, getAccessLevelTag, handleSelectionChange, handleDownload, renderResourceMetadata, renderResourceStats]);

  /**
   * 计算统计信息
   */
  const stats = useMemo(() => {
    const total = resources.length;
    const selected = selectedIds.length;
    const downloadable = resources.filter(r => isResourceDownloadable(r)).length;
    const textbooks = resources.filter(r => r.type === 'textbook').length;
    const videos = resources.filter(r => r.type === 'video').length;
    
    return { total, selected, downloadable, textbooks, videos };
  }, [resources, selectedIds, isResourceDownloadable]);

  /**
   * 检查是否全选
   */
  const isAllSelected = useMemo(() => {
    return resources.length > 0 && selectedIds.length === resources.length;
  }, [resources.length, selectedIds.length]);

  /**
   * 检查是否部分选择
   */
  const isIndeterminate = useMemo(() => {
    return selectedIds.length > 0 && selectedIds.length < resources.length;
  }, [selectedIds.length, resources.length]);

  if (loading) {
    return (
      <Card>
        <Spin size="large" style={{ display: 'block', textAlign: 'center', padding: '50px 0' }} />
      </Card>
    );
  }

  if (resources.length === 0) {
    return (
      <Card>
        <Empty 
          description="暂无资源"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      </Card>
    );
  }

  return (
    <Card
      title={
        <Space>
          <Title level={4} style={{ margin: 0 }}>资源列表</Title>
          <Badge count={stats.total} showZero color="blue" />
        </Space>
      }
      extra={
        <Space>
          <Tooltip title="按单元分组显示">
            <Switch
              checkedChildren="单元"
              unCheckedChildren="全部"
              checked={groupByUnit}
              onChange={setGroupByUnit}
              size="small"
            />
          </Tooltip>
          <Divider type="vertical" />
          <Tooltip title="切换显示模式">
            <Switch
              checkedChildren={<AppstoreOutlined />}
              unCheckedChildren={<BarsOutlined />}
              checked={viewMode === 'card'}
              onChange={(checked) => setViewMode(checked ? 'card' : 'list')}
            />
          </Tooltip>
        </Space>
      }
    >
      {/* 统计信息和批量操作 */}
      <div style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col flex="auto">
            <Space wrap>
              <Checkbox
                indeterminate={isIndeterminate}
                checked={isAllSelected}
                onChange={(e) => handleSelectAll(e.target.checked)}
              >
                全选
              </Checkbox>
              <Text type="secondary">
                共 {stats.total} 个资源，已选择 {stats.selected} 个
              </Text>
              <Text type="secondary">
                可下载 {stats.downloadable} 个
              </Text>
              <Text type="secondary">
                教材 {stats.textbooks} 个，视频 {stats.videos} 个
              </Text>
            </Space>
          </Col>
          <Col>
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              disabled={selectedIds.length === 0}
              onClick={handleBatchDownload}
            >
              批量下载 ({selectedIds.length})
            </Button>
          </Col>
        </Row>
      </div>



      {/* 资源列表 */}
      {groupByUnit ? (
        <Row gutter={16} style={{ height: 'calc(100vh - 300px)' }}>
          {/* 左侧单元导航 */}
          <Col span={6}>
            <Card
              title="单元目录"
              size="small"
              style={{ height: '100%', overflow: 'auto' }}
              styles={{ body: { padding: '8px 0' } }}
            >
              <Menu
                mode="inline"
                selectedKeys={[selectedUnit]}
                openKeys={Array.from(expandedUnits)}
                onSelect={({ key }) => setSelectedUnit(key as string)}
                onOpenChange={(keys) => setExpandedUnits(new Set(keys as string[]))}
                style={{ border: 'none' }}
                items={[
                  {
                    key: '全部',
                    icon: <BookOutlined />,
                    label: `全部 (${resources.length})`
                  },
                  { type: 'divider' },
                  ...Object.entries(groupedResources).map(([unitName, unitResources]) => ({
                    key: unitName,
                    icon: <FolderOutlined />,
                    label: `${unitName} (${unitResources.length})`,
                    children: [
                      ...unitResources.slice(0, 10).map(resource => ({
                        key: `${unitName}-${resource.id}`,
                        label: resource.title.length > 20 ? `${resource.title.slice(0, 20)}...` : resource.title,
                        style: { fontSize: '12px', paddingLeft: '48px' }
                      })),
                      ...(unitResources.length > 10 ? [{
                        key: `${unitName}-more`,
                        label: `还有 ${unitResources.length - 10} 个课程...`,
                        disabled: true,
                        style: { paddingLeft: '48px', fontSize: '12px', color: '#999' }
                      }] : [])
                    ]
                  }))
                ]}
              />
            </Card>
          </Col>

          {/* 右侧课程列表 */}
          <Col span={18}>
            <Card
              title={
                <Space>
                  <span>{selectedUnit === '全部' ? '全部课程' : selectedUnit}</span>
                  <Tag color="blue">{filteredResources.length} 个资源</Tag>
                </Space>
              }
              size="small"
              style={{ height: '100%' }}
              styles={{ body: { padding: '16px', overflow: 'auto', height: 'calc(100% - 57px)' } }}
            >
              {viewMode === 'card' ? (
                <Row gutter={[16, 16]}>
                  {renderHierarchicalResources(filteredResources)}
                </Row>
              ) : (
                <List
                  dataSource={filteredResources}
                  renderItem={renderListItem}
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
                  }}
                />
              )}
            </Card>
          </Col>
        </Row>
      ) : (
        // 非单元模式：显示所有资源的列表视图
        <div style={{ height: 'calc(100vh - 300px)', overflow: 'auto' }}>
          {viewMode === 'card' ? (
            <Row gutter={[16, 16]}>
              {resources.map(resource => (
                <Col key={resource.id} xs={24} sm={12} md={8} lg={6}>
                  {renderCardItem(resource)}
                </Col>
              ))}
            </Row>
          ) : (
            <List
              dataSource={resources}
              renderItem={renderListItem}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`
              }}
            />
          )}
        </div>
      )}
    </Card>
  );
};

export default ResourceList;