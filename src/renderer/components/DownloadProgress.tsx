import React from 'react';
import { Progress, Card, Typography, Space, Tag, But<PERSON>, Tooltip } from 'antd';
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  StopOutlined, 
  ReloadOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { DownloadTask, TaskStatus } from '../../shared/types';

const { Text } = Typography;

/**
 * 下载进度组件属性
 */
export interface DownloadProgressProps {
  task: DownloadTask;
  onPause?: (taskId: string) => void;
  onResume?: (taskId: string) => void;
  onCancel?: (taskId: string) => void;
  onRetry?: (taskId: string) => void;
  onClear?: (taskId: string) => void;
  showActions?: boolean;
  compact?: boolean;
}

/**
 * 格式化文件大小
 */
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 格式化时间
 */
const formatTime = (seconds: number): string => {
  if (!seconds || seconds === Infinity) return '--';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
};

/**
 * 格式化速度
 */
const formatSpeed = (bytesPerSecond: number): string => {
  return `${formatFileSize(bytesPerSecond)}/s`;
};

/**
 * 获取状态颜色
 */
const getStatusColor = (status: TaskStatus): string => {
  switch (status) {
    case 'completed':
      return 'success';
    case 'downloading':
      return 'processing';
    case 'failed':
      return 'error';
    case 'paused':
      return 'warning';
    case 'cancelled':
      return 'default';
    default:
      return 'default';
  }
};

/**
 * 获取状态图标
 */
const getStatusIcon = (status: TaskStatus) => {
  switch (status) {
    case 'completed':
      return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
    case 'downloading':
      return <PlayCircleOutlined style={{ color: '#1890ff' }} />;
    case 'failed':
      return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
    case 'paused':
      return <PauseCircleOutlined style={{ color: '#faad14' }} />;
    case 'cancelled':
      return <StopOutlined style={{ color: '#d9d9d9' }} />;
    case 'pending':
      return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;
    default:
      return null;
  }
};

/**
 * 获取状态文本
 */
const getStatusText = (status: TaskStatus): string => {
  switch (status) {
    case 'pending':
      return '等待中';
    case 'downloading':
      return '下载中';
    case 'paused':
      return '已暂停';
    case 'completed':
      return '已完成';
    case 'failed':
      return '下载失败';
    case 'cancelled':
      return '已取消';
    default:
      return '未知状态';
  }
};

/**
 * 下载进度组件
 */
export const DownloadProgress: React.FC<DownloadProgressProps> = ({
  task,
  onPause,
  onResume,
  onCancel,
  onRetry,
  onClear,
  showActions = true,
  compact = false
}) => {
  const { resource, status, progress, speed, estimatedTime, error } = task;

  // 计算已下载和总大小
  const downloadedBytes = resource.metadata?.fileSize 
    ? (resource.metadata.fileSize * progress) / 100 
    : 0;
  const totalBytes = resource.metadata?.fileSize || 0;

  // 渲染操作按钮
  const renderActions = () => {
    if (!showActions) return null;

    const actions = [];

    switch (status) {
      case 'downloading':
        if (onPause) {
          actions.push(
            <Tooltip key="pause" title="暂停下载">
              <Button 
                type="text" 
                size="small" 
                icon={<PauseCircleOutlined />}
                onClick={() => onPause(task.id)}
              />
            </Tooltip>
          );
        }
        if (onCancel) {
          actions.push(
            <Tooltip key="cancel" title="取消下载">
              <Button 
                type="text" 
                size="small" 
                icon={<StopOutlined />}
                onClick={() => onCancel(task.id)}
                danger
              />
            </Tooltip>
          );
        }
        break;

      case 'paused':
        if (onResume) {
          actions.push(
            <Tooltip key="resume" title="继续下载">
              <Button 
                type="text" 
                size="small" 
                icon={<PlayCircleOutlined />}
                onClick={() => onResume(task.id)}
              />
            </Tooltip>
          );
        }
        if (onCancel) {
          actions.push(
            <Tooltip key="cancel" title="取消下载">
              <Button 
                type="text" 
                size="small" 
                icon={<StopOutlined />}
                onClick={() => onCancel(task.id)}
                danger
              />
            </Tooltip>
          );
        }
        break;

      case 'failed':
        if (onRetry) {
          actions.push(
            <Tooltip key="retry" title="重试下载">
              <Button 
                type="text" 
                size="small" 
                icon={<ReloadOutlined />}
                onClick={() => onRetry(task.id)}
              />
            </Tooltip>
          );
        }
        if (onClear) {
          actions.push(
            <Tooltip key="clear" title="清除任务">
              <Button 
                type="text" 
                size="small" 
                icon={<DeleteOutlined />}
                onClick={() => onClear(task.id)}
                danger
              />
            </Tooltip>
          );
        }
        break;

      case 'completed':
      case 'cancelled':
        if (onClear) {
          actions.push(
            <Tooltip key="clear" title="清除任务">
              <Button 
                type="text" 
                size="small" 
                icon={<DeleteOutlined />}
                onClick={() => onClear(task.id)}
              />
            </Tooltip>
          );
        }
        break;
    }

    return actions.length > 0 ? <Space>{actions}</Space> : null;
  };

  // 紧凑模式渲染
  if (compact) {
    return (
      <div style={{ padding: '8px 0', borderBottom: '1px solid #f0f0f0' }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div style={{ flex: 1, marginRight: 16 }}>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
              {getStatusIcon(status)}
              <Text 
                strong 
                style={{ marginLeft: 8, fontSize: '14px' }}
                ellipsis={{ tooltip: resource.title }}
              >
                {resource.title}
              </Text>
              <Tag 
                color={getStatusColor(status)} 
                style={{ marginLeft: 8 }}
              >
                {getStatusText(status)}
              </Tag>
            </div>
            
            <Progress 
              percent={Math.round(progress)} 
              size="small"
              status={status === 'failed' ? 'exception' : status === 'completed' ? 'success' : 'active'}
              showInfo={false}
            />
            
            <div style={{ display: 'flex', justifyContent: 'space-between', fontSize: '12px', color: '#666', marginTop: 2 }}>
              <span>{formatFileSize(downloadedBytes)} / {formatFileSize(totalBytes)}</span>
              {status === 'downloading' && (
                <span>
                  {formatSpeed(speed)} · 剩余 {formatTime(estimatedTime)}
                  {task.retryCount > 0 && ` · 重试 ${task.retryCount}`}
                </span>
              )}
              {status === 'completed' && (
                <span style={{ color: '#52c41a' }}>✓ 完成</span>
              )}
              {status === 'failed' && (
                <span style={{ color: '#ff4d4f' }}>✗ 失败</span>
              )}
            </div>
          </div>
          
          {renderActions()}
        </div>
        
        {error && (
          <Text type="danger" style={{ fontSize: '12px' }}>
            错误: {error}
          </Text>
        )}
      </div>
    );
  }

  // 完整模式渲染
  return (
    <Card 
      size="small"
      style={{ marginBottom: 16 }}
      title={
        <div style={{ display: 'flex', alignItems: 'center' }}>
          {getStatusIcon(status)}
          <span style={{ marginLeft: 8 }}>{resource.title}</span>
          <Tag 
            color={getStatusColor(status)} 
            style={{ marginLeft: 'auto' }}
          >
            {getStatusText(status)}
          </Tag>
        </div>
      }
      extra={renderActions()}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <Progress 
          percent={Math.round(progress)} 
          status={status === 'failed' ? 'exception' : status === 'completed' ? 'success' : 'active'}
        />
        
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Text type="secondary">
            {formatFileSize(downloadedBytes)} / {formatFileSize(totalBytes)}
            {task.retryCount > 0 && ` (重试 ${task.retryCount}/${task.maxRetries})`}
          </Text>
          
          {status === 'downloading' && (
            <Text type="secondary">
              {formatSpeed(speed)} · 剩余 {formatTime(estimatedTime)}
            </Text>
          )}
          
          {status === 'completed' && (
            <Text type="success">
              下载完成 · 用时 {formatTime((Date.now() - task.createdAt.getTime()) / 1000)}
            </Text>
          )}
          
          {status === 'failed' && task.error && (
            <Text type="danger">
              失败原因: {task.error}
            </Text>
          )}
        </div>
        
        {resource.metadata && (
          <div style={{ display: 'flex', gap: 8, flexWrap: 'wrap' }}>
            <Tag>{resource.metadata.stage}</Tag>
            <Tag>{resource.metadata.grade}</Tag>
            <Tag>{resource.metadata.subject}</Tag>
            <Tag>{resource.metadata.version}</Tag>
            <Tag>{resource.metadata.volume}</Tag>
          </div>
        )}
        
        {error && (
          <Text type="danger">
            错误: {error}
          </Text>
        )}
      </Space>
    </Card>
  );
};

export default DownloadProgress;
