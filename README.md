# 智慧教育下载器 (Smart Edu Downloader)

一个用于从国家中小学智慧平台下载教育资源的桌面应用程序。

## 技术栈

- **框架**: Electron + React + TypeScript
- **构建工具**: Webpack
- **UI组件库**: Ant Design
- **状态管理**: Zustand
- **HTTP客户端**: Axios
- **文件处理**: fs-extra
- **代码规范**: ESLint + Prettier

## 开发环境设置

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

这将同时启动主进程和渲染进程的开发服务器。

### 构建应用

```bash
npm run build
```

### 启动应用

```bash
npm start
```

### 代码检查和格式化

```bash
# 运行 ESLint 检查
npm run lint

# 自动修复 ESLint 问题
npm run lint:fix

# 检查代码格式
npm run format:check

# 格式化代码
npm run format

# TypeScript 类型检查
npm run type-check
```

### 打包应用

```bash
# 打包为目录
npm run pack

# 构建安装包
npm run dist
```

## 项目结构

```
src/
├── main/           # Electron 主进程
│   ├── main.ts     # 主进程入口
│   └── preload.ts  # 预加载脚本
├── renderer/       # React 渲染进程
│   ├── App.tsx     # 主应用组件
│   ├── index.tsx   # 渲染进程入口
│   ├── index.html  # HTML 模板
│   └── styles/     # 样式文件
└── shared/         # 共享类型和工具
    └── types/      # TypeScript 类型定义
```

## 功能特性

- 🎯 与官网一致的资源筛选界面
- 📚 电子教材下载 (PDF格式)
- 🎥 教学视频下载 (M3U8转MP4)
- 📦 批量下载和队列管理
- 📁 智能文件组织和命名
- 🔄 断点续传和错误重试
- 📊 实时下载进度监控
- 🔐 支持登录和免登录模式

## 开发状态

当前项目处于初始化阶段，基础架构已搭建完成。后续将按照规划逐步实现各项功能。