import { BrowserWindow } from 'electron';

/**
 * DevTools 管理工具类
 * 用于处理开发工具相关的配置和错误
 */
export class DevToolsManager {
  private window: BrowserWindow;
  private devToolsOpened: boolean = false;

  constructor(window: BrowserWindow) {
    this.window = window;
    this.setupDevToolsHandlers();
  }

  /**
   * 设置 DevTools 事件处理器
   */
  private setupDevToolsHandlers(): void {
    // DevTools 打开事件
    this.window.webContents.on('devtools-opened', () => {
      this.devToolsOpened = true;
      console.log('✅ DevTools opened successfully');
    });

    // DevTools 关闭事件
    this.window.webContents.on('devtools-closed', () => {
      this.devToolsOpened = false;
      console.log('📴 DevTools closed');
    });

    // 过滤控制台消息，忽略 DevTools 相关的非关键错误
    this.window.webContents.on('console-message', (event, level, message, line, sourceId) => {
      // 过滤掉已知的 DevTools 错误
      if (this.isDevToolsError(message)) {
        return; // 静默忽略
      }

      // 记录所有级别的消息（临时调试）
      console.log(`🔍 Console [${this.getLevelName(level)}]: ${message}`);
      if (line && sourceId) {
        console.log(`   at ${sourceId}:${line}`);
      }
    });
  }

  /**
   * 安全地打开 DevTools
   */
  public openDevTools(): void {
    if (process.env.NODE_ENV !== 'development') {
      return;
    }

    try {
      // 等待页面加载完成后再打开 DevTools
      if (this.window.webContents.isLoading()) {
        this.window.webContents.once('did-finish-load', () => {
          setTimeout(() => this.doOpenDevTools(), 1000);
        });
      } else {
        setTimeout(() => this.doOpenDevTools(), 500);
      }
    } catch (error) {
      console.error('❌ Failed to open DevTools:', error);
    }
  }

  /**
   * 实际执行打开 DevTools 的操作
   */
  private doOpenDevTools(): void {
    try {
      if (!this.devToolsOpened && this.window && !this.window.isDestroyed()) {
        this.window.webContents.openDevTools({
          mode: 'detach', // 分离模式，避免布局问题
          activate: false, // 不自动激活，避免焦点问题
        });
      }
    } catch (error) {
      console.error('❌ Error opening DevTools:', error);
    }
  }

  /**
   * 关闭 DevTools
   */
  public closeDevTools(): void {
    try {
      if (this.devToolsOpened && this.window && !this.window.isDestroyed()) {
        this.window.webContents.closeDevTools();
      }
    } catch (error) {
      console.error('❌ Error closing DevTools:', error);
    }
  }

  /**
   * 切换 DevTools 显示状态
   */
  public toggleDevTools(): void {
    if (this.devToolsOpened) {
      this.closeDevTools();
    } else {
      this.openDevTools();
    }
  }

  /**
   * 检查是否为 DevTools 相关错误
   */
  private isDevToolsError(message: string): boolean {
    const devToolsErrorPatterns = [
      'devtools://',
      'Failed to fetch',
      'chrome-extension://',
      'extensions::',
      'Uncaught (in promise) TypeError: Failed to fetch',
      'net::ERR_FAILED',
      'DevTools',
    ];

    return devToolsErrorPatterns.some(pattern => 
      message.toLowerCase().includes(pattern.toLowerCase())
    );
  }

  /**
   * 获取日志级别名称
   */
  private getLevelName(level: number): string {
    switch (level) {
      case 0: return 'LOG';
      case 1: return 'WARNING';
      case 2: return 'ERROR';
      default: return 'UNKNOWN';
    }
  }

  /**
   * 检查 DevTools 是否已打开
   */
  public isDevToolsOpened(): boolean {
    return this.devToolsOpened;
  }

  /**
   * 清理资源
   */
  public dispose(): void {
    // 移除所有事件监听器
    this.window.webContents.removeAllListeners('devtools-opened');
    this.window.webContents.removeAllListeners('devtools-closed');
    this.window.webContents.removeAllListeners('console-message');
  }
}
