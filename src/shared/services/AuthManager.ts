import { AuthResult, CaptchaInfo } from '../types';
import { SmartEduClient } from './SmartEduClient';

/**
 * 认证管理器
 * 提供用户认证相关的功能
 */
export class AuthManager {
  private static instance: AuthManager;
  private smartEduClient: SmartEduClient;
  private currentUser: any = null;
  private authToken: string | null = null;

  private constructor() {
    this.smartEduClient = new SmartEduClient();
  }

  public static getInstance(): AuthManager {
    if (!AuthManager.instance) {
      AuthManager.instance = new AuthManager();
    }
    return AuthManager.instance;
  }

  /**
   * 用户登录
   */
  async login(username: string, password: string, captcha?: string): Promise<AuthResult> {
    try {
      console.log('🔐 开始用户登录');

      const electronAPI = (window as any).electronAPI;
      if (!electronAPI?.login) {
        // 如果没有专门的登录API，暂时返回游客模式
        console.log('⚠️ 登录API不可用，使用游客模式');
        this.currentUser = { type: 'guest', name: '游客用户' };
        return {
          success: true,
          user: this.currentUser,
          token: 'guest_token'
        };
      }

      const result = await electronAPI.login(username, password, captcha);

      if (result.success) {
        this.currentUser = result.user;
        this.authToken = result.token;
      }

      return result;
    } catch (error) {
      console.error('登录失败:', error);
      throw error;
    }
  }

  /**
   * 用户登出
   */
  async logout(): Promise<void> {
    try {
      console.log('🔐 用户登出');

      this.currentUser = null;
      this.authToken = null;

      // 清除本地存储的认证信息
      if (typeof localStorage !== 'undefined') {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_info');
      }
    } catch (error) {
      console.error('登出失败:', error);
    }
  }

  /**
   * 检查认证状态
   */
  async checkAuthStatus(): Promise<boolean> {
    try {
      // 如果有当前用户信息，认为已认证
      return !!this.currentUser;
    } catch (error) {
      console.error('检查认证状态失败:', error);
      return false;
    }
  }

  /**
   * 获取验证码
   */
  async getCaptcha(): Promise<CaptchaInfo> {
    try {
      console.log('🔐 获取验证码');

      const electronAPI = (window as any).electronAPI;
      if (!electronAPI?.getCaptcha) {
        // 返回空的验证码信息
        return {
          image: '',
          token: ''
        };
      }

      const captcha = await electronAPI.getCaptcha();
      return captcha;
    } catch (error) {
      console.error('获取验证码失败:', error);
      return {
        image: '',
        token: ''
      };
    }
  }

  /**
   * 获取当前用户
   */
  getCurrentUser(): any {
    return this.currentUser;
  }

  /**
   * 检查是否已登录
   */
  isLoggedIn(): boolean {
    return !!this.currentUser;
  }
}

export default AuthManager;
