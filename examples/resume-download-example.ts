/**
 * 视频下载断点续传功能使用示例
 * 
 * 本示例展示如何使用断点续传功能下载M3U8视频，
 * 包括暂停、恢复、网络中断处理等场景。
 */

import { DownloadManager } from '../src/shared/services/DownloadManager';
import { M3U8Downloader } from '../src/shared/services/M3U8Downloader';
import { FileOrganizer } from '../src/shared/services/FileOrganizer';
import { ResumeStateManager } from '../src/shared/services/ResumeStateManager';
import { NetworkMonitor } from '../src/shared/services/NetworkMonitor';
import { CourseResource } from '../src/shared/types';

async function resumeDownloadExample() {
  console.log('🚀 视频下载断点续传功能示例');

  // 1. 创建文件组织器
  const fileOrganizer = new FileOrganizer({
    basePath: './downloads',
    namingPattern: '{stage}-{grade}-{subject}-{title}',
    createSubfolders: true,
    groupBySubject: true,
    groupByGrade: true
  });

  // 2. 创建下载管理器（启用断点续传）
  const downloadManager = new DownloadManager(fileOrganizer, {
    maxConcurrentDownloads: 3,
    autoRetry: true,
    maxRetries: 3,
    retryDelay: 2000,
    enableNotifications: true
  });

  // 3. 创建示例视频资源
  const videoResource: CourseResource = {
    id: 'example-video-001',
    title: '小学数学-加法运算',
    type: 'video',
    url: 'https://example.com/math/addition.m3u8',
    metadata: {
      stage: '小学',
      grade: '三年级',
      subject: '数学',
      version: '人教版',
      volume: '上册',
      chapter: '第一章',
      lesson: '加法运算',
      duration: 1800 // 30分钟
    },
    requiresAuth: false
  };

  // 4. 设置事件监听器
  setupEventListeners(downloadManager);

  try {
    console.log('📥 开始下载视频...');
    
    // 5. 添加下载任务
    const task = downloadManager.addTask(videoResource);
    console.log(`✅ 任务已添加: ${task.id}`);

    // 6. 模拟用户操作：等待一段时间后暂停下载
    setTimeout(async () => {
      console.log('⏸️  暂停下载...');
      await downloadManager.pauseTask(task.id);
      
      // 7. 等待一段时间后恢复下载
      setTimeout(async () => {
        console.log('▶️  恢复下载...');
        await downloadManager.resumeTask(task.id);
      }, 3000);
    }, 5000);

    // 8. 等待下载完成
    await waitForTaskCompletion(downloadManager, task.id);
    
    console.log('🎉 下载完成！');

  } catch (error) {
    console.error('❌ 下载失败:', error);
  }
}

/**
 * 设置事件监听器
 */
function setupEventListeners(downloadManager: DownloadManager) {
  // 任务开始
  downloadManager.on('task-started', (task) => {
    console.log(`🏁 任务开始: ${task.resource.title}`);
  });

  // 下载进度
  downloadManager.on('task-progress', (task, progress) => {
    const status = progress.isResuming ? '恢复中' : '下载中';
    console.log(`📊 ${status}: ${task.resource.title} - ${progress.progress}% (${formatSpeed(progress.speed)})`);
    
    if (progress.segmentsCompleted && progress.segmentsTotal) {
      console.log(`   片段进度: ${progress.segmentsCompleted}/${progress.segmentsTotal}`);
    }
  });

  // 任务暂停
  downloadManager.on('task-paused', (task) => {
    console.log(`⏸️  任务已暂停: ${task.resource.title}`);
    if (task.canResume) {
      console.log(`   ✅ 支持断点续传 (已完成 ${task.progress}%)`);
    }
  });

  // 任务恢复
  downloadManager.on('task-resuming', (task) => {
    console.log(`▶️  任务恢复中: ${task.resource.title}`);
    if (task.resumeState) {
      console.log(`   📈 从 ${task.resumeState.downloadedSegments}/${task.resumeState.totalSegments} 片段继续`);
      console.log(`   🔄 第 ${task.resumeState.resumeCount + 1} 次恢复`);
    }
  });

  // 任务完成
  downloadManager.on('task-completed', (task, outputPath) => {
    console.log(`✅ 任务完成: ${task.resource.title}`);
    console.log(`   📁 文件路径: ${outputPath}`);
  });

  // 任务失败
  downloadManager.on('task-failed', (task, error) => {
    console.log(`❌ 任务失败: ${task.resource.title}`);
    console.log(`   💥 错误信息: ${error}`);
    console.log(`   🔄 重试次数: ${task.retryCount}/${task.maxRetries}`);
  });

  // 自动恢复成功
  downloadManager.on('auto-resume-success', (taskId) => {
    console.log(`🔄 网络恢复，任务自动恢复: ${taskId}`);
  });

  // 自动恢复失败
  downloadManager.on('auto-resume-failed', (taskId, error) => {
    console.log(`⚠️  自动恢复失败: ${taskId}, 错误: ${error}`);
  });
}

/**
 * 等待任务完成
 */
function waitForTaskCompletion(downloadManager: DownloadManager, taskId: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const checkTask = () => {
      const task = downloadManager.getTask(taskId);
      if (!task) {
        reject(new Error('任务不存在'));
        return;
      }

      switch (task.status) {
        case 'completed':
          resolve();
          break;
        case 'failed':
          reject(new Error(task.error || '下载失败'));
          break;
        default:
          // 继续等待
          setTimeout(checkTask, 1000);
          break;
      }
    };

    checkTask();
  });
}

/**
 * 格式化下载速度
 */
function formatSpeed(bytesPerSecond: number): string {
  if (bytesPerSecond === 0) return '0 B/s';
  
  const units = ['B/s', 'KB/s', 'MB/s', 'GB/s'];
  const unitIndex = Math.floor(Math.log(bytesPerSecond) / Math.log(1024));
  const speed = bytesPerSecond / Math.pow(1024, unitIndex);
  
  return `${speed.toFixed(1)} ${units[unitIndex]}`;
}

/**
 * 网络监控示例
 */
async function networkMonitorExample() {
  console.log('🌐 网络监控功能示例');

  const networkMonitor = new NetworkMonitor({
    checkInterval: 5000, // 5秒检查一次
    enableDetailedInfo: true,
    testUrls: [
      'https://www.google.com/generate_204',
      'https://www.baidu.com',
      'https://httpbin.org/status/200'
    ]
  });

  // 设置网络事件监听
  networkMonitor.on('network-online', (event) => {
    console.log('🟢 网络已连接');
    console.log(`   连接类型: ${event.status.connectionType}`);
    console.log(`   网络质量: ${networkMonitor.getNetworkQuality()}/100`);
  });

  networkMonitor.on('network-offline', (event) => {
    console.log('🔴 网络已断开');
  });

  networkMonitor.on('network-reconnected', (event) => {
    console.log('🔄 网络已重连');
    const strategy = networkMonitor.getRecommendedDownloadStrategy();
    console.log(`   推荐策略: 并发数=${strategy.maxConcurrent}, 块大小=${strategy.chunkSize}B`);
  });

  networkMonitor.on('network-speed-changed', (event) => {
    const quality = networkMonitor.getNetworkQuality();
    console.log(`📶 网络速度变化: ${event.type}, 质量评分: ${quality}/100`);
  });

  // 启动监控
  networkMonitor.startMonitoring();

  // 运行10秒后停止
  setTimeout(() => {
    networkMonitor.stopMonitoring();
    console.log('🛑 网络监控已停止');
  }, 10000);
}

/**
 * 断点续传状态管理示例
 */
async function resumeStateExample() {
  console.log('💾 断点续传状态管理示例');

  const resumeStateManager = new ResumeStateManager({
    stateDir: './download-states',
    enableChecksum: true,
    maxStateAge: 7 * 24 * 60 * 60 * 1000, // 7天
    autoCleanup: true
  });

  // 模拟保存断点续传状态
  const mockState = {
    taskId: 'example-task-001',
    resource: {
      id: 'video-001',
      title: '示例视频',
      type: 'video' as const,
      url: 'https://example.com/video.m3u8',
      metadata: {
        stage: '小学',
        grade: '三年级',
        subject: '数学',
        version: '人教版',
        volume: '上册'
      },
      requiresAuth: false
    },
    outputPath: './downloads/example-video.mp4',
    tempDir: './temp/example-task-001',
    totalSegments: 100,
    downloadedSegments: [
      {
        index: 0,
        url: 'https://example.com/segment0.ts',
        filePath: './temp/example-task-001/segment_000000.ts',
        size: 1024000,
        downloadedAt: new Date(),
        isComplete: true
      }
    ],
    failedSegments: [5, 10],
    lastUpdateTime: new Date(),
    playlistUrl: 'https://example.com/video.m3u8',
    baseUrl: 'https://example.com/'
  };

  try {
    // 保存状态
    await resumeStateManager.saveState(mockState);
    console.log('✅ 断点续传状态已保存');

    // 加载状态
    const loadedState = await resumeStateManager.loadState(mockState.taskId);
    if (loadedState) {
      console.log('📖 状态加载成功:');
      console.log(`   总片段数: ${loadedState.totalSegments}`);
      console.log(`   已下载: ${loadedState.downloadedSegments.length}`);
      console.log(`   失败片段: ${loadedState.failedSegments.length}`);
      console.log(`   进度: ${resumeStateManager.calculateProgress(mockState.taskId)}%`);
    }

    // 检查是否可以续传
    const canResume = await resumeStateManager.canResume(mockState.taskId);
    console.log(`🔄 可以续传: ${canResume ? '是' : '否'}`);

  } catch (error) {
    console.error('❌ 状态管理错误:', error);
  }
}

// 运行示例
async function runExamples() {
  console.log('🎬 开始运行断点续传功能示例\n');

  try {
    // 1. 网络监控示例
    await networkMonitorExample();
    console.log('\n');

    // 2. 状态管理示例
    await resumeStateExample();
    console.log('\n');

    // 3. 完整下载示例
    await resumeDownloadExample();

  } catch (error) {
    console.error('示例运行失败:', error);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  runExamples();
}

export {
  resumeDownloadExample,
  networkMonitorExample,
  resumeStateExample,
  runExamples
};
