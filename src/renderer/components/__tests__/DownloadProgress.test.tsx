import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { DownloadProgress } from '../DownloadProgress';
import { DownloadTask, CourseResource } from '../../../shared/types';

describe('DownloadProgress', () => {
  const mockResource: CourseResource = {
    id: 'resource-1',
    title: '测试教材',
    type: 'textbook',
    url: 'https://example.com/textbook.pdf',
    metadata: {
      stage: '小学',
      grade: '三年级',
      subject: '数学',
      version: '人教版',
      volume: '上册',
      fileSize: 1024000 // 1MB
    },
    requiresAuth: false,
    accessLevel: 'public'
  };

  const createMockTask = (overrides: Partial<DownloadTask> = {}): DownloadTask => ({
    id: 'task-1',
    resource: mockResource,
    status: 'pending',
    progress: 0,
    speed: 0,
    estimatedTime: 0,
    requiresAuth: false,
    createdAt: new Date('2023-01-01T10:00:00Z'),
    updatedAt: new Date(),
    outputPath: '/test/output',
    retryCount: 0,
    maxRetries: 3,
    ...overrides
  });

  const mockProps = {
    onPause: jest.fn(),
    onResume: jest.fn(),
    onCancel: jest.fn(),
    onRetry: jest.fn(),
    onClear: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('基本渲染', () => {
    it('应该渲染任务标题', () => {
      const task = createMockTask();
      render(<DownloadProgress task={task} {...mockProps} />);
      
      expect(screen.getByText('测试教材')).toBeInTheDocument();
    });

    it('应该显示任务状态', () => {
      const task = createMockTask({ status: 'downloading' });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      expect(screen.getByText('下载中')).toBeInTheDocument();
    });

    it('应该显示进度条', () => {
      const task = createMockTask({ progress: 50 });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      const progressBar = document.querySelector('.ant-progress-bg');
      expect(progressBar).toHaveStyle('width: 50%');
    });
  });

  describe('不同状态显示', () => {
    it('应该显示等待中状态', () => {
      const task = createMockTask({ status: 'pending' });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      expect(screen.getByText('等待中')).toBeInTheDocument();
    });

    it('应该显示下载中状态和速度信息', () => {
      const task = createMockTask({ 
        status: 'downloading',
        progress: 25,
        speed: 1024000, // 1MB/s
        estimatedTime: 30 // 30 seconds
      });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      expect(screen.getByText('下载中')).toBeInTheDocument();
      expect(screen.getByText(/1.00 MB\/s/)).toBeInTheDocument();
      expect(screen.getByText(/剩余 0:30/)).toBeInTheDocument();
    });

    it('应该显示已暂停状态', () => {
      const task = createMockTask({ status: 'paused' });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      expect(screen.getByText('已暂停')).toBeInTheDocument();
    });

    it('应该显示已完成状态', () => {
      const task = createMockTask({ 
        status: 'completed',
        progress: 100,
        createdAt: new Date('2023-01-01T10:00:00Z')
      });
      
      // Mock Date.now() to return a fixed time
      const mockNow = new Date('2023-01-01T10:02:00Z').getTime(); // 2 minutes later
      jest.spyOn(Date, 'now').mockReturnValue(mockNow);
      
      render(<DownloadProgress task={task} {...mockProps} />);
      
      expect(screen.getByText('已完成')).toBeInTheDocument();
      expect(screen.getByText(/下载完成 · 用时 2:00/)).toBeInTheDocument();
      
      jest.restoreAllMocks();
    });

    it('应该显示失败状态和错误信息', () => {
      const task = createMockTask({ 
        status: 'failed',
        error: '网络连接超时'
      });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      expect(screen.getByText('下载失败')).toBeInTheDocument();
      expect(screen.getByText(/失败原因: 网络连接超时/)).toBeInTheDocument();
    });

    it('应该显示已取消状态', () => {
      const task = createMockTask({ status: 'cancelled' });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      expect(screen.getByText('已取消')).toBeInTheDocument();
    });
  });

  describe('重试信息显示', () => {
    it('应该显示重试次数', () => {
      const task = createMockTask({ 
        status: 'downloading',
        retryCount: 2,
        maxRetries: 3
      });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      expect(screen.getByText(/重试 2\/3/)).toBeInTheDocument();
    });

    it('应该在紧凑模式下显示重试信息', () => {
      const task = createMockTask({ 
        status: 'downloading',
        retryCount: 1,
        speed: 1024000,
        estimatedTime: 30
      });
      render(<DownloadProgress task={task} {...mockProps} compact={true} />);
      
      expect(screen.getByText(/重试 1/)).toBeInTheDocument();
    });
  });

  describe('文件大小和进度显示', () => {
    it('应该显示正确的文件大小信息', () => {
      const task = createMockTask({ 
        progress: 50,
        resource: {
          ...mockResource,
          metadata: {
            ...mockResource.metadata,
            fileSize: 2048000 // 2MB
          }
        }
      });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      // 50% of 2MB = 1MB downloaded
      expect(screen.getByText(/1.00 MB \/ 2.00 MB/)).toBeInTheDocument();
    });

    it('应该处理未知文件大小', () => {
      const task = createMockTask({ 
        progress: 50,
        resource: {
          ...mockResource,
          metadata: {
            ...mockResource.metadata,
            fileSize: undefined
          }
        }
      });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      expect(screen.getByText(/0 B \/ 0 B/)).toBeInTheDocument();
    });
  });

  describe('操作按钮', () => {
    it('应该显示下载中任务的暂停和取消按钮', () => {
      const task = createMockTask({ status: 'downloading' });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      expect(screen.getByLabelText('暂停下载')).toBeInTheDocument();
      expect(screen.getByLabelText('取消下载')).toBeInTheDocument();
    });

    it('应该显示暂停任务的继续和取消按钮', () => {
      const task = createMockTask({ status: 'paused' });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      expect(screen.getByLabelText('继续下载')).toBeInTheDocument();
      expect(screen.getByLabelText('取消下载')).toBeInTheDocument();
    });

    it('应该显示失败任务的重试和清除按钮', () => {
      const task = createMockTask({ status: 'failed' });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      expect(screen.getByLabelText('重试下载')).toBeInTheDocument();
      expect(screen.getByLabelText('清除任务')).toBeInTheDocument();
    });

    it('应该显示完成任务的清除按钮', () => {
      const task = createMockTask({ status: 'completed' });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      expect(screen.getByLabelText('清除任务')).toBeInTheDocument();
    });

    it('应该在禁用操作时不显示按钮', () => {
      const task = createMockTask({ status: 'downloading' });
      render(<DownloadProgress task={task} {...mockProps} showActions={false} />);
      
      expect(screen.queryByLabelText('暂停下载')).not.toBeInTheDocument();
      expect(screen.queryByLabelText('取消下载')).not.toBeInTheDocument();
    });
  });

  describe('按钮点击事件', () => {
    it('应该调用暂停回调', () => {
      const task = createMockTask({ status: 'downloading' });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      fireEvent.click(screen.getByLabelText('暂停下载'));
      expect(mockProps.onPause).toHaveBeenCalledWith('task-1');
    });

    it('应该调用继续回调', () => {
      const task = createMockTask({ status: 'paused' });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      fireEvent.click(screen.getByLabelText('继续下载'));
      expect(mockProps.onResume).toHaveBeenCalledWith('task-1');
    });

    it('应该调用取消回调', () => {
      const task = createMockTask({ status: 'downloading' });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      fireEvent.click(screen.getByLabelText('取消下载'));
      expect(mockProps.onCancel).toHaveBeenCalledWith('task-1');
    });

    it('应该调用重试回调', () => {
      const task = createMockTask({ status: 'failed' });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      fireEvent.click(screen.getByLabelText('重试下载'));
      expect(mockProps.onRetry).toHaveBeenCalledWith('task-1');
    });

    it('应该调用清除回调', () => {
      const task = createMockTask({ status: 'completed' });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      fireEvent.click(screen.getByLabelText('清除任务'));
      expect(mockProps.onClear).toHaveBeenCalledWith('task-1');
    });
  });

  describe('紧凑模式', () => {
    it('应该在紧凑模式下使用不同的布局', () => {
      const task = createMockTask({ status: 'downloading', progress: 50 });
      render(<DownloadProgress task={task} {...mockProps} compact={true} />);
      
      // 紧凑模式应该有不同的样式
      const container = screen.getByText('测试教材').closest('div');
      expect(container).toHaveStyle('padding: 8px 0');
    });

    it('应该在紧凑模式下显示简化的信息', () => {
      const task = createMockTask({ 
        status: 'downloading',
        progress: 75,
        speed: 2048000,
        estimatedTime: 15
      });
      render(<DownloadProgress task={task} {...mockProps} compact={true} />);
      
      expect(screen.getByText(/2.00 MB\/s · 剩余 0:15/)).toBeInTheDocument();
    });
  });

  describe('元数据标签', () => {
    it('应该在完整模式下显示资源元数据标签', () => {
      const task = createMockTask();
      render(<DownloadProgress task={task} {...mockProps} compact={false} />);
      
      expect(screen.getByText('小学')).toBeInTheDocument();
      expect(screen.getByText('三年级')).toBeInTheDocument();
      expect(screen.getByText('数学')).toBeInTheDocument();
      expect(screen.getByText('人教版')).toBeInTheDocument();
      expect(screen.getByText('上册')).toBeInTheDocument();
    });

    it('应该在紧凑模式下不显示元数据标签', () => {
      const task = createMockTask();
      render(<DownloadProgress task={task} {...mockProps} compact={true} />);
      
      expect(screen.queryByText('小学')).not.toBeInTheDocument();
      expect(screen.queryByText('三年级')).not.toBeInTheDocument();
    });
  });

  describe('进度条状态', () => {
    it('应该为失败任务显示异常状态进度条', () => {
      const task = createMockTask({ status: 'failed', progress: 30 });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      const progressBar = document.querySelector('.ant-progress-status-exception');
      expect(progressBar).toBeInTheDocument();
    });

    it('应该为完成任务显示成功状态进度条', () => {
      const task = createMockTask({ status: 'completed', progress: 100 });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      const progressBar = document.querySelector('.ant-progress-status-success');
      expect(progressBar).toBeInTheDocument();
    });

    it('应该为下载中任务显示活动状态进度条', () => {
      const task = createMockTask({ status: 'downloading', progress: 45 });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      const progressBar = document.querySelector('.ant-progress-status-active');
      expect(progressBar).toBeInTheDocument();
    });
  });

  describe('时间格式化', () => {
    it('应该正确格式化小于1小时的时间', () => {
      const task = createMockTask({ 
        status: 'downloading',
        estimatedTime: 125 // 2:05
      });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      expect(screen.getByText(/剩余 2:05/)).toBeInTheDocument();
    });

    it('应该正确格式化超过1小时的时间', () => {
      const task = createMockTask({ 
        status: 'downloading',
        estimatedTime: 3665 // 1:01:05
      });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      expect(screen.getByText(/剩余 1:01:05/)).toBeInTheDocument();
    });

    it('应该处理无效的时间值', () => {
      const task = createMockTask({ 
        status: 'downloading',
        estimatedTime: Infinity
      });
      render(<DownloadProgress task={task} {...mockProps} />);
      
      expect(screen.getByText(/剩余 --/)).toBeInTheDocument();
    });
  });

  describe('错误信息显示', () => {
    it('应该在紧凑模式下显示错误信息', () => {
      const task = createMockTask({ 
        status: 'failed',
        error: '下载链接已失效'
      });
      render(<DownloadProgress task={task} {...mockProps} compact={true} />);
      
      expect(screen.getByText(/错误: 下载链接已失效/)).toBeInTheDocument();
    });

    it('应该在完整模式下显示错误信息', () => {
      const task = createMockTask({ 
        status: 'failed',
        error: '磁盘空间不足'
      });
      render(<DownloadProgress task={task} {...mockProps} compact={false} />);
      
      expect(screen.getByText(/错误: 磁盘空间不足/)).toBeInTheDocument();
    });
  });
});