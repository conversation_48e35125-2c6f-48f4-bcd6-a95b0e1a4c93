import { DownloadManagerV2, DownloadManagerConfig } from '../DownloadManagerV2';
import { FileOrganizer } from '../FileOrganizer';
import { TaskPriority } from '../DownloadQueue';
import { CourseResource } from '../../types';

// Mock dependencies
jest.mock('../TextbookDownloader');
jest.mock('../M3U8Downloader');
jest.mock('../FileOrganizer');

describe('DownloadManagerV2', () => {
  let downloadManager: DownloadManagerV2;
  let fileOrganizer: FileOrganizer;
  let config: Partial<DownloadManagerConfig>;
  let mockResource: CourseResource;

  beforeEach(() => {
    fileOrganizer = new FileOrganizer({
      basePath: '/test/downloads',
      namingPattern: '{title}',
      createSubfolders: false,
      groupBySubject: false,
      groupByGrade: false
    });

    config = {
      maxConcurrentDownloads: 2,
      autoRetry: true,
      maxRetries: 3,
      retryDelay: 1000,
      enableNotifications: false,
      enablePriority: true,
      persistState: false
    };

    downloadManager = new DownloadManagerV2(fileOrganizer, config);

    mockResource = {
      id: 'resource1',
      title: '测试资源',
      type: 'textbook',
      url: 'https://example.com/resource1',
      requiresAuth: false,
      accessLevel: 'public',
      metadata: {
        stage: '小学',
        grade: '一年级',
        subject: '语文',
        version: '人教版',
        volume: '上册',
        fileSize: 1024
      }
    };
  });

  afterEach(() => {
    downloadManager.destroy();
  });

  describe('addTask', () => {
    it('should add a new download task', () => {
      const task = downloadManager.addTask(mockResource);

      expect(task).toBeDefined();
      expect(task.resource).toEqual(mockResource);
      expect(task.status).toBe('pending');
      expect(task.progress).toBe(0);
      expect(task.retryCount).toBe(0);
      expect(task.maxRetries).toBe(config.maxRetries);
    });

    it('should add task with specified priority', () => {
      const task = downloadManager.addTask(mockResource, TaskPriority.HIGH);
      
      expect(task).toBeDefined();
      // 验证任务被添加到队列中（通过检查任务是否存在）
      expect(downloadManager.getTask(task.id)).toBe(task);
    });

    it('should throw error when adding duplicate task', () => {
      downloadManager.addTask(mockResource);
      
      expect(() => {
        downloadManager.addTask(mockResource);
      }).toThrow('该资源已在下载队列中');
    });

    it('should emit task-added event', (done) => {
      downloadManager.on('task-added', (task) => {
        expect(task.resource).toEqual(mockResource);
        done();
      });

      downloadManager.addTask(mockResource);
    });
  });

  describe('addBatchTasks', () => {
    it('should add multiple tasks', () => {
      const resources = [
        { ...mockResource, id: 'resource1' },
        { ...mockResource, id: 'resource2' },
        { ...mockResource, id: 'resource3' }
      ];

      const tasks = downloadManager.addBatchTasks(resources, TaskPriority.NORMAL);

      expect(tasks).toHaveLength(3);
      expect(downloadManager.getAllTasks()).toHaveLength(3);
    });

    it('should skip duplicate resources in batch', () => {
      downloadManager.addTask(mockResource);
      
      const resources = [
        mockResource, // 重复资源
        { ...mockResource, id: 'resource2' },
        { ...mockResource, id: 'resource3' }
      ];

      const tasks = downloadManager.addBatchTasks(resources);

      expect(tasks).toHaveLength(2); // 只添加了2个新任务
      expect(downloadManager.getAllTasks()).toHaveLength(3); // 总共3个任务
    });
  });

  describe('task control', () => {
    let taskId: string;

    beforeEach(() => {
      const task = downloadManager.addTask(mockResource);
      taskId = task.id;
    });

    it('should pause downloading task', async () => {
      // 模拟任务开始下载
      const task = downloadManager.getTask(taskId)!;
      task.status = 'downloading';

      await downloadManager.pauseTask(taskId);

      expect(task.status).toBe('paused');
    });

    it('should resume paused task', async () => {
      const task = downloadManager.getTask(taskId)!;
      task.status = 'paused';
      task.canResume = false; // 非视频任务

      await downloadManager.resumeTask(taskId);

      expect(task.status).toBe('pending');
    });

    it('should cancel task', () => {
      downloadManager.cancelTask(taskId);

      const task = downloadManager.getTask(taskId)!;
      expect(task.status).toBe('cancelled');
    });

    it('should retry failed task', () => {
      const task = downloadManager.getTask(taskId)!;
      task.status = 'failed';
      task.error = 'Test error';

      downloadManager.retryTask(taskId);

      expect(task.status).toBe('pending');
      expect(task.error).toBeUndefined();
      expect(task.retryCount).toBe(0);
    });

    it('should clear task', () => {
      downloadManager.clearTask(taskId);

      expect(downloadManager.getTask(taskId)).toBeUndefined();
    });

    it('should throw error for non-existent task operations', () => {
      expect(() => downloadManager.pauseTask('nonexistent')).rejects.toThrow('任务不存在');
      expect(() => downloadManager.resumeTask('nonexistent')).rejects.toThrow('任务不存在');
      expect(() => downloadManager.cancelTask('nonexistent')).toThrow('任务不存在');
      expect(() => downloadManager.retryTask('nonexistent')).toThrow('任务不存在');
    });
  });

  describe('batch operations', () => {
    beforeEach(() => {
      // 添加多个任务
      downloadManager.addTask({ ...mockResource, id: 'resource1' });
      downloadManager.addTask({ ...mockResource, id: 'resource2' });
      downloadManager.addTask({ ...mockResource, id: 'resource3' });
    });

    it('should pause all tasks', async () => {
      // 模拟一些任务正在下载
      const tasks = downloadManager.getAllTasks();
      tasks[0].status = 'downloading';
      tasks[1].status = 'downloading';

      await downloadManager.pauseAll();

      const downloadingTasks = downloadManager.getTasksByStatus('downloading');
      expect(downloadingTasks).toHaveLength(0);
    });

    it('should resume all paused tasks', () => {
      // 模拟一些任务被暂停
      const tasks = downloadManager.getAllTasks();
      tasks[0].status = 'paused';
      tasks[1].status = 'paused';

      downloadManager.resumeAll();

      // 任务应该被重新加入队列（状态变为pending）
      const pausedTasks = downloadManager.getTasksByStatus('paused');
      expect(pausedTasks.length).toBeLessThan(2);
    });

    it('should cancel all active tasks', () => {
      // 模拟一些任务处于活动状态
      const tasks = downloadManager.getAllTasks();
      tasks[0].status = 'downloading';
      tasks[1].status = 'pending';

      downloadManager.cancelAll();

      const activeTasks = [
        ...downloadManager.getTasksByStatus('downloading'),
        ...downloadManager.getTasksByStatus('pending')
      ];
      expect(activeTasks).toHaveLength(0);
    });

    it('should clear completed tasks', () => {
      // 模拟一些任务已完成
      const tasks = downloadManager.getAllTasks();
      tasks[0].status = 'completed';
      tasks[1].status = 'completed';

      downloadManager.clearCompletedTasks();

      const completedTasks = downloadManager.getTasksByStatus('completed');
      expect(completedTasks).toHaveLength(0);
      expect(downloadManager.getAllTasks()).toHaveLength(1); // 只剩下未完成的任务
    });
  });

  describe('task queries', () => {
    beforeEach(() => {
      // 添加不同状态的任务
      const task1 = downloadManager.addTask({ ...mockResource, id: 'resource1' });
      const task2 = downloadManager.addTask({ ...mockResource, id: 'resource2' });
      const task3 = downloadManager.addTask({ ...mockResource, id: 'resource3' });

      task1.status = 'downloading';
      task2.status = 'completed';
      task3.status = 'failed';
    });

    it('should get task by id', () => {
      const tasks = downloadManager.getAllTasks();
      const task = downloadManager.getTask(tasks[0].id);

      expect(task).toBeDefined();
      expect(task!.id).toBe(tasks[0].id);
    });

    it('should get all tasks', () => {
      const tasks = downloadManager.getAllTasks();
      expect(tasks).toHaveLength(3);
    });

    it('should get tasks by status', () => {
      const downloadingTasks = downloadManager.getTasksByStatus('downloading');
      const completedTasks = downloadManager.getTasksByStatus('completed');
      const failedTasks = downloadManager.getTasksByStatus('failed');

      expect(downloadingTasks).toHaveLength(1);
      expect(completedTasks).toHaveLength(1);
      expect(failedTasks).toHaveLength(1);
    });
  });

  describe('statistics', () => {
    beforeEach(() => {
      // 添加不同状态的任务
      const task1 = downloadManager.addTask({ ...mockResource, id: 'resource1' });
      const task2 = downloadManager.addTask({ ...mockResource, id: 'resource2' });
      const task3 = downloadManager.addTask({ ...mockResource, id: 'resource3' });
      const task4 = downloadManager.addTask({ ...mockResource, id: 'resource4' });

      task1.status = 'downloading';
      task2.status = 'completed';
      task3.status = 'failed';
      task4.status = 'paused';
    });

    it('should provide accurate statistics', () => {
      const stats = downloadManager.getStats();

      expect(stats.totalTasks).toBe(4);
      expect(stats.activeTasks).toBe(1);
      expect(stats.completedTasks).toBe(1);
      expect(stats.failedTasks).toBe(1);
      expect(stats.pausedTasks).toBe(1);
      expect(stats.pendingTasks).toBe(0);
    });

    it('should calculate average speed from active tasks', () => {
      const tasks = downloadManager.getAllTasks();
      const downloadingTask = tasks.find(t => t.status === 'downloading')!;
      downloadingTask.speed = 1024; // 1KB/s

      const stats = downloadManager.getStats();
      expect(stats.averageSpeed).toBe(1024);
    });

    it('should emit stats-updated event', (done) => {
      downloadManager.on('stats-updated', (stats) => {
        expect(stats.totalTasks).toBeGreaterThan(0);
        done();
      });

      // 添加任务应该触发统计更新
      downloadManager.addTask({ ...mockResource, id: 'new-resource' });
    });
  });

  describe('configuration', () => {
    it('should update configuration', () => {
      const newConfig: Partial<DownloadManagerConfig> = {
        maxConcurrentDownloads: 5,
        autoRetry: false,
        enableNotifications: true
      };

      downloadManager.updateConfig(newConfig);
      const currentConfig = downloadManager.getConfig();

      expect(currentConfig.maxConcurrentDownloads).toBe(5);
      expect(currentConfig.autoRetry).toBe(false);
      expect(currentConfig.enableNotifications).toBe(true);
    });

    it('should emit concurrency-changed event when max concurrent changes', (done) => {
      downloadManager.on('concurrency-changed', (oldValue, newValue) => {
        expect(oldValue).toBe(2);
        expect(newValue).toBe(4);
        done();
      });

      downloadManager.updateConfig({ maxConcurrentDownloads: 4 });
    });
  });

  describe('event handling', () => {
    it('should emit task events based on state changes', (done) => {
      let eventCount = 0;
      const expectedEvents = ['task-added', 'task-started', 'task-completed'];

      expectedEvents.forEach(event => {
        downloadManager.on(event as any, () => {
          eventCount++;
          if (eventCount === expectedEvents.length) {
            done();
          }
        });
      });

      // 添加任务
      const task = downloadManager.addTask(mockResource);

      // 模拟状态变化
      setTimeout(() => {
        task.status = 'downloading';
        downloadManager.emit('task-started', task);
        
        setTimeout(() => {
          task.status = 'completed';
          downloadManager.emit('task-completed', task);
        }, 10);
      }, 10);
    });

    it('should emit queue-empty event', (done) => {
      downloadManager.on('queue-empty', () => {
        done();
      });

      // 模拟队列为空的情况
      downloadManager.emit('queue-empty');
    });
  });

  describe('error handling', () => {
    it('should handle download errors gracefully', () => {
      const task = downloadManager.addTask(mockResource);
      
      // 模拟下载错误
      task.status = 'failed';
      task.error = 'Network error';
      task.retryCount = 1;

      expect(task.status).toBe('failed');
      expect(task.error).toBe('Network error');
      expect(task.retryCount).toBe(1);
    });

    it('should handle invalid task operations', () => {
      expect(() => {
        downloadManager.getTask('nonexistent');
      }).not.toThrow();

      expect(downloadManager.getTask('nonexistent')).toBeUndefined();
    });
  });
});
