# 错误处理和重试机制使用指南

本指南介绍如何在智慧教育下载器中使用错误处理和重试机制。

## 概述

系统提供了两个主要的错误处理组件：

1. **RetryManager** - 提供智能重试机制
2. **ErrorRecoveryManager** - 提供错误恢复策略
3. **ErrorDisplay** - 提供用户友好的错误显示界面

## RetryManager 使用方法

### 基本用法

```typescript
import { RetryManager } from '../services/RetryManager';
import { NetworkError } from '../types';

const retryManager = new RetryManager({
  maxRetries: 3,
  baseDelay: 1000,
  strategy: 'exponential'
});

// 执行带重试的操作
const result = await retryManager.executeWithRetry(async () => {
  // 你的操作代码
  const response = await fetch('https://api.example.com/data');
  if (!response.ok) {
    throw new NetworkError('网络请求失败');
  }
  return response.json();
});

if (result.success) {
  console.log('操作成功:', result.result);
} else {
  console.error('操作失败:', result.error);
}
```

### 重试策略

支持三种重试策略：

1. **指数退避** (`exponential`) - 延迟时间呈指数增长
2. **线性退避** (`linear`) - 延迟时间线性增长
3. **固定延迟** (`fixed`) - 固定延迟时间

```typescript
const retryManager = new RetryManager({
  strategy: 'exponential',
  baseDelay: 1000,      // 基础延迟 1 秒
  backoffFactor: 2,     // 退避因子
  maxDelay: 30000,      // 最大延迟 30 秒
  jitter: true          // 启用抖动
});
```

### 函数包装器

```typescript
// 将普通函数包装为带重试的函数
const downloadFile = async (url: string) => {
  // 下载逻辑
};

const retryableDownload = retryManager.wrap(downloadFile);

// 使用包装后的函数
const result = await retryableDownload('https://example.com/file.mp4');
```

### 批量操作

```typescript
const operations = [
  () => downloadFile('file1.mp4'),
  () => downloadFile('file2.mp4'),
  () => downloadFile('file3.mp4')
];

// 串行批量重试
const results = await retryManager.executeBatchWithRetry(operations);

// 并行批量重试（限制并发数）
const parallelResults = await retryManager.executeBatchParallelWithRetry(
  operations,
  { maxRetries: 2 },
  3 // 最大并发数
);
```

## ErrorRecoveryManager 使用方法

### 基本用法

```typescript
import { ErrorRecoveryManager } from '../services/ErrorRecoveryManager';
import { RetryManager } from '../services/RetryManager';

const retryManager = new RetryManager();
const recoveryManager = new ErrorRecoveryManager(retryManager);

// 尝试恢复错误
const result = await recoveryManager.attemptRecovery(
  new NetworkError('网络连接失败'),
  async () => {
    // 原始操作
    return await performNetworkOperation();
  }
);
```

### 自定义恢复动作

```typescript
import { ErrorType, RecoveryAction } from '../types';

// 添加自定义恢复动作
const customRecoveryAction: RecoveryAction = {
  strategy: 'fallback',
  description: '切换到备用服务器',
  execute: async () => {
    // 切换服务器逻辑
    switchToBackupServer();
    return true;
  },
  condition: (error) => error.message.includes('服务器不可用'),
  priority: 1
};

recoveryManager.addRecoveryAction(ErrorType.NETWORK_ERROR, customRecoveryAction);
```

### 自动恢复包装器

```typescript
// 包装函数以支持自动恢复
const originalFunction = async (data: any) => {
  // 可能失败的操作
};

const autoRecoveryFunction = recoveryManager.wrapWithAutoRecovery(originalFunction);

// 使用包装后的函数，自动处理错误恢复
const result = await autoRecoveryFunction(inputData);
```

### 监听恢复事件

```typescript
recoveryManager.on('recovery-started', (error, strategy) => {
  console.log(`开始恢复，策略: ${strategy}`);
});

recoveryManager.on('recovery-success', (error, strategy, result) => {
  console.log(`恢复成功，策略: ${strategy}`);
});

recoveryManager.on('recovery-failed', (error, strategy, reason) => {
  console.log(`恢复失败，策略: ${strategy}，原因: ${reason}`);
});

recoveryManager.on('cooldown-started', (duration) => {
  console.log(`进入冷却期，持续时间: ${duration}ms`);
});
```

## ErrorDisplay 组件使用方法

### 基本用法

```tsx
import React from 'react';
import { ErrorDisplay } from '../components/ErrorDisplay';
import { NetworkError } from '../types';

const MyComponent: React.FC = () => {
  const [error, setError] = useState<Error | null>(null);
  const [retryState, setRetryState] = useState<RetryState | undefined>();

  const handleRetry = () => {
    // 重试逻辑
    performOperation();
  };

  const handleDismiss = () => {
    setError(null);
  };

  return (
    <div>
      {error && (
        <ErrorDisplay
          error={error}
          retryState={retryState}
          onRetry={handleRetry}
          onDismiss={handleDismiss}
          showDetails={true}
        />
      )}
    </div>
  );
};
```

### 错误类型和建议

ErrorDisplay 组件会根据错误类型自动显示相应的解决建议：

- **网络错误**: 检查网络连接、切换网络环境等
- **API错误**: 检查服务器状态、稍后重试等
- **文件错误**: 检查磁盘空间、文件权限等
- **下载错误**: 重新开始下载、单个文件下载等
- **认证错误**: 重新登录、检查账户状态等

## 配置选项

### RetryManager 配置

```typescript
interface RetryConfig {
  maxRetries: number;           // 最大重试次数
  baseDelay: number;           // 基础延迟时间（毫秒）
  maxDelay: number;            // 最大延迟时间（毫秒）
  strategy: RetryStrategy;     // 重试策略
  backoffFactor: number;       // 退避因子
  jitter: boolean;             // 是否启用抖动
  retryableErrors: ErrorType[]; // 可重试的错误类型
  timeoutMultiplier: number;   // 超时倍数
}
```

### ErrorRecoveryManager 配置

```typescript
interface RecoveryConfig {
  maxRecoveryAttempts: number;  // 最大恢复尝试次数
  recoveryTimeout: number;      // 恢复操作超时时间
  enableAutoRecovery: boolean;  // 是否启用自动恢复
  fallbackStrategies: RecoveryStrategy[]; // 备用策略
  errorThreshold: number;       // 错误阈值
  cooldownPeriod: number;       // 冷却期时长
}
```

## 预设配置

RetryManager 提供了几种预设配置：

```typescript
// 激进重试 - 更多重试次数，更短延迟
const aggressiveConfig = RetryManager.createPresetConfig('aggressive');

// 保守重试 - 较少重试次数，较长延迟
const conservativeConfig = RetryManager.createPresetConfig('conservative');

// 快速重试 - 线性策略，短延迟
const fastConfig = RetryManager.createPresetConfig('fast');

// 健壮重试 - 更多重试次数，包含更多错误类型
const robustConfig = RetryManager.createPresetConfig('robust');

const retryManager = new RetryManager(aggressiveConfig);
```

## 最佳实践

### 1. 选择合适的重试策略

- **指数退避**: 适用于网络请求，避免服务器过载
- **线性退避**: 适用于文件操作，提供稳定的重试间隔
- **固定延迟**: 适用于简单的重试场景

### 2. 设置合理的重试次数

- 网络操作: 3-5 次
- 文件操作: 2-3 次
- API 调用: 3-4 次

### 3. 使用错误分类

```typescript
import { SmartEduError, ErrorType } from '../types';

// 创建具体的错误类型
throw new SmartEduError(
  ErrorType.NETWORK_ERROR,
  '网络连接失败',
  { statusCode: 500, url: 'https://api.example.com' },
  true // 可重试
);
```

### 4. 监听事件进行调试

```typescript
retryManager.on('retry-attempt', (state, error) => {
  console.log(`重试第 ${state.attempt} 次: ${error.message}`);
});

retryManager.on('retry-exhausted', (state) => {
  console.error(`重试耗尽，总尝试次数: ${state.attempt}`);
});
```

### 5. 使用统计信息优化配置

```typescript
const stats = retryManager.getErrorStats(retryStates);
console.log('成功率:', stats.successRate);
console.log('平均尝试次数:', stats.averageAttempts);
console.log('错误分布:', stats.errorsByType);
```

## 示例代码

完整的使用示例请参考 `src/shared/examples/error-handling-demo.ts` 文件。

## 注意事项

1. **避免无限重试**: 始终设置合理的最大重试次数
2. **处理不可重试的错误**: 某些错误（如认证失败）不应该重试
3. **监控资源使用**: 重试机制可能增加系统负载
4. **用户体验**: 在 UI 中提供清晰的错误信息和操作选项
5. **日志记录**: 记录重试和恢复过程以便调试

## 故障排除

### 常见问题

1. **重试次数过多**: 检查 `maxRetries` 配置
2. **延迟时间过长**: 调整 `baseDelay` 和 `maxDelay`
3. **内存泄漏**: 确保正确清理事件监听器
4. **冷却期过长**: 调整 `cooldownPeriod` 配置

### 调试技巧

1. 启用详细日志
2. 监听所有事件
3. 检查错误统计信息
4. 使用较短的延迟进行测试