#!/usr/bin/env node

const fs = require('fs-extra');
const path = require('path');

/**
 * 打包配置测试脚本
 * 专门测试 electron-builder 配置的正确性
 */
class PackagingTester {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.testResults = [];
  }

  /**
   * 运行打包配置测试
   */
  async runPackagingTests() {
    console.log('📦 开始打包配置测试...\n');

    try {
      await this.testPackageJsonConfig();
      await this.testBuildResources();
      await this.testPlatformConfigs();
      
      this.printResults();
    } catch (error) {
      console.error('❌ 打包配置测试失败:', error.message);
      process.exit(1);
    }
  }

  /**
   * 测试 package.json 中的构建配置
   */
  async testPackageJsonConfig() {
    console.log('⚙️  测试 package.json 构建配置...');

    try {
      const packageJson = await fs.readJson(path.join(this.projectRoot, 'package.json'));
      
      // 检查基本信息
      if (packageJson.name) {
        this.addResult('✅', `应用名称: ${packageJson.name}`);
      } else {
        this.addResult('❌', '缺少应用名称');
      }

      if (packageJson.version) {
        this.addResult('✅', `版本号: ${packageJson.version}`);
      } else {
        this.addResult('❌', '缺少版本号');
      }

      if (packageJson.description) {
        this.addResult('✅', `应用描述: ${packageJson.description}`);
      } else {
        this.addResult('⚠️', '缺少应用描述');
      }

      // 检查 build 配置
      if (packageJson.build) {
        this.addResult('✅', '存在 build 配置');
        
        const build = packageJson.build;
        
        // 检查必要字段
        const requiredFields = {
          'appId': '应用ID',
          'productName': '产品名称',
          'directories': '目录配置',
          'files': '文件配置'
        };

        for (const [field, description] of Object.entries(requiredFields)) {
          if (build[field]) {
            this.addResult('✅', `${description}: 已配置`);
          } else {
            this.addResult('❌', `${description}: 缺失`);
          }
        }

        // 检查平台配置
        const platforms = ['mac', 'win', 'linux'];
        for (const platform of platforms) {
          if (build[platform]) {
            this.addResult('✅', `${platform} 平台配置: 已配置`);
            
            // 检查平台特定配置
            const platformConfig = build[platform];
            if (platformConfig.target) {
              this.addResult('✅', `  ${platform} 目标格式: ${JSON.stringify(platformConfig.target)}`);
            }
            if (platformConfig.icon) {
              this.addResult('✅', `  ${platform} 图标: ${platformConfig.icon}`);
            }
          } else {
            this.addResult('⚠️', `${platform} 平台配置: 缺失`);
          }
        }

        // 检查发布配置
        if (build.publish) {
          this.addResult('✅', `发布配置: ${JSON.stringify(build.publish)}`);
        } else {
          this.addResult('⚠️', '发布配置: 缺失（自动更新将不可用）');
        }

      } else {
        this.addResult('❌', 'package.json 中缺少 build 配置');
      }

      // 检查构建脚本
      if (packageJson.scripts) {
        const buildScripts = {
          'build': '源代码构建',
          'pack': '应用打包',
          'dist': '生成安装包',
          'dist:mac': 'macOS 安装包',
          'dist:win': 'Windows 安装包',
          'dist:linux': 'Linux 安装包'
        };

        for (const [script, description] of Object.entries(buildScripts)) {
          if (packageJson.scripts[script]) {
            this.addResult('✅', `${description}脚本: ${packageJson.scripts[script]}`);
          } else {
            this.addResult('⚠️', `${description}脚本: 缺失`);
          }
        }
      }

      // 检查依赖
      const requiredDeps = ['electron-builder', 'electron'];
      for (const dep of requiredDeps) {
        if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
          this.addResult('✅', `依赖 ${dep}: ${packageJson.devDependencies[dep]}`);
        } else if (packageJson.dependencies && packageJson.dependencies[dep]) {
          this.addResult('✅', `依赖 ${dep}: ${packageJson.dependencies[dep]}`);
        } else {
          this.addResult('❌', `依赖 ${dep}: 缺失`);
        }
      }

    } catch (error) {
      this.addResult('❌', `读取 package.json 失败: ${error.message}`);
    }
  }

  /**
   * 测试构建资源
   */
  async testBuildResources() {
    console.log('📁 测试构建资源...');

    const buildDir = path.join(this.projectRoot, 'build');
    
    if (await fs.pathExists(buildDir)) {
      this.addResult('✅', 'build 目录存在');
      
      // 检查必要的构建资源文件
      const requiredFiles = {
        'entitlements.mac.plist': 'macOS 权限配置',
        'installer.nsh': 'Windows 安装脚本',
        'app-metadata.json': '应用元数据',
        'icon-info.md': '图标说明文件'
      };

      for (const [file, description] of Object.entries(requiredFiles)) {
        const filePath = path.join(buildDir, file);
        if (await fs.pathExists(filePath)) {
          const stats = await fs.stat(filePath);
          this.addResult('✅', `${description}: ${file} (${this.formatBytes(stats.size)})`);
        } else {
          this.addResult('⚠️', `${description}: ${file} 缺失`);
        }
      }

      // 检查图标文件（这些通常需要手动添加）
      const iconFiles = {
        'icon.icns': 'macOS 图标',
        'icon.ico': 'Windows 图标',
        'icon.png': 'Linux 图标'
      };

      for (const [file, description] of Object.entries(iconFiles)) {
        const filePath = path.join(buildDir, file);
        if (await fs.pathExists(filePath)) {
          const stats = await fs.stat(filePath);
          this.addResult('✅', `${description}: ${file} (${this.formatBytes(stats.size)})`);
        } else {
          this.addResult('⚠️', `${description}: ${file} 缺失 - 需要手动添加`);
        }
      }

    } else {
      this.addResult('❌', 'build 目录不存在');
    }

    // 检查 resources 目录
    const resourcesDir = path.join(this.projectRoot, 'resources');
    if (await fs.pathExists(resourcesDir)) {
      this.addResult('✅', 'resources 目录存在');
    } else {
      this.addResult('⚠️', 'resources 目录不存在');
    }
  }

  /**
   * 测试平台特定配置
   */
  async testPlatformConfigs() {
    console.log('🖥️  测试平台特定配置...');

    try {
      const packageJson = await fs.readJson(path.join(this.projectRoot, 'package.json'));
      const build = packageJson.build;

      if (!build) {
        this.addResult('❌', '无法测试平台配置：缺少 build 配置');
        return;
      }

      // 测试 macOS 配置
      if (build.mac) {
        this.addResult('✅', 'macOS 配置存在');
        const mac = build.mac;
        
        if (mac.category) {
          this.addResult('✅', `  应用分类: ${mac.category}`);
        }
        if (mac.target) {
          this.addResult('✅', `  构建目标: ${JSON.stringify(mac.target)}`);
        }
        if (mac.hardenedRuntime) {
          this.addResult('✅', '  启用强化运行时');
        }
      }

      // 测试 Windows 配置
      if (build.win) {
        this.addResult('✅', 'Windows 配置存在');
        const win = build.win;
        
        if (win.target) {
          this.addResult('✅', `  构建目标: ${JSON.stringify(win.target)}`);
        }
        if (win.publisherName) {
          this.addResult('✅', `  发布者: ${win.publisherName}`);
        }
      }

      // 测试 Linux 配置
      if (build.linux) {
        this.addResult('✅', 'Linux 配置存在');
        const linux = build.linux;
        
        if (linux.target) {
          this.addResult('✅', `  构建目标: ${JSON.stringify(linux.target)}`);
        }
        if (linux.category) {
          this.addResult('✅', `  应用分类: ${linux.category}`);
        }
      }

      // 测试 NSIS 配置（Windows 安装程序）
      if (build.nsis) {
        this.addResult('✅', 'NSIS 配置存在');
        const nsis = build.nsis;
        
        if (nsis.oneClick === false) {
          this.addResult('✅', '  禁用一键安装（允许用户选择安装目录）');
        }
        if (nsis.allowToChangeInstallationDirectory) {
          this.addResult('✅', '  允许更改安装目录');
        }
        if (nsis.createDesktopShortcut) {
          this.addResult('✅', '  创建桌面快捷方式');
        }
      }

      // 测试 DMG 配置（macOS 磁盘映像）
      if (build.dmg) {
        this.addResult('✅', 'DMG 配置存在');
        const dmg = build.dmg;
        
        if (dmg.title) {
          this.addResult('✅', `  DMG 标题: ${dmg.title}`);
        }
        if (dmg.contents) {
          this.addResult('✅', `  DMG 内容配置: ${dmg.contents.length} 项`);
        }
      }

    } catch (error) {
      this.addResult('❌', `测试平台配置失败: ${error.message}`);
    }
  }

  /**
   * 添加测试结果
   */
  addResult(status, message) {
    this.testResults.push({ status, message });
    console.log(`   ${status} ${message}`);
  }

  /**
   * 打印测试结果摘要
   */
  printResults() {
    console.log('\n📊 打包配置测试结果摘要:');
    console.log('='.repeat(60));

    const passed = this.testResults.filter(r => r.status === '✅').length;
    const failed = this.testResults.filter(r => r.status === '❌').length;
    const warnings = this.testResults.filter(r => r.status === '⚠️').length;

    console.log(`✅ 通过: ${passed}`);
    console.log(`❌ 失败: ${failed}`);
    console.log(`⚠️  警告: ${warnings}`);
    console.log(`📊 总计: ${this.testResults.length}`);

    console.log('\n📋 建议和说明:');
    console.log('1. ⚠️  警告项目通常不会阻止打包，但建议完善');
    console.log('2. 图标文件需要手动创建并放置在 build/ 目录中');
    console.log('3. 代码签名证书是可选的，但推荐用于发布版本');
    console.log('4. 自动更新功能需要配置发布源（如 GitHub Releases）');

    if (failed > 0) {
      console.log('\n❌ 存在关键配置问题，可能影响打包！');
      console.log('请修复上述 ❌ 标记的问题后再进行打包。');
    } else {
      console.log('\n🎉 打包配置基本完整！');
      console.log('可以尝试运行 npm run pack 进行测试打包。');
    }
  }

  /**
   * 格式化字节大小
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// 运行测试
if (require.main === module) {
  const tester = new PackagingTester();
  tester.runPackagingTests().catch(console.error);
}

module.exports = PackagingTester;