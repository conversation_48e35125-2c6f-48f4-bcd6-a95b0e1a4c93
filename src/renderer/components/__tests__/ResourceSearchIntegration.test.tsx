import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ResourceList } from '../ResourceList';
import { ResourceService } from '../../../shared/services/ResourceService';
import { SmartEduClient } from '../../../shared/services/SmartEduClient';
import { CourseResource, CourseFilters } from '../../../shared/types';

// Mock the services
jest.mock('../../../shared/services/SmartEduClient');
jest.mock('../../../shared/services/ResourceService');

describe('资源搜索和展示功能集成测试', () => {
  let mockClient: jest.Mocked<SmartEduClient>;
  let mockResourceService: jest.Mocked<ResourceService>;

  const mockResources: CourseResource[] = [
    {
      id: 'resource1',
      title: '小学语文教材第一册',
      type: 'textbook',
      url: 'https://example.com/textbook1',
      metadata: {
        stage: 'primary',
        grade: 'grade1',
        subject: 'chinese',
        version: 'renjiao',
        volume: 'volume1',
        fileSize: 1024000
      },
      requiresAuth: false,
      accessLevel: 'public'
    },
    {
      id: 'resource2',
      title: '小学语文视频课程',
      type: 'video',
      url: 'https://example.com/video1',
      metadata: {
        stage: 'primary',
        grade: 'grade1',
        subject: 'chinese',
        version: 'renjiao',
        volume: 'volume1',
        duration: 1800
      },
      requiresAuth: true,
      accessLevel: 'registered'
    }
  ];

  const defaultProps = {
    resources: mockResources,
    onDownload: jest.fn(),
    onBatchDownload: jest.fn(),
    userLoggedIn: false,
    loading: false,
    selectedResources: [],
    onSelectionChange: jest.fn()
  };

  beforeEach(() => {
    mockClient = new SmartEduClient() as jest.Mocked<SmartEduClient>;
    mockResourceService = new ResourceService(mockClient) as jest.Mocked<ResourceService>;
    jest.clearAllMocks();
  });

  describe('基本功能测试', () => {
    it('应该能够显示资源列表', () => {
      render(<ResourceList {...defaultProps} />);
      
      // 验证资源标题显示
      expect(screen.getByText('小学语文教材第一册')).toBeInTheDocument();
      expect(screen.getByText('小学语文视频课程')).toBeInTheDocument();
      
      // 验证资源类型图标
      const bookIcons = document.querySelectorAll('[aria-label="book"]');
      const videoIcons = document.querySelectorAll('[aria-label="play-circle"]');
      expect(bookIcons.length).toBeGreaterThan(0);
      expect(videoIcons.length).toBeGreaterThan(0);
    });

    it('应该能够切换视图模式', () => {
      render(<ResourceList {...defaultProps} />);
      
      const viewModeSwitch = screen.getByRole('switch');
      expect(viewModeSwitch).toBeInTheDocument();
      
      // 默认应该是卡片模式
      expect(viewModeSwitch).toBeChecked();
      
      // 切换到列表模式
      fireEvent.click(viewModeSwitch);
      expect(viewModeSwitch).not.toBeChecked();
    });

    it('应该显示正确的访问权限标签', () => {
      render(<ResourceList {...defaultProps} />);
      
      // 验证权限标签
      expect(screen.getByText('公开')).toBeInTheDocument();
      expect(screen.getByText('需登录')).toBeInTheDocument();
    });

    it('应该能够选择资源', () => {
      const onSelectionChange = jest.fn();
      render(<ResourceList {...defaultProps} onSelectionChange={onSelectionChange} />);
      
      const checkboxes = screen.getAllByRole('checkbox');
      // 第一个是全选checkbox，跳过它
      const firstResourceCheckbox = checkboxes[1];
      
      fireEvent.click(firstResourceCheckbox);
      
      expect(onSelectionChange).toHaveBeenCalledWith(['resource1']);
    });

    it('应该能够处理下载操作', () => {
      const onDownload = jest.fn();
      render(<ResourceList {...defaultProps} onDownload={onDownload} />);
      
      const downloadButtons = screen.getAllByText('下载');
      fireEvent.click(downloadButtons[0]);
      
      expect(onDownload).toHaveBeenCalledWith(mockResources[0]);
    });
  });

  describe('权限控制测试', () => {
    it('未登录用户应该看到权限提示', () => {
      render(<ResourceList {...defaultProps} userLoggedIn={false} />);
      
      expect(screen.getByText('部分资源需要登录后才能下载')).toBeInTheDocument();
    });

    it('已登录用户不应该看到权限提示', () => {
      render(<ResourceList {...defaultProps} userLoggedIn={true} />);
      
      expect(screen.queryByText('部分资源需要登录后才能下载')).not.toBeInTheDocument();
    });
  });

  describe('批量操作测试', () => {
    it('应该能够进行批量下载', () => {
      const onBatchDownload = jest.fn();
      render(<ResourceList 
        {...defaultProps} 
        onBatchDownload={onBatchDownload}
        selectedResources={['resource1', 'resource2']}
      />);
      
      const batchDownloadButton = screen.getByText(/批量下载 \(2\)/);
      fireEvent.click(batchDownloadButton);
      
      expect(onBatchDownload).toHaveBeenCalledWith(mockResources);
    });

    it('应该能够全选资源', () => {
      const onSelectionChange = jest.fn();
      render(<ResourceList {...defaultProps} onSelectionChange={onSelectionChange} />);
      
      const selectAllCheckbox = screen.getByRole('checkbox', { name: '全选' });
      fireEvent.click(selectAllCheckbox);
      
      expect(onSelectionChange).toHaveBeenCalledWith(['resource1', 'resource2']);
    });
  });

  describe('加载状态测试', () => {
    it('应该显示加载状态', () => {
      render(<ResourceList {...defaultProps} loading={true} />);
      
      // 查找加载指示器 - 使用aria-busy属性
      const loadingElement = document.querySelector('[aria-busy="true"]');
      expect(loadingElement).toBeInTheDocument();
    });

    it('应该显示空状态', () => {
      render(<ResourceList {...defaultProps} resources={[]} />);
      
      expect(screen.getByText('暂无资源')).toBeInTheDocument();
    });
  });

  describe('统计信息测试', () => {
    it('应该显示正确的统计信息', () => {
      render(<ResourceList {...defaultProps} />);
      
      // 验证统计信息
      expect(screen.getByText(/共 2 个资源/)).toBeInTheDocument();
      expect(screen.getByText(/教材 1 个，视频 1 个/)).toBeInTheDocument();
    });
  });
});

describe('ResourceService 功能测试', () => {
  let resourceService: ResourceService;
  let mockClient: jest.Mocked<SmartEduClient>;

  const mockFilters: CourseFilters = {
    stage: 'primary',
    grade: 'grade1',
    subject: 'chinese',
    version: 'renjiao',
    volume: 'volume1'
  };

  beforeEach(() => {
    mockClient = new SmartEduClient() as jest.Mocked<SmartEduClient>;
    resourceService = new ResourceService(mockClient);
    jest.clearAllMocks();
  });

  it('应该能够搜索资源', async () => {
    const mockResources: CourseResource[] = [
      {
        id: 'resource1',
        title: '测试资源',
        type: 'textbook',
        url: 'https://example.com/test',
        metadata: mockFilters,
        requiresAuth: false,
        accessLevel: 'public'
      }
    ];

    mockClient.searchResources.mockResolvedValue(mockResources);

    const result = await resourceService.searchResources(mockFilters);

    expect(result).toEqual(mockResources);
    expect(mockClient.searchResources).toHaveBeenCalledWith(mockFilters);
  });

  it('应该能够获取资源详情', async () => {
    const mockDetail = {
      id: 'resource1',
      title: '测试资源',
      type: 'textbook' as const,
      url: 'https://example.com/test',
      metadata: mockFilters,
      requiresAuth: false,
      accessLevel: 'public' as const,
      description: '测试描述',
      thumbnailUrl: 'https://example.com/thumb.jpg',
      tags: ['测试'],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    mockClient.getResourceDetail.mockResolvedValue(mockDetail);

    const result = await resourceService.getResourceDetail('resource1');

    expect(result).toEqual(mockDetail);
    expect(mockClient.getResourceDetail).toHaveBeenCalledWith('resource1');
  });

  it('应该能够检查资源访问权限', async () => {
    const mockAccessInfo = {
      hasAccess: true,
      requiresAuth: false,
      accessLevel: 'public' as const
    };

    mockClient.checkResourceAccess.mockResolvedValue(mockAccessInfo);

    const result = await resourceService.checkResourceAccess('resource1');

    expect(result).toEqual(mockAccessInfo);
    expect(mockClient.checkResourceAccess).toHaveBeenCalledWith('resource1');
  });

  it('应该能够缓存搜索结果', async () => {
    const mockResources: CourseResource[] = [
      {
        id: 'resource1',
        title: '测试资源',
        type: 'textbook',
        url: 'https://example.com/test',
        metadata: mockFilters,
        requiresAuth: false,
        accessLevel: 'public'
      }
    ];

    mockClient.searchResources.mockResolvedValue(mockResources);

    // 第一次调用
    await resourceService.searchResources(mockFilters);
    // 第二次调用应该使用缓存
    await resourceService.searchResources(mockFilters);

    // 只应该调用一次API
    expect(mockClient.searchResources).toHaveBeenCalledTimes(1);
  });
});