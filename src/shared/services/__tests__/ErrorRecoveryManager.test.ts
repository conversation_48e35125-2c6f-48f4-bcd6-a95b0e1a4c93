import { ErrorRecoveryManager, RecoveryAction, RecoveryConfig } from '../ErrorRecoveryManager';
import { RetryManager } from '../RetryManager';
import { SmartEduError, ErrorType, NetworkError, ApiError, FileError } from '../../types';

// Mock fetch for network recovery tests
global.fetch = jest.fn();

describe('ErrorRecoveryManager', () => {
  let errorRecoveryManager: ErrorRecoveryManager;
  let mockRetryManager: jest.Mocked<RetryManager>;

  beforeEach(() => {
    mockRetryManager = {
      executeWithRetry: jest.fn(),
      wrap: jest.fn(),
      executeBatchWithRetry: jest.fn(),
      executeBatchParallelWithRetry: jest.fn(),
      getErrorStats: jest.fn(),
      updateConfig: jest.fn(),
      getConfig: jest.fn(),
      resetConfig: jest.fn(),
      on: jest.fn(),
      off: jest.fn(),
      emit: jest.fn(),
      removeAllListeners: jest.fn()
    } as any;

    errorRecoveryManager = new ErrorRecoveryManager(mockRetryManager, {
      recoveryTimeout: 1000, // 短超时时间
      cooldownPeriod: 1000
    });
    
    // Mock fetch
    (fetch as jest.Mock).mockClear();
  });

  afterEach(() => {
    errorRecoveryManager.removeAllListeners();
    jest.clearAllTimers();
  });

  describe('基本恢复功能', () => {
    it('应该成功恢复网络错误', async () => {
      const networkError = new NetworkError('网络连接失败');
      const mockOperation = jest.fn().mockResolvedValue('success');

      // Mock successful network check
      (fetch as jest.Mock).mockResolvedValue({ ok: true });

      const result = await errorRecoveryManager.attemptRecovery(
        networkError,
        mockOperation
      );

      expect(result).toBe('success');
      expect(mockOperation).toHaveBeenCalledTimes(1);
    });

    it('应该在恢复失败时抛出错误', async () => {
      const fileError = new FileError('文件不存在');
      const mockOperation = jest.fn().mockResolvedValue('success');

      await expect(
        errorRecoveryManager.attemptRecovery(fileError, mockOperation)
      ).rejects.toThrow('所有恢复策略都已失败');
    });

    it('应该在冷却期内拒绝恢复', async () => {
      const config: Partial<RecoveryConfig> = {
        errorThreshold: 1,
        cooldownPeriod: 5000
      };
      
      const manager = new ErrorRecoveryManager(mockRetryManager, config);
      const networkError = new NetworkError('网络错误');
      const mockOperation = jest.fn().mockResolvedValue('success');

      // 触发冷却期
      try {
        await manager.attemptRecovery(networkError, mockOperation);
      } catch {
        // 忽略第一次失败
      }

      // 第二次尝试应该被拒绝
      await expect(
        manager.attemptRecovery(networkError, mockOperation)
      ).rejects.toThrow('错误恢复管理器正在冷却期');
    });

    it('应该在达到最大恢复尝试次数后停止', async () => {
      const config: Partial<RecoveryConfig> = {
        maxRecoveryAttempts: 1
      };
      
      const manager = new ErrorRecoveryManager(mockRetryManager, config);
      const networkError = new NetworkError('网络错误');
      const mockOperation = jest.fn().mockRejectedValue(new Error('操作失败'));

      // Mock failed network recovery
      (fetch as jest.Mock).mockRejectedValue(new Error('网络不可用'));

      await expect(
        manager.attemptRecovery(networkError, mockOperation)
      ).rejects.toThrow('恢复尝试次数已达上限');
    });
  });

  describe('恢复动作管理', () => {
    it('应该添加自定义恢复动作', async () => {
      const customAction: RecoveryAction = {
        strategy: 'retry',
        description: '自定义重试',
        execute: jest.fn().mockResolvedValue(true),
        priority: 1
      };

      errorRecoveryManager.addRecoveryAction(ErrorType.NETWORK_ERROR, customAction);

      const networkError = new NetworkError('网络错误');
      const mockOperation = jest.fn().mockResolvedValue('success');

      await errorRecoveryManager.attemptRecovery(networkError, mockOperation);

      expect(customAction.execute).toHaveBeenCalled();
    });

    it('应该移除恢复动作', () => {
      errorRecoveryManager.removeRecoveryAction(ErrorType.NETWORK_ERROR, 'retry');

      const networkError = new NetworkError('网络错误');
      const mockOperation = jest.fn().mockResolvedValue('success');

      // 应该没有可用的恢复动作
      expect(
        errorRecoveryManager.attemptRecovery(networkError, mockOperation)
      ).rejects.toThrow('没有适用的恢复策略');
    });

    it('应该按优先级排序恢复动作', async () => {
      const highPriorityAction: RecoveryAction = {
        strategy: 'retry',
        description: '高优先级',
        execute: jest.fn().mockResolvedValue(true),
        priority: 1
      };

      const lowPriorityAction: RecoveryAction = {
        strategy: 'fallback',
        description: '低优先级',
        execute: jest.fn().mockResolvedValue(true),
        priority: 2
      };

      errorRecoveryManager.addRecoveryAction(ErrorType.NETWORK_ERROR, lowPriorityAction);
      errorRecoveryManager.addRecoveryAction(ErrorType.NETWORK_ERROR, highPriorityAction);

      const networkError = new NetworkError('网络错误');
      const mockOperation = jest.fn().mockResolvedValue('success');

      await errorRecoveryManager.attemptRecovery(networkError, mockOperation);

      // 高优先级动作应该先执行
      expect(highPriorityAction.execute).toHaveBeenCalled();
      expect(lowPriorityAction.execute).not.toHaveBeenCalled();
    });

    it('应该使用条件过滤恢复动作', async () => {
      const conditionalAction: RecoveryAction = {
        strategy: 'retry',
        description: '条件动作',
        execute: jest.fn().mockResolvedValue(true),
        condition: (error) => error.message.includes('特定错误'),
        priority: 1
      };

      errorRecoveryManager.addRecoveryAction(ErrorType.NETWORK_ERROR, conditionalAction);

      // 不匹配条件的错误
      const networkError1 = new NetworkError('普通网络错误');
      const mockOperation1 = jest.fn().mockResolvedValue('success');

      await expect(
        errorRecoveryManager.attemptRecovery(networkError1, mockOperation1)
      ).rejects.toThrow('没有适用的恢复策略');

      // 匹配条件的错误
      const networkError2 = new NetworkError('特定错误');
      const mockOperation2 = jest.fn().mockResolvedValue('success');

      await errorRecoveryManager.attemptRecovery(networkError2, mockOperation2);

      expect(conditionalAction.execute).toHaveBeenCalled();
    });
  });

  describe('自动恢复包装器', () => {
    it('应该自动恢复失败的操作', async () => {
      const mockFunction = jest.fn()
        .mockRejectedValueOnce(new NetworkError('网络错误'))
        .mockResolvedValue('success');

      // Mock successful network recovery
      (fetch as jest.Mock).mockResolvedValue({ ok: true });

      const wrappedFunction = errorRecoveryManager.wrapWithAutoRecovery(mockFunction);
      const result = await wrappedFunction('arg1', 'arg2');

      expect(result).toBe('success');
      expect(mockFunction).toHaveBeenCalledTimes(2);
      expect(mockFunction).toHaveBeenCalledWith('arg1', 'arg2');
    });

    it('应该在禁用自动恢复时直接抛出错误', async () => {
      const config: Partial<RecoveryConfig> = {
        enableAutoRecovery: false
      };
      
      const manager = new ErrorRecoveryManager(mockRetryManager, config);
      const mockFunction = jest.fn().mockRejectedValue(new NetworkError('网络错误'));

      const wrappedFunction = manager.wrapWithAutoRecovery(mockFunction);

      await expect(wrappedFunction()).rejects.toThrow('网络错误');
      expect(mockFunction).toHaveBeenCalledTimes(1);
    });
  });

  describe('批量恢复', () => {
    it('应该恢复批量操作', async () => {
      const operation1 = jest.fn().mockResolvedValue('result1');
      const operation2 = jest.fn()
        .mockRejectedValueOnce(new NetworkError('网络错误'))
        .mockResolvedValue('result2');
      const operation3 = jest.fn().mockRejectedValue(new FileError('文件错误'));

      // Mock successful network recovery
      (fetch as jest.Mock).mockResolvedValue({ ok: true });

      const results = await errorRecoveryManager.recoverBatch([
        operation1,
        operation2,
        operation3
      ]);

      expect(results).toHaveLength(3);
      expect(results[0].success).toBe(true);
      expect(results[0].result).toBe('result1');
      expect(results[1].success).toBe(true);
      expect(results[1].result).toBe('result2');
      expect(results[2].success).toBe(false);
      expect(results[2].error).toBeInstanceOf(FileError);
    });

    it('应该在遇到错误时停止批量操作', async () => {
      const operation1 = jest.fn().mockResolvedValue('result1');
      const operation2 = jest.fn().mockRejectedValue(new FileError('文件错误'));
      const operation3 = jest.fn().mockResolvedValue('result3');

      const results = await errorRecoveryManager.recoverBatch([
        operation1,
        operation2,
        operation3
      ], false); // continueOnError = false

      expect(results).toHaveLength(2);
      expect(results[0].success).toBe(true);
      expect(results[1].success).toBe(false);
      expect(operation3).not.toHaveBeenCalled();
    });
  });

  describe('错误类型识别', () => {
    it('应该正确识别SmartEduError类型', async () => {
      const apiError = new ApiError('API错误');
      const mockOperation = jest.fn().mockResolvedValue('success');

      // 应该尝试API错误的恢复策略
      await expect(
        errorRecoveryManager.attemptRecovery(apiError, mockOperation)
      ).rejects.toThrow(); // 因为没有成功的恢复动作
    });

    it('应该根据消息推断普通Error类型', async () => {
      const networkError = new Error('网络连接失败');
      const mockOperation = jest.fn().mockResolvedValue('success');

      // Mock successful network recovery
      (fetch as jest.Mock).mockResolvedValue({ ok: true });

      const result = await errorRecoveryManager.attemptRecovery(
        networkError,
        mockOperation
      );

      expect(result).toBe('success');
    });
  });

  describe('冷却机制', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('应该在错误频率过高时进入冷却期', async () => {
      const config: Partial<RecoveryConfig> = {
        errorThreshold: 2,
        cooldownPeriod: 5000
      };
      
      const manager = new ErrorRecoveryManager(mockRetryManager, config);
      const cooldownStartedSpy = jest.fn();
      manager.on('cooldown-started', cooldownStartedSpy);

      const networkError = new NetworkError('网络错误');
      const mockOperation = jest.fn().mockRejectedValue(new Error('操作失败'));

      // Mock failed network recovery
      (fetch as jest.Mock).mockRejectedValue(new Error('网络不可用'));

      // 触发多个错误
      try {
        await manager.attemptRecovery(networkError, mockOperation);
      } catch {}
      
      try {
        await manager.attemptRecovery(networkError, mockOperation);
      } catch {}

      expect(cooldownStartedSpy).toHaveBeenCalledWith(5000);
    });

    it('应该在冷却期结束后恢复正常', async () => {
      const config: Partial<RecoveryConfig> = {
        errorThreshold: 1,
        cooldownPeriod: 5000
      };
      
      const manager = new ErrorRecoveryManager(mockRetryManager, config);
      const cooldownEndedSpy = jest.fn();
      manager.on('cooldown-ended', cooldownEndedSpy);

      const networkError = new NetworkError('网络错误');
      const mockOperation = jest.fn().mockRejectedValue(new Error('操作失败'));

      // Mock failed network recovery
      (fetch as jest.Mock).mockRejectedValue(new Error('网络不可用'));

      // 触发冷却期
      try {
        await manager.attemptRecovery(networkError, mockOperation);
      } catch {}

      // 快进时间
      jest.advanceTimersByTime(5000);

      expect(cooldownEndedSpy).toHaveBeenCalled();

      // 现在应该可以正常恢复
      (fetch as jest.Mock).mockResolvedValue({ ok: true });
      const successOperation = jest.fn().mockResolvedValue('success');
      
      const result = await manager.attemptRecovery(networkError, successOperation);
      expect(result).toBe('success');
    });
  });

  describe('事件发射', () => {
    it('应该在恢复开始时发射事件', async () => {
      const recoveryStartedSpy = jest.fn();
      errorRecoveryManager.on('recovery-started', recoveryStartedSpy);

      const networkError = new NetworkError('网络错误');
      const mockOperation = jest.fn().mockResolvedValue('success');

      // Mock successful network recovery
      (fetch as jest.Mock).mockResolvedValue({ ok: true });

      await errorRecoveryManager.attemptRecovery(networkError, mockOperation);

      expect(recoveryStartedSpy).toHaveBeenCalledWith(networkError, 'retry');
    });

    it('应该在恢复成功时发射事件', async () => {
      const recoverySuccessSpy = jest.fn();
      errorRecoveryManager.on('recovery-success', recoverySuccessSpy);

      const networkError = new NetworkError('网络错误');
      const mockOperation = jest.fn().mockResolvedValue('success');

      // Mock successful network recovery
      (fetch as jest.Mock).mockResolvedValue({ ok: true });

      await errorRecoveryManager.attemptRecovery(networkError, mockOperation);

      expect(recoverySuccessSpy).toHaveBeenCalledWith(networkError, 'retry', 'success');
    });

    it('应该在恢复失败时发射事件', async () => {
      const recoveryFailedSpy = jest.fn();
      errorRecoveryManager.on('recovery-failed', recoveryFailedSpy);

      const networkError = new NetworkError('网络错误');
      const mockOperation = jest.fn().mockResolvedValue('success');

      // Mock failed network recovery
      (fetch as jest.Mock).mockRejectedValue(new Error('网络不可用'));

      await expect(
        errorRecoveryManager.attemptRecovery(networkError, mockOperation)
      ).rejects.toThrow();

      expect(recoveryFailedSpy).toHaveBeenCalled();
    });

    it('应该在恢复耗尽时发射事件', async () => {
      const recoveryExhaustedSpy = jest.fn();
      errorRecoveryManager.on('recovery-exhausted', recoveryExhaustedSpy);

      const config: Partial<RecoveryConfig> = {
        maxRecoveryAttempts: 1
      };
      
      const manager = new ErrorRecoveryManager(mockRetryManager, config);
      manager.on('recovery-exhausted', recoveryExhaustedSpy);

      const networkError = new NetworkError('网络错误');
      const mockOperation = jest.fn().mockRejectedValue(new Error('操作失败'));

      // Mock failed network recovery
      (fetch as jest.Mock).mockRejectedValue(new Error('网络不可用'));

      await expect(
        manager.attemptRecovery(networkError, mockOperation)
      ).rejects.toThrow();

      expect(recoveryExhaustedSpy).toHaveBeenCalled();
    });
  });

  describe('统计信息', () => {
    it('应该提供恢复统计信息', async () => {
      const networkError = new NetworkError('网络错误');
      const mockOperation = jest.fn().mockResolvedValue('success');

      // Mock successful network recovery
      (fetch as jest.Mock).mockResolvedValue({ ok: true });

      await errorRecoveryManager.attemptRecovery(networkError, mockOperation);

      const stats = errorRecoveryManager.getRecoveryStats();

      expect(stats.totalAttempts).toBe(1);
      expect(stats.successRate).toBe(1);
      expect(stats.strategyStats.retry.attempts).toBe(1);
      expect(stats.strategyStats.retry.successes).toBe(1);
    });

    it('应该跟踪冷却状态', async () => {
      const config: Partial<RecoveryConfig> = {
        errorThreshold: 1,
        cooldownPeriod: 5000
      };
      
      const manager = new ErrorRecoveryManager(mockRetryManager, config);
      const networkError = new NetworkError('网络错误');
      const mockOperation = jest.fn().mockRejectedValue(new Error('操作失败'));

      // Mock failed network recovery
      (fetch as jest.Mock).mockRejectedValue(new Error('网络不可用'));

      // 触发冷却期
      try {
        await manager.attemptRecovery(networkError, mockOperation);
      } catch {}

      const stats = manager.getRecoveryStats();

      expect(stats.isInCooldown).toBe(true);
      expect(stats.cooldownRemaining).toBeGreaterThan(0);
    });
  });

  describe('配置管理', () => {
    it('应该更新配置', () => {
      const newConfig: Partial<RecoveryConfig> = {
        maxRecoveryAttempts: 5,
        recoveryTimeout: 60000
      };

      errorRecoveryManager.updateConfig(newConfig);

      const config = errorRecoveryManager.getConfig();
      expect(config.maxRecoveryAttempts).toBe(5);
      expect(config.recoveryTimeout).toBe(60000);
    });

    it('应该重置状态', () => {
      // 先触发一些状态变化
      const stats1 = errorRecoveryManager.getRecoveryStats();
      
      errorRecoveryManager.reset();
      
      const stats2 = errorRecoveryManager.getRecoveryStats();
      expect(stats2.totalAttempts).toBe(0);
      expect(stats2.errorCount).toBe(0);
      expect(stats2.isInCooldown).toBe(false);
    });
  });

  describe('超时处理', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('应该在恢复操作超时时失败', async () => {
      const slowRecoveryAction: RecoveryAction = {
        strategy: 'retry',
        description: '慢速恢复',
        execute: () => new Promise(resolve => setTimeout(resolve, 60000)),
        priority: 1
      };

      errorRecoveryManager.addRecoveryAction(ErrorType.NETWORK_ERROR, slowRecoveryAction);

      const networkError = new NetworkError('网络错误');
      const mockOperation = jest.fn().mockResolvedValue('success');

      const recoveryPromise = errorRecoveryManager.attemptRecovery(
        networkError,
        mockOperation
      );

      // 快进到超时
      jest.advanceTimersByTime(30000);

      await expect(recoveryPromise).rejects.toThrow();
    });
  });
});