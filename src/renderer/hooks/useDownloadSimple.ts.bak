import { useState, useCallback, useRef, useEffect } from 'react';
import { message } from 'antd';
import { RendererDownloadManager } from '../services/RendererDownloadManager';
import { DownloadTask, DownloadProgress, AppConfig } from '../../shared/types';

interface DownloadState {
  tasks: DownloadTask[];
  isLoading: boolean;
  error: string | null;
}

/**
 * 简化的下载 Hook
 * 使用渲染进程下载管理器，避免 Node.js 模块依赖
 */
export const useDownloadSimple = (config: AppConfig) => {
  const [state, setState] = useState<DownloadState>({
    tasks: [],
    isLoading: false,
    error: null,
  });

  const downloadManagerRef = useRef<RendererDownloadManager | null>(null);

  // 初始化下载管理器
  useEffect(() => {
    const downloadManager = new RendererDownloadManager();
    downloadManagerRef.current = downloadManager;

    // 加载现有任务
    loadTasks();

    return () => {
      downloadManager.dispose();
    };
  }, []);

  /**
   * 加载所有下载任务
   */
  const loadTasks = useCallback(async () => {
    if (!downloadManagerRef.current) return;

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      const tasks = await downloadManagerRef.current.getAllTasks();
      setState(prev => ({ ...prev, tasks, isLoading: false }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '加载下载任务失败';
      setState(prev => ({ ...prev, error: errorMessage, isLoading: false }));
      message.error(errorMessage);
    }
  }, []);

  /**
   * 开始下载
   */
  const startDownload = useCallback(async (url: string): Promise<string | null> => {
    if (!downloadManagerRef.current) {
      message.error('下载管理器未初始化');
      return null;
    }

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      
      const taskId = await downloadManagerRef.current.startDownload(
        url,
        config,
        (progress: DownloadProgress) => {
          // 更新任务进度
          setState(prev => ({
            ...prev,
            tasks: prev.tasks.map(task => 
              task.id === taskId 
                ? { ...task, progress }
                : task
            )
          }));
        }
      );

      // 刷新任务列表
      await loadTasks();
      
      setState(prev => ({ ...prev, isLoading: false }));
      message.success('下载任务已开始');
      
      return taskId;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '开始下载失败';
      setState(prev => ({ ...prev, error: errorMessage, isLoading: false }));
      message.error(errorMessage);
      return null;
    }
  }, [config, loadTasks]);

  /**
   * 暂停下载
   */
  const pauseDownload = useCallback(async (taskId: string) => {
    if (!downloadManagerRef.current) return;

    try {
      await downloadManagerRef.current.pauseDownload(taskId);
      await loadTasks();
      message.info('下载已暂停');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '暂停下载失败';
      message.error(errorMessage);
    }
  }, [loadTasks]);

  /**
   * 恢复下载
   */
  const resumeDownload = useCallback(async (taskId: string) => {
    if (!downloadManagerRef.current) return;

    try {
      await downloadManagerRef.current.resumeDownload(taskId);
      await loadTasks();
      message.info('下载已恢复');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '恢复下载失败';
      message.error(errorMessage);
    }
  }, [loadTasks]);

  /**
   * 取消下载
   */
  const cancelDownload = useCallback(async (taskId: string) => {
    if (!downloadManagerRef.current) return;

    try {
      await downloadManagerRef.current.cancelDownload(taskId);
      await loadTasks();
      message.info('下载已取消');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '取消下载失败';
      message.error(errorMessage);
    }
  }, [loadTasks]);

  /**
   * 刷新任务列表
   */
  const refreshTasks = useCallback(() => {
    loadTasks();
  }, [loadTasks]);

  return {
    // 状态
    tasks: state.tasks,
    isLoading: state.isLoading,
    error: state.error,

    // 操作方法
    startDownload,
    pauseDownload,
    resumeDownload,
    cancelDownload,
    refreshTasks,
  };
};
