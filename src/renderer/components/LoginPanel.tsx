import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Alert, 
  Image, 
  Space, 
  Divider, 
  Typography,
  Spin
} from 'antd';
import { 
  UserOutlined, 
  LockOutlined, 
  SafetyOutlined,
  ReloadOutlined,
  LoginOutlined,
  UserAddOutlined
} from '@ant-design/icons';
import { LoginPanelProps, LoginCredentials } from '../../shared/types';

const { Title, Text } = Typography;

/**
 * 登录面板组件
 * 提供用户登录和访客模式切换功能
 */
export const LoginPanel: React.FC<LoginPanelProps> = ({
  onLogin,
  onGuestMode,
  loading,
  error,
  captchaRequired = false,
  captchaImage,
  onCaptchaRefresh
}) => {
  const [form] = Form.useForm();
  const [showCaptcha, setShowCaptcha] = useState(captchaRequired);

  useEffect(() => {
    setShowCaptcha(captchaRequired);
  }, [captchaRequired]);

  const handleLogin = async (values: LoginCredentials) => {
    try {
      await onLogin(values);
    } catch (error) {
      console.error('登录失败:', error);
    }
  };

  const handleGuestMode = () => {
    onGuestMode();
  };

  const handleCaptchaRefresh = () => {
    if (onCaptchaRefresh) {
      onCaptchaRefresh();
    }
  };

  const validateMessages = {
    required: '${label}是必填项',
    types: {
      email: '请输入有效的邮箱地址',
    },
    string: {
      min: '${label}至少需要${min}个字符',
      max: '${label}不能超过${max}个字符',
    },
  };

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      padding: '20px'
    }}>
      <Card
        style={{ 
          width: '100%', 
          maxWidth: 400,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          borderRadius: '12px'
        }}
        bodyStyle={{ padding: '32px' }}
      >
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <Title level={2} style={{ color: '#1890ff', marginBottom: '8px' }}>
            智慧教育下载器
          </Title>
          <Text type="secondary">
            国家中小学智慧平台资源下载工具
          </Text>
        </div>

        {error && (
          <Alert
            message="登录失败"
            description={error}
            type="error"
            showIcon
            style={{ marginBottom: '24px' }}
          />
        )}

        <Form
          form={form}
          name="login"
          onFinish={handleLogin}
          validateMessages={validateMessages}
          size="large"
          layout="vertical"
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[
              { required: true },
              { min: 3, max: 50 }
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="请输入用户名"
              disabled={loading}
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="密码"
            rules={[
              { required: true },
              { min: 6, max: 100 }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入密码"
              disabled={loading}
            />
          </Form.Item>

          {showCaptcha && (
            <Form.Item
              name="captcha"
              label="验证码"
              rules={[{ required: true, message: '请输入验证码' }]}
            >
              <Space.Compact style={{ width: '100%' }}>
                <Input
                  prefix={<SafetyOutlined />}
                  placeholder="请输入验证码"
                  disabled={loading}
                  style={{ flex: 1 }}
                />
                <div style={{ 
                  display: 'flex', 
                  alignItems: 'center',
                  border: '1px solid #d9d9d9',
                  borderLeft: 'none',
                  borderRadius: '0 6px 6px 0',
                  padding: '4px 8px',
                  background: '#fafafa'
                }}>
                  {captchaImage ? (
                    <Image
                      src={`data:image/png;base64,${captchaImage}`}
                      alt="验证码"
                      width={80}
                      height={32}
                      preview={false}
                      style={{ cursor: 'pointer' }}
                      onClick={handleCaptchaRefresh}
                    />
                  ) : (
                    <Spin size="small" />
                  )}
                  <Button
                    type="text"
                    icon={<ReloadOutlined />}
                    size="small"
                    onClick={handleCaptchaRefresh}
                    disabled={loading}
                    style={{ marginLeft: '4px' }}
                  />
                </div>
              </Space.Compact>
            </Form.Item>
          )}

          <Form.Item style={{ marginBottom: '16px' }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
              icon={<LoginOutlined />}
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        <Divider>
          <Text type="secondary">或者</Text>
        </Divider>

        <Button
          type="default"
          onClick={handleGuestMode}
          disabled={loading}
          block
          icon={<UserAddOutlined />}
          style={{ marginTop: '16px' }}
        >
          访客模式
        </Button>

        <div style={{ 
          textAlign: 'center', 
          marginTop: '24px',
          padding: '16px',
          background: '#f5f5f5',
          borderRadius: '8px'
        }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            <div>• 登录后可访问更多教育资源</div>
            <div>• 访客模式仅可下载公开资源</div>
            <div>• 请使用智慧平台官方账号登录</div>
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default LoginPanel;