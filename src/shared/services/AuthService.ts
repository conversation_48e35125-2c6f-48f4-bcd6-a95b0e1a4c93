import * as crypto from 'crypto';

/**
 * 智慧教育平台认证服务
 * 基于FlyEduDownloader项目的认证逻辑实现
 */
export class AuthService {
  private static instance: AuthService;
  private authToken: string | null = null;
  private sessionCookies: string | null = null;

  private constructor() {}

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * 生成X-Nd-Auth认证头
   * 基于FlyEduDownloader项目的x-nd-auth生成思路
   */
  generateXNdAuth(url: string, timestamp?: number): string {
    try {
      const ts = timestamp || Date.now();
      const urlPath = new URL(url).pathname;
      
      // 基于URL路径和时间戳生成认证字符串
      // 这里使用简化的算法，实际可能需要更复杂的加密逻辑
      const baseString = `${urlPath}${ts}`;
      const hash = crypto.createHash('md5').update(baseString).digest('hex');
      
      // 构造认证头格式
      return `MAC id="anonymous",ts="${ts}",nonce="${hash.substring(0, 8)}",mac="${hash}"`;
    } catch (error) {
      console.warn('生成X-Nd-Auth失败，使用默认值:', error);
      return 'MAC id="anonymous",ts="' + Date.now() + '",nonce="12345678",mac="default"';
    }
  }

  /**
   * 生成X-Nd-Checksum校验头
   */
  generateXNdChecksum(url: string, timestamp?: number): string {
    try {
      const ts = timestamp || Date.now();
      const urlPath = new URL(url).pathname;
      
      // 生成校验和
      const checksumString = `${urlPath}${ts}`;
      return crypto.createHash('sha256').update(checksumString).digest('hex').substring(0, 16);
    } catch (error) {
      console.warn('生成X-Nd-Checksum失败，使用默认值:', error);
      return 'default_checksum';
    }
  }

  /**
   * 构建完整的请求头
   */
  buildRequestHeaders(url: string, additionalHeaders: Record<string, string> = {}): Record<string, string> {
    const timestamp = Date.now();
    
    const headers: Record<string, string> = {
      'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Accept': 'application/json, text/plain, */*',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache',
      'Referer': 'https://basic.smartedu.cn/',
      'Origin': 'https://basic.smartedu.cn',
      'X-Nd-Auth': this.generateXNdAuth(url, timestamp),
      'X-Nd-Checksum': this.generateXNdChecksum(url, timestamp),
      ...additionalHeaders
    };

    // 如果有会话Cookie，添加到请求头
    if (this.sessionCookies) {
      headers['Cookie'] = this.sessionCookies;
    }

    return headers;
  }

  /**
   * 设置会话Cookie
   */
  setSessionCookies(cookies: string): void {
    this.sessionCookies = cookies;
  }

  /**
   * 获取会话Cookie
   */
  getSessionCookies(): string | null {
    return this.sessionCookies;
  }

  /**
   * 清除认证信息
   */
  clearAuth(): void {
    this.authToken = null;
    this.sessionCookies = null;
  }

  /**
   * 检查是否已认证
   */
  isAuthenticated(): boolean {
    return this.authToken !== null || this.sessionCookies !== null;
  }

  /**
   * 设置认证令牌
   */
  setAuthToken(token: string): void {
    this.authToken = token;
  }

  /**
   * 获取认证令牌
   */
  getAuthToken(): string | null {
    return this.authToken;
  }
}

export default AuthService;
