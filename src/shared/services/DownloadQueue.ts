import { EventEmitter } from 'events';

// 任务状态枚举
export enum TaskStatus {
  PENDING = 'pending',
  DOWNLOADING = 'downloading', 
  PAUSED = 'paused',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

// 任务优先级枚举
export enum TaskPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high'
}

// 下载任务接口
export interface DownloadTask {
  id: string;
  resource: {
    id: string;
    title: string;
    type: 'textbook' | 'video';
    url: string;
    metadata: {
      stage: string;
      grade: string;
      subject: string;
      version: string;
      volume: string;
      chapter?: string;
      lesson?: string;
      fileSize?: number;
      duration?: number;
    };
    requiresAuth: boolean;
  };
  status: TaskStatus;
  progress: number;
  speed: number;
  estimatedTime: number;
  error?: string;
  requiresAuth: boolean;
  createdAt: Date;
  updatedAt: Date;
  outputPath?: string;
}

// 下载进度信息
export interface DownloadProgress {
  taskId: string;
  progress: number;
  speed: number;
  estimatedTime: number;
  downloadedBytes: number;
  totalBytes: number;
}

// 队列配置
export interface QueueConfig {
  maxConcurrentDownloads: number;
  retryAttempts: number;
  retryDelay: number;
  maxConcurrentTasks?: number;
  maxRetries?: number;
  enablePriority?: boolean;
  persistState?: boolean;
}

/**
 * 下载队列管理系统
 * 支持任务队列管理、状态跟踪、并发控制和任务操作
 */
export class DownloadQueue extends EventEmitter {
  private tasks: Map<string, DownloadTask> = new Map();
  private activeDownloads: Set<string> = new Set();
  private config: QueueConfig;

  constructor(config: Partial<QueueConfig> = {}) {
    super();
    this.config = {
      maxConcurrentDownloads: 3,
      retryAttempts: 3,
      retryDelay: 1000,
      ...config
    };
  }

  /**
   * 添加下载任务到队列
   */
  addTask(task: Omit<DownloadTask, 'id' | 'status' | 'progress' | 'speed' | 'estimatedTime' | 'createdAt' | 'updatedAt'> | string, priority?: TaskPriority): string {
    // Handle both old and new signatures
    if (typeof task === 'string') {
      // Legacy signature - just return the taskId
      return task;
    }
    const taskId = this.generateTaskId();
    const downloadTask: DownloadTask = {
      ...task,
      id: taskId,
      status: TaskStatus.PENDING,
      progress: 0,
      speed: 0,
      estimatedTime: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.tasks.set(taskId, downloadTask);
    this.emit('taskAdded', downloadTask);
    
    // 尝试开始下载
    this.processQueue();
    
    return taskId;
  }

  /**
   * 暂停指定任务
   */
  pauseTask(taskId: string): boolean {
    const task = this.tasks.get(taskId);
    if (!task) {
      return false;
    }

    if (task.status === TaskStatus.DOWNLOADING) {
      this.updateTaskStatus(taskId, TaskStatus.PAUSED);
      this.activeDownloads.delete(taskId);
      this.emit('taskPaused', task);
      
      // 处理队列中的其他任务
      this.processQueue();
      return true;
    }

    return false;
  }

  /**
   * 恢复指定任务
   */
  resumeTask(taskId: string): boolean {
    const task = this.tasks.get(taskId);
    if (!task) {
      return false;
    }

    if (task.status === TaskStatus.PAUSED || task.status === TaskStatus.FAILED) {
      this.updateTaskStatus(taskId, TaskStatus.PENDING);
      this.emit('taskResumed', task);
      
      // 尝试开始下载
      this.processQueue();
      return true;
    }

    return false;
  }

  /**
   * 取消指定任务
   */
  cancelTask(taskId: string): boolean {
    const task = this.tasks.get(taskId);
    if (!task) {
      return false;
    }

    if (task.status !== TaskStatus.COMPLETED && task.status !== TaskStatus.CANCELLED) {
      this.updateTaskStatus(taskId, TaskStatus.CANCELLED);
      this.activeDownloads.delete(taskId);
      this.emit('taskCancelled', task);
      
      // 处理队列中的其他任务
      this.processQueue();
      return true;
    }

    return false;
  }

  /**
   * 获取任务状态
   */
  getTaskStatus(taskId: string): TaskStatus | null {
    const task = this.tasks.get(taskId);
    return task ? task.status : null;
  }

  /**
   * 获取指定任务
   */
  getTask(taskId: string): DownloadTask | null {
    return this.tasks.get(taskId) || null;
  }

  /**
   * 获取所有任务
   */
  getAllTasks(): DownloadTask[] {
    return Array.from(this.tasks.values());
  }

  /**
   * 获取指定状态的任务
   */
  getTasksByStatus(status: TaskStatus): DownloadTask[] {
    return Array.from(this.tasks.values()).filter(task => task.status === status);
  }

  /**
   * 清空已完成和已取消的任务
   */
  clearCompletedTasks(): void {
    const completedTasks: string[] = [];
    
    this.tasks.forEach((task, taskId) => {
      if (task.status === TaskStatus.COMPLETED || task.status === TaskStatus.CANCELLED) {
        completedTasks.push(taskId);
      }
    });

    completedTasks.forEach(taskId => {
      const task = this.tasks.get(taskId);
      this.tasks.delete(taskId);
      if (task) {
        this.emit('taskRemoved', task);
      }
    });
  }

  /**
   * 暂停所有任务
   */
  pauseAll(): void {
    this.tasks.forEach((task, taskId) => {
      if (task.status === TaskStatus.DOWNLOADING || task.status === TaskStatus.PENDING) {
        this.pauseTask(taskId);
      }
    });
  }

  /**
   * 恢复所有暂停的任务
   */
  resumeAll(): void {
    this.tasks.forEach((task, taskId) => {
      if (task.status === TaskStatus.PAUSED) {
        this.resumeTask(taskId);
      }
    });
  }

  /**
   * 更新任务进度
   */
  updateTaskProgress(taskId: string, progress: DownloadProgress): void {
    const task = this.tasks.get(taskId);
    if (!task) {
      return;
    }

    task.progress = progress.progress;
    task.speed = progress.speed;
    task.estimatedTime = progress.estimatedTime;
    task.updatedAt = new Date();

    this.emit('taskProgress', task, progress);
  }

  /**
   * 标记任务完成
   */
  completeTask(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (!task) {
      return;
    }

    this.updateTaskStatus(taskId, TaskStatus.COMPLETED);
    this.activeDownloads.delete(taskId);
    this.emit('taskCompleted', task);
    
    // 处理队列中的其他任务
    this.processQueue();
  }

  /**
   * 标记任务失败
   */
  failTask(taskId: string, error: string): void {
    const task = this.tasks.get(taskId);
    if (!task) {
      return;
    }

    task.error = error;
    this.updateTaskStatus(taskId, TaskStatus.FAILED);
    this.activeDownloads.delete(taskId);
    this.emit('taskFailed', task);
    
    // 处理队列中的其他任务
    this.processQueue();
  }

  /**
   * 获取队列统计信息
   */
  getQueueStats() {
    const stats = {
      total: this.tasks.size,
      pending: 0,
      downloading: 0,
      paused: 0,
      completed: 0,
      failed: 0,
      cancelled: 0
    };

    this.tasks.forEach(task => {
      switch (task.status) {
        case TaskStatus.PENDING:
          stats.pending++;
          break;
        case TaskStatus.DOWNLOADING:
          stats.downloading++;
          break;
        case TaskStatus.PAUSED:
          stats.paused++;
          break;
        case TaskStatus.COMPLETED:
          stats.completed++;
          break;
        case TaskStatus.FAILED:
          stats.failed++;
          break;
        case TaskStatus.CANCELLED:
          stats.cancelled++;
          break;
      }
    });

    return stats;
  }

  /**
   * 处理下载队列
   */
  private processQueue(): void {
    // 如果已达到最大并发数，则不处理新任务
    if (this.activeDownloads.size >= this.config.maxConcurrentDownloads) {
      return;
    }

    // 查找待处理的任务
    const pendingTasks = this.getTasksByStatus(TaskStatus.PENDING);
    
    for (const task of pendingTasks) {
      if (this.activeDownloads.size >= this.config.maxConcurrentDownloads) {
        break;
      }

      this.startDownload(task.id);
    }
  }

  /**
   * 开始下载任务
   */
  private startDownload(taskId: string): void {
    const task = this.tasks.get(taskId);
    if (!task) {
      return;
    }

    this.updateTaskStatus(taskId, TaskStatus.DOWNLOADING);
    this.activeDownloads.add(taskId);
    this.emit('taskStarted', task);
  }

  /**
   * 更新任务状态
   */
  private updateTaskStatus(taskId: string, status: TaskStatus): void {
    const task = this.tasks.get(taskId);
    if (!task) {
      return;
    }

    const oldStatus = task.status;
    task.status = status;
    task.updatedAt = new Date();

    this.emit('taskStatusChanged', task, oldStatus, status);
  }

  /**
   * 移除任务
   */
  removeTask(taskId: string): boolean {
    const task = this.tasks.get(taskId);
    if (!task) {
      return false;
    }

    this.tasks.delete(taskId);
    this.activeDownloads.delete(taskId);
    this.emit('taskRemoved', task);
    return true;
  }

  /**
   * 标记任务完成
   */
  markTaskCompleted(taskId: string): void {
    this.completeTask(taskId);
  }

  /**
   * 标记任务失败
   */
  markTaskFailed(taskId: string, error: string): void {
    this.failTask(taskId, error);
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<QueueConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 获取配置
   */
  getConfig(): QueueConfig {
    return { ...this.config };
  }

  /**
   * 销毁队列
   */
  destroy(): void {
    this.tasks.clear();
    this.activeDownloads.clear();
    this.removeAllListeners();
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
}