import { MemoryMonitor, MemoryInfo, MemoryWarning } from '../MemoryMonitor';
import * as os from 'os';

// Mock os module
jest.mock('os');
const mockOs = os as jest.Mocked<typeof os>;

// Mock process.memoryUsage
const originalMemoryUsage = process.memoryUsage;
const mockMemoryUsage = jest.fn();

describe('MemoryMonitor', () => {
  let monitor: MemoryMonitor;

  beforeEach(() => {
    monitor = new MemoryMonitor(1000); // 1秒检查间隔
    jest.clearAllMocks();

    // Setup process.memoryUsage mock
    (process as any).memoryUsage = mockMemoryUsage;

    // Setup default mocks
    mockOs.totalmem.mockReturnValue(16 * 1024 * 1024 * 1024); // 16GB
    mockOs.freemem.mockReturnValue(4 * 1024 * 1024 * 1024);   // 4GB

    // Mock process.memoryUsage
    mockMemoryUsage.mockReturnValue({
      rss: 200 * 1024 * 1024,
      heapTotal: 150 * 1024 * 1024,
      heapUsed: 120 * 1024 * 1024,
      external: 10 * 1024 * 1024,
      arrayBuffers: 5 * 1024 * 1024
    });
  });

  afterEach(() => {
    monitor.stopMonitoring();
    process.memoryUsage = originalMemoryUsage;
  });

  describe('getMemoryInfo', () => {
    it('should return correct memory information', () => {
      const result = monitor.getMemoryInfo();

      expect(result).toEqual({
        totalMemory: 16 * 1024 * 1024 * 1024,
        freeMemory: 4 * 1024 * 1024 * 1024,
        usedMemory: 12 * 1024 * 1024 * 1024,
        usagePercentage: 0.75,
        processMemory: {
          rss: 200 * 1024 * 1024,
          heapTotal: 150 * 1024 * 1024,
          heapUsed: 120 * 1024 * 1024,
          external: 10 * 1024 * 1024,
          arrayBuffers: 5 * 1024 * 1024
        }
      });
    });
  });

  describe('checkMemoryUsage', () => {
    it('should return warning when process memory usage is high', () => {
      (process as any).memoryUsage = mockMemoryUsage.mockReturnValue({
        rss: 200 * 1024 * 1024,
        heapTotal: 600 * 1024 * 1024,
        heapUsed: 600 * 1024 * 1024, // High heap usage
        external: 10 * 1024 * 1024,
        arrayBuffers: 5 * 1024 * 1024
      });

      const warning = monitor.checkMemoryUsage();

      expect(warning).toEqual({
        level: 'warning',
        message: expect.stringContaining('应用程序内存使用过高'),
        currentUsage: 0.75,
        processMemory: expect.any(Object)
      });
    });

    it('should return critical warning when system memory usage exceeds 90%', () => {
      mockOs.totalmem.mockReturnValue(16 * 1024 * 1024 * 1024);
      mockOs.freemem.mockReturnValue(1 * 1024 * 1024 * 1024); // Only 1GB free (93.75% used)

      const warning = monitor.checkMemoryUsage();

      expect(warning).toEqual({
        level: 'critical',
        message: expect.stringContaining('系统内存严重不足'),
        currentUsage: expect.any(Number),
        processMemory: expect.any(Object)
      });
    });

    it('should return warning when system memory usage exceeds 80%', () => {
      mockOs.totalmem.mockReturnValue(16 * 1024 * 1024 * 1024);
      mockOs.freemem.mockReturnValue(2 * 1024 * 1024 * 1024); // 2GB free (87.5% used)

      const warning = monitor.checkMemoryUsage();

      expect(warning).toEqual({
        level: 'warning',
        message: expect.stringContaining('系统内存使用率较高'),
        currentUsage: expect.any(Number),
        processMemory: expect.any(Object)
      });
    });

    it('should return null when memory usage is normal', () => {
      mockOs.totalmem.mockReturnValue(16 * 1024 * 1024 * 1024);
      mockOs.freemem.mockReturnValue(8 * 1024 * 1024 * 1024); // 8GB free (50% used)

      (process as any).memoryUsage = mockMemoryUsage.mockReturnValue({
        rss: 200 * 1024 * 1024,
        heapTotal: 150 * 1024 * 1024,
        heapUsed: 120 * 1024 * 1024, // Normal heap usage
        external: 10 * 1024 * 1024,
        arrayBuffers: 5 * 1024 * 1024
      });

      const warning = monitor.checkMemoryUsage();

      expect(warning).toBeNull();
    });
  });

  describe('startMonitoring', () => {
    it('should start and stop monitoring without errors', () => {
      monitor.startMonitoring();
      expect(true).toBe(true); // Should not throw
      monitor.stopMonitoring();
    });
  });

  describe('forceGarbageCollection', () => {
    it('should return true when gc is available', () => {
      (global as any).gc = jest.fn();
      
      const result = monitor.forceGarbageCollection();
      
      expect(result).toBe(true);
      expect(global.gc).toHaveBeenCalled();
    });

    it('should return false when gc is not available', () => {
      delete (global as any).gc;
      
      const result = monitor.forceGarbageCollection();
      
      expect(result).toBe(false);
    });
  });

  describe('getOptimizationSuggestions', () => {
    it('should suggest reducing concurrent downloads when memory usage is high', () => {
      mockOs.freemem.mockReturnValue(1 * 1024 * 1024 * 1024); // Low free memory

      const suggestions = monitor.getOptimizationSuggestions();

      expect(suggestions).toContainEqual({
        action: 'reduce_concurrent_downloads',
        description: '减少并发下载数量以降低内存使用',
        priority: 'high'
      });
    });

    it('should suggest garbage collection when process memory is high', () => {
      (process as any).memoryUsage = mockMemoryUsage.mockReturnValue({
        rss: 200 * 1024 * 1024,
        heapTotal: 600 * 1024 * 1024,
        heapUsed: 600 * 1024 * 1024, // High heap usage
        external: 10 * 1024 * 1024,
        arrayBuffers: 5 * 1024 * 1024
      });

      const suggestions = monitor.getOptimizationSuggestions();

      expect(suggestions).toContainEqual({
        action: 'force_garbage_collection',
        description: '执行垃圾回收以释放内存',
        priority: 'medium'
      });
    });
  });

  describe('getMemoryStats', () => {
    it('should return correct stats when history is available', () => {
      // Start monitoring to build history
      monitor.startMonitoring();
      
      // Wait a bit for history to build
      setTimeout(() => {
        const stats = monitor.getMemoryStats();
        
        expect(stats).toHaveProperty('averageUsage');
        expect(stats).toHaveProperty('peakUsage');
        expect(stats).toHaveProperty('currentUsage');
        expect(stats.currentUsage).toBe(0.75);
      }, 100);
    });

    it('should return current usage when no history is available', () => {
      const stats = monitor.getMemoryStats();
      
      expect(stats.averageUsage).toBe(0.75);
      expect(stats.peakUsage).toBe(0.75);
      expect(stats.currentUsage).toBe(0.75);
    });
  });

  describe('performance tests', () => {
    it('should handle multiple concurrent getMemoryInfo calls', () => {
      const promises = Array(100).fill(0).map(() => 
        Promise.resolve(monitor.getMemoryInfo())
      );

      return Promise.all(promises).then(results => {
        expect(results).toHaveLength(100);
        results.forEach(result => {
          expect(result.usagePercentage).toBe(0.75);
        });
      });
    });

    it('should complete memory check within reasonable time', () => {
      const startTime = Date.now();
      const memoryInfo = monitor.getMemoryInfo();
      const elapsed = Date.now() - startTime;
      
      expect(elapsed).toBeLessThan(100); // Should complete within 100ms
      expect(memoryInfo).toHaveProperty('usagePercentage');
    });

    it('should handle memory history efficiently', () => {
      const history = monitor.getMemoryHistory();
      expect(Array.isArray(history)).toBe(true);
      expect(history.length).toBeLessThanOrEqual(100); // Should not exceed max history size
    });

    it('should detect memory usage patterns', () => {
      const memoryInfo = monitor.getMemoryInfo();
      expect(memoryInfo.usagePercentage).toBeGreaterThanOrEqual(0);
      expect(memoryInfo.usagePercentage).toBeLessThanOrEqual(1);
    });
  });
});