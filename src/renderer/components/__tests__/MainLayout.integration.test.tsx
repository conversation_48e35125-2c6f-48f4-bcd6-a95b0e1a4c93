import React from 'react';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { MainLayout } from '../MainLayout';
import { AppConfig } from '../../../shared/types';

// Mock hooks
jest.mock('../../hooks/useAuth', () => ({
  useAuth: () => ({
    authState: 'LOGGED_IN',
    user: {
      id: '1',
      username: 'testuser',
      displayName: '测试用户',
      permissions: []
    },
    isLoggedIn: true,
    isGuestMode: false,
    logout: jest.fn()
  })
}));

jest.mock('../../hooks/useDownload', () => ({
  useDownload: () => ({
    tasks: [],
    stats: {
      totalTasks: 0,
      completedTasks: 0,
      failedTasks: 0,
      activeTasks: 0,
      totalDownloadedBytes: 0,
      averageSpeed: 0
    },
    pauseTask: jest.fn(),
    resumeTask: jest.fn(),
    cancelTask: jest.fn(),
    retryTask: jest.fn(),
    clearTask: jest.fn(),
    pauseAll: jest.fn(),
    resumeAll: jest.fn(),
    cancelAll: jest.fn(),
    clearCompleted: jest.fn(),
    downloadResource: jest.fn(),
    downloadBatch: jest.fn()
  })
}));

// Mock Electron API
Object.defineProperty(window, 'electronAPI', {
  value: {
    showOpenDialog: jest.fn().mockResolvedValue({
      canceled: false,
      filePaths: ['/test/path']
    }),
    getAppName: jest.fn().mockResolvedValue('智慧教育下载器'),
    getAppVersion: jest.fn().mockResolvedValue('1.0.0')
  }
});

const defaultConfig: AppConfig = {
  downloadPath: './downloads',
  maxConcurrentDownloads: 3,
  requestTimeout: 30000,
  retryAttempts: 3,
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
  fileOrganization: {
    basePath: './downloads',
    namingPattern: '{subject}/{grade}/{volume}/{title}',
    createSubfolders: true,
    groupBySubject: true,
    groupByGrade: true
  }
};

const renderMainLayout = (config = defaultConfig, onConfigChange = jest.fn()) => {
  return render(
    <ConfigProvider locale={zhCN}>
      <MainLayout config={config} onConfigChange={onConfigChange} />
    </ConfigProvider>
  );
};

describe('MainLayout Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('初始渲染', () => {
    it('应该正确渲染主界面布局', () => {
      renderMainLayout();
      
      // 检查侧边栏
      expect(screen.getByText('智慧教育下载器')).toBeInTheDocument();
      expect(screen.getByText('仪表板')).toBeInTheDocument();
      expect(screen.getByText('资源管理')).toBeInTheDocument();
      expect(screen.getByText('下载管理')).toBeInTheDocument();
      expect(screen.getByText('系统监控')).toBeInTheDocument();
      
      // 检查顶部导航栏
      expect(screen.getByText('测试用户')).toBeInTheDocument();
      
      // 检查默认显示仪表板
      expect(screen.getByText('仪表板')).toBeInTheDocument();
      expect(screen.getByText('总下载任务')).toBeInTheDocument();
      expect(screen.getByText('进行中任务')).toBeInTheDocument();
      expect(screen.getByText('已下载大小')).toBeInTheDocument();
    });

    it('应该显示正确的统计信息', () => {
      renderMainLayout();
      
      // 检查统计卡片
      const statsCards = screen.getAllByText('0');
      expect(statsCards.length).toBeGreaterThan(0);
      
      // 检查快速操作按钮
      expect(screen.getByText('搜索资源')).toBeInTheDocument();
      expect(screen.getByText('查看下载 (0)')).toBeInTheDocument();
      expect(screen.getByText('系统监控')).toBeInTheDocument();
      expect(screen.getByText('应用设置')).toBeInTheDocument();
    });
  });

  describe('导航功能', () => {
    it('应该能够切换到不同的视图', async () => {
      renderMainLayout();
      
      // 切换到资源管理
      fireEvent.click(screen.getByText('资源管理'));
      await waitFor(() => {
        expect(screen.getByText('资源管理')).toBeInTheDocument();
      });
      
      // 切换到下载管理
      fireEvent.click(screen.getByText('下载管理'));
      await waitFor(() => {
        expect(screen.getByText('下载管理器')).toBeInTheDocument();
      });
      
      // 切换到系统监控
      fireEvent.click(screen.getByText('系统监控'));
      await waitFor(() => {
        expect(screen.getByText('磁盘空间')).toBeInTheDocument();
        expect(screen.getByText('内存使用')).toBeInTheDocument();
        expect(screen.getByText('网络状态')).toBeInTheDocument();
      });
    });

    it('应该能够折叠和展开侧边栏', () => {
      renderMainLayout();
      
      // 查找折叠按钮
      const collapseButton = screen.getByRole('button', { name: /fold/i });
      
      // 点击折叠
      fireEvent.click(collapseButton);
      
      // 验证侧边栏已折叠（文字应该隐藏）
      // 注意：由于Ant Design的实现，我们需要检查特定的类名或样式
    });
  });

  describe('筛选功能', () => {
    it('应该能够打开筛选抽屉', async () => {
      renderMainLayout();
      
      // 点击搜索资源按钮
      fireEvent.click(screen.getByText('搜索资源'));
      
      // 等待抽屉打开
      await waitFor(() => {
        expect(screen.getByText('资源筛选')).toBeInTheDocument();
      });
    });

    it('应该能够通过浮动按钮打开筛选', async () => {
      renderMainLayout();
      
      // 查找浮动按钮组
      const floatButtons = document.querySelectorAll('.ant-float-btn');
      expect(floatButtons.length).toBeGreaterThan(0);
    });
  });

  describe('设置功能', () => {
    it('应该能够打开设置面板', async () => {
      renderMainLayout();
      
      // 点击应用设置按钮
      fireEvent.click(screen.getByText('应用设置'));
      
      // 等待设置面板打开
      await waitFor(() => {
        expect(screen.getByText('应用设置')).toBeInTheDocument();
      });
    });

    it('应该能够通过用户菜单打开设置', async () => {
      renderMainLayout();
      
      // 点击用户名打开下拉菜单
      fireEvent.click(screen.getByText('测试用户'));
      
      // 等待菜单出现并点击设置
      await waitFor(() => {
        const settingsMenuItem = screen.getByText('设置');
        fireEvent.click(settingsMenuItem);
      });
      
      // 验证设置面板打开
      await waitFor(() => {
        expect(screen.getByText('应用设置')).toBeInTheDocument();
      });
    });
  });

  describe('用户交互', () => {
    it('应该显示用户信息和操作菜单', async () => {
      renderMainLayout();
      
      // 点击用户名
      fireEvent.click(screen.getByText('测试用户'));
      
      // 验证菜单项
      await waitFor(() => {
        expect(screen.getByText('个人信息')).toBeInTheDocument();
        expect(screen.getByText('设置')).toBeInTheDocument();
        expect(screen.getByText('退出登录')).toBeInTheDocument();
      });
    });

    it('应该能够处理登出操作', async () => {
      const mockLogout = jest.fn();
      
      // 重新mock useAuth以包含logout函数
      jest.doMock('../../hooks/useAuth', () => ({
        useAuth: () => ({
          authState: 'LOGGED_IN',
          user: {
            id: '1',
            username: 'testuser',
            displayName: '测试用户',
            permissions: []
          },
          isLoggedIn: true,
          isGuestMode: false,
          logout: mockLogout
        })
      }));
      
      renderMainLayout();
      
      // 点击用户名打开菜单
      fireEvent.click(screen.getByText('测试用户'));
      
      // 点击退出登录
      await waitFor(() => {
        const logoutButton = screen.getByText('退出登录');
        fireEvent.click(logoutButton);
      });
      
      // 验证logout函数被调用
      expect(mockLogout).toHaveBeenCalled();
    });
  });

  describe('响应式设计', () => {
    it('应该在移动设备上正确显示', () => {
      // 模拟移动设备视口
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 375,
      });
      
      Object.defineProperty(window, 'innerHeight', {
        writable: true,
        configurable: true,
        value: 667,
      });
      
      renderMainLayout();
      
      // 验证基本布局仍然存在
      expect(screen.getByText('智慧教育下载器')).toBeInTheDocument();
      expect(screen.getByText('测试用户')).toBeInTheDocument();
    });
  });

  describe('配置管理', () => {
    it('应该能够更新配置', async () => {
      const mockOnConfigChange = jest.fn();
      renderMainLayout(defaultConfig, mockOnConfigChange);
      
      // 打开设置面板
      fireEvent.click(screen.getByText('应用设置'));
      
      await waitFor(() => {
        expect(screen.getByText('应用设置')).toBeInTheDocument();
      });
      
      // 这里可以添加更多的配置更新测试
    });

    it('应该正确保存和加载配置', () => {
      const customConfig: AppConfig = {
        ...defaultConfig,
        maxConcurrentDownloads: 5,
        downloadPath: '/custom/path'
      };
      
      renderMainLayout(customConfig);
      
      // 验证配置被正确应用
      // 这里需要根据实际的UI实现来验证
    });
  });

  describe('错误处理', () => {
    it('应该优雅地处理网络错误', async () => {
      // Mock网络错误
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      renderMainLayout();
      
      // 触发可能导致网络错误的操作
      fireEvent.click(screen.getByText('搜索资源'));
      
      // 验证错误被正确处理
      await waitFor(() => {
        // 这里应该检查错误处理的UI反馈
      });
      
      consoleSpy.mockRestore();
    });

    it('应该处理无效的配置', () => {
      const invalidConfig = {
        ...defaultConfig,
        maxConcurrentDownloads: -1 // 无效值
      };
      
      // 应该不会崩溃
      expect(() => renderMainLayout(invalidConfig)).not.toThrow();
    });
  });

  describe('性能测试', () => {
    it('应该在合理时间内渲染', () => {
      const startTime = performance.now();
      renderMainLayout();
      const endTime = performance.now();
      
      // 渲染时间应该少于100ms
      expect(endTime - startTime).toBeLessThan(100);
    });

    it('应该正确清理资源', () => {
      const { unmount } = renderMainLayout();
      
      // 卸载组件
      unmount();
      
      // 验证没有内存泄漏或未清理的定时器
      // 这里可以添加更具体的清理验证
    });
  });
});

describe('MainLayout Accessibility Tests', () => {
  it('应该具有正确的ARIA标签', () => {
    renderMainLayout();
    
    // 检查主要区域的ARIA标签
    const navigation = screen.getByRole('navigation');
    expect(navigation).toBeInTheDocument();
    
    const main = screen.getByRole('main');
    expect(main).toBeInTheDocument();
  });

  it('应该支持键盘导航', () => {
    renderMainLayout();
    
    // 测试Tab键导航
    const firstFocusableElement = screen.getByRole('button', { name: /fold/i });
    firstFocusableElement.focus();
    
    expect(document.activeElement).toBe(firstFocusableElement);
  });

  it('应该有适当的颜色对比度', () => {
    renderMainLayout();
    
    // 这里可以添加颜色对比度的测试
    // 通常需要使用专门的工具来测试
  });
});