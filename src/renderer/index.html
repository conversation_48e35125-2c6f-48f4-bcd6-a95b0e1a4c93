<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智慧教育下载器</title>
    <script>
        // 修复 global 变量未定义的问题
        if (typeof global === 'undefined') {
            window.global = window;
        }
        if (typeof globalThis === 'undefined') {
            window.globalThis = window;
        }
        console.log('✅ Global polyfills loaded in HTML');
    </script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f5f5f5;
        }
        #root {
            height: 100vh;
        }
    </style>
</head>
<body>
    <div id="root"></div>
</body>
</html>