import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ErrorDisplay } from '../ErrorDisplay';
import { SmartEduError, ErrorType, NetworkError, ApiError, FileError } from '../../../shared/types';
import { RetryState } from '../../../shared/services/RetryManager';

describe('ErrorDisplay', () => {
  const mockRetryState: RetryState = {
    attempt: 2,
    totalDelay: 3000,
    startTime: new Date('2023-01-01T10:00:00Z'),
    nextRetryAt: new Date('2023-01-01T10:00:05Z'),
    errorHistory: [
      {
        error: new NetworkError('第一次网络错误'),
        timestamp: new Date('2023-01-01T10:00:01Z'),
        attempt: 1
      },
      {
        error: new NetworkError('第二次网络错误'),
        timestamp: new Date('2023-01-01T10:00:03Z'),
        attempt: 2
      }
    ]
  };

  describe('基本渲染', () => {
    it('应该渲染网络错误', () => {
      const networkError = new NetworkError('网络连接失败');
      
      render(<ErrorDisplay error={networkError} />);
      
      expect(screen.getByText('网络连接出现问题，请检查网络设置后重试')).toBeInTheDocument();
      expect(screen.getByText('检查网络连接是否正常')).toBeInTheDocument();
    });

    it('应该渲染API错误', () => {
      const apiError = new ApiError('服务器响应异常');
      
      render(<ErrorDisplay error={apiError} />);
      
      expect(screen.getByText('服务器响应异常，请稍后重试')).toBeInTheDocument();
      expect(screen.getByText('检查服务器状态')).toBeInTheDocument();
    });

    it('应该渲染文件错误', () => {
      const fileError = new FileError('文件操作失败');
      
      render(<ErrorDisplay error={fileError} />);
      
      expect(screen.getByText('文件操作失败，请检查磁盘空间和权限')).toBeInTheDocument();
      expect(screen.getByText('检查磁盘空间是否充足')).toBeInTheDocument();
    });

    it('应该渲染普通错误', () => {
      const error = new Error('未知错误');
      
      render(<ErrorDisplay error={error} />);
      
      expect(screen.getByText('未知错误')).toBeInTheDocument();
      expect(screen.getByText('请联系技术支持')).toBeInTheDocument();
    });

    it('应该根据错误消息推断错误类型', () => {
      const timeoutError = new Error('请求超时');
      
      render(<ErrorDisplay error={timeoutError} />);
      
      expect(screen.getByText('请求超时')).toBeInTheDocument();
    });
  });

  describe('重试信息显示', () => {
    it('应该显示重试状态信息', () => {
      const networkError = new NetworkError('网络错误');
      
      render(
        <ErrorDisplay 
          error={networkError} 
          retryState={mockRetryState}
        />
      );
      
      expect(screen.getByText('重试次数:')).toBeInTheDocument();
      expect(screen.getByText('2')).toBeInTheDocument();
      expect(screen.getByText('下次重试:')).toBeInTheDocument();
      expect(screen.getByText('总等待时间:')).toBeInTheDocument();
      expect(screen.getByText('3秒')).toBeInTheDocument();
    });

    it('应该在没有重试状态时不显示重试信息', () => {
      const networkError = new NetworkError('网络错误');
      
      render(<ErrorDisplay error={networkError} />);
      
      expect(screen.queryByText('重试次数:')).not.toBeInTheDocument();
    });
  });

  describe('错误详情', () => {
    it('应该在showDetails为true时显示错误详情', () => {
      const networkError = new NetworkError('网络错误', { 
        statusCode: 500,
        context: { url: 'https://example.com' },
        timestamp: new Date()
      });
      
      render(
        <ErrorDisplay 
          error={networkError} 
          retryState={mockRetryState}
          showDetails={true}
        />
      );
      
      // 点击展开详情
      fireEvent.click(screen.getByText('错误详情'));
      
      expect(screen.getByText('错误类型:')).toBeInTheDocument();
      expect(screen.getByText('NETWORK_ERROR')).toBeInTheDocument();
      expect(screen.getByText('原始消息:')).toBeInTheDocument();
      expect(screen.getByText('详细信息:')).toBeInTheDocument();
    });

    it('应该显示错误历史', () => {
      const networkError = new NetworkError('网络错误');
      
      render(
        <ErrorDisplay 
          error={networkError} 
          retryState={mockRetryState}
          showDetails={true}
        />
      );
      
      // 点击展开详情
      fireEvent.click(screen.getByText('错误详情'));
      
      expect(screen.getByText('错误历史:')).toBeInTheDocument();
      expect(screen.getByText(/第1次/)).toBeInTheDocument();
      expect(screen.getByText(/第2次/)).toBeInTheDocument();
    });

    it('应该在showDetails为false时不显示错误详情', () => {
      const networkError = new NetworkError('网络错误');
      
      render(
        <ErrorDisplay 
          error={networkError} 
          showDetails={false}
        />
      );
      
      expect(screen.queryByText('错误详情')).not.toBeInTheDocument();
    });
  });

  describe('操作按钮', () => {
    it('应该显示重试按钮并处理点击', () => {
      const networkError = new NetworkError('网络错误');
      const onRetry = jest.fn();
      
      render(
        <ErrorDisplay 
          error={networkError} 
          onRetry={onRetry}
        />
      );
      
      const retryButton = screen.getByText('重试');
      expect(retryButton).toBeInTheDocument();
      
      fireEvent.click(retryButton);
      expect(onRetry).toHaveBeenCalledTimes(1);
    });

    it('应该显示忽略按钮并处理点击', () => {
      const networkError = new NetworkError('网络错误');
      const onDismiss = jest.fn();
      
      render(
        <ErrorDisplay 
          error={networkError} 
          onDismiss={onDismiss}
        />
      );
      
      const dismissButton = screen.getByText(/忽.*略/);
      expect(dismissButton).toBeInTheDocument();
      
      fireEvent.click(dismissButton);
      expect(onDismiss).toHaveBeenCalledTimes(1);
    });

    it('应该对不可重试的错误隐藏重试按钮', () => {
      const fileError = new FileError('文件错误');
      const onRetry = jest.fn();
      
      render(
        <ErrorDisplay 
          error={fileError} 
          onRetry={onRetry}
        />
      );
      
      expect(screen.queryByText('重试')).not.toBeInTheDocument();
    });

    it('应该在没有回调函数时不显示按钮', () => {
      const networkError = new NetworkError('网络错误');
      
      render(<ErrorDisplay error={networkError} />);
      
      expect(screen.queryByText('重试')).not.toBeInTheDocument();
      expect(screen.queryByText('忽略')).not.toBeInTheDocument();
    });
  });

  describe('错误严重程度', () => {
    it('应该为可重试错误显示警告样式', () => {
      const networkError = new NetworkError('网络错误');
      
      render(<ErrorDisplay error={networkError} />);
      
      const alert = screen.getByRole('alert');
      expect(alert).toHaveClass('ant-alert-warning');
    });

    it('应该为不可重试错误显示错误样式', () => {
      const fileError = new FileError('文件错误');
      
      render(<ErrorDisplay error={fileError} />);
      
      const alert = screen.getByRole('alert');
      expect(alert).toHaveClass('ant-alert-error');
    });
  });

  describe('解决建议', () => {
    it('应该为网络错误显示相应建议', () => {
      const networkError = new NetworkError('网络错误');
      
      render(<ErrorDisplay error={networkError} />);
      
      expect(screen.getByText('解决建议:')).toBeInTheDocument();
      expect(screen.getByText('检查网络连接是否正常')).toBeInTheDocument();
      expect(screen.getByText('尝试切换网络环境')).toBeInTheDocument();
      expect(screen.getByText('检查防火墙设置')).toBeInTheDocument();
      expect(screen.getByText('稍后重试')).toBeInTheDocument();
    });

    it('应该为API错误显示相应建议', () => {
      const apiError = new ApiError('API错误');
      
      render(<ErrorDisplay error={apiError} />);
      
      expect(screen.getByText('检查服务器状态')).toBeInTheDocument();
      expect(screen.getByText('稍后重试')).toBeInTheDocument();
      expect(screen.getByText('联系技术支持')).toBeInTheDocument();
    });

    it('应该为文件错误显示相应建议', () => {
      const fileError = new FileError('文件错误');
      
      render(<ErrorDisplay error={fileError} />);
      
      expect(screen.getByText('检查磁盘空间是否充足')).toBeInTheDocument();
      expect(screen.getByText('检查文件权限')).toBeInTheDocument();
      expect(screen.getByText('尝试更换存储位置')).toBeInTheDocument();
    });

    it('应该为下载错误显示相应建议', () => {
      const downloadError = new SmartEduError(
        ErrorType.DOWNLOAD_ERROR,
        '下载失败',
        {},
        true
      );
      
      render(<ErrorDisplay error={downloadError} />);
      
      expect(screen.getByText('检查网络连接')).toBeInTheDocument();
      expect(screen.getByText('重新开始下载')).toBeInTheDocument();
      expect(screen.getByText('尝试单个文件下载')).toBeInTheDocument();
    });

    it('应该为认证错误显示相应建议', () => {
      const authError = new SmartEduError(
        ErrorType.AUTH_ERROR,
        '认证失败',
        {},
        false
      );
      
      render(<ErrorDisplay error={authError} />);
      
      expect(screen.getByText('重新登录账户')).toBeInTheDocument();
      expect(screen.getByText('检查账户状态')).toBeInTheDocument();
      expect(screen.getByText('清除浏览器缓存')).toBeInTheDocument();
    });

    it('应该为M3U8错误显示相应建议', () => {
      const m3u8Error = new SmartEduError(
        ErrorType.M3U8_ERROR,
        'M3U8解析失败',
        {},
        true
      );
      
      render(<ErrorDisplay error={m3u8Error} />);
      
      expect(screen.getByText('检查视频链接有效性')).toBeInTheDocument();
      expect(screen.getByText('尝试重新获取视频信息')).toBeInTheDocument();
      expect(screen.getByText('联系技术支持')).toBeInTheDocument();
    });

    it('应该为FFmpeg错误显示相应建议', () => {
      const ffmpegError = new SmartEduError(
        ErrorType.FFMPEG_ERROR,
        'FFmpeg处理失败',
        {},
        false
      );
      
      render(<ErrorDisplay error={ffmpegError} />);
      
      expect(screen.getByText('检查FFmpeg是否正确安装')).toBeInTheDocument();
      expect(screen.getByText('检查视频文件完整性')).toBeInTheDocument();
      expect(screen.getByText('尝试重新下载视频')).toBeInTheDocument();
    });

    it('应该为磁盘空间错误显示相应建议', () => {
      const diskSpaceError = new SmartEduError(
        ErrorType.DISK_SPACE_ERROR,
        '磁盘空间不足',
        {},
        false
      );
      
      render(<ErrorDisplay error={diskSpaceError} />);
      
      expect(screen.getByText('清理磁盘空间')).toBeInTheDocument();
      expect(screen.getByText('更换存储位置')).toBeInTheDocument();
      expect(screen.getByText('删除不必要的文件')).toBeInTheDocument();
    });
  });

  describe('自定义样式', () => {
    it('应该应用自定义className', () => {
      const networkError = new NetworkError('网络错误');
      
      const { container } = render(
        <ErrorDisplay 
          error={networkError} 
          className="custom-error-display"
        />
      );
      
      expect(container.firstChild).toHaveClass('custom-error-display');
    });
  });

  describe('可访问性', () => {
    it('应该有正确的ARIA属性', () => {
      const networkError = new NetworkError('网络错误');
      
      render(<ErrorDisplay error={networkError} />);
      
      const alert = screen.getByRole('alert');
      expect(alert).toBeInTheDocument();
    });

    it('应该支持键盘导航', () => {
      const networkError = new NetworkError('网络错误');
      const onRetry = jest.fn();
      
      render(
        <ErrorDisplay 
          error={networkError} 
          onRetry={onRetry}
        />
      );
      
      const retryButton = screen.getByText('重试');
      retryButton.focus();
      
      fireEvent.click(retryButton);
      expect(onRetry).toHaveBeenCalledTimes(1);
    });
  });

  describe('边界情况', () => {
    it('应该处理空错误消息', () => {
      const error = new Error('');
      
      render(<ErrorDisplay error={error} />);
      
      expect(screen.getByText('发生未知错误')).toBeInTheDocument();
    });

    it('应该处理undefined错误消息', () => {
      const error = new Error();
      error.message = undefined as any;
      
      render(<ErrorDisplay error={error} />);
      
      expect(screen.getByText('发生未知错误')).toBeInTheDocument();
    });

    it('应该处理空的错误历史', () => {
      const networkError = new NetworkError('网络错误');
      const emptyRetryState: RetryState = {
        attempt: 0,
        totalDelay: 0,
        startTime: new Date(),
        errorHistory: []
      };
      
      render(
        <ErrorDisplay 
          error={networkError} 
          retryState={emptyRetryState}
          showDetails={true}
        />
      );
      
      // 点击展开详情
      fireEvent.click(screen.getByText('错误详情'));
      
      expect(screen.queryByText('错误历史:')).not.toBeInTheDocument();
    });

    it('应该处理没有详细信息的SmartEduError', () => {
      const error = new SmartEduError(
        ErrorType.NETWORK_ERROR,
        '网络错误',
        undefined as any,
        true
      );
      
      render(
        <ErrorDisplay 
          error={error} 
          showDetails={true}
        />
      );
      
      // 点击展开详情
      fireEvent.click(screen.getByText('错误详情'));
      
      expect(screen.queryByText('详细信息:')).not.toBeInTheDocument();
    });
  });
});