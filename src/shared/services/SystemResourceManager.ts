import { EventEmitter } from 'events';
import { DiskSpaceMonitor, DiskSpaceInfo, DiskSpaceWarning } from './DiskSpaceMonitor';
import { MemoryMonitor, MemoryInfo, MemoryWarning, MemoryOptimizationSuggestion } from './MemoryMonitor';
import { NetworkMonitor, NetworkStatus, DownloadStrategy } from './NetworkMonitor';

export interface SystemResourceStatus {
  disk: DiskSpaceInfo | null;
  memory: MemoryInfo | null;
  network: NetworkStatus | null;
  warnings: SystemWarning[];
  suggestions: SystemSuggestion[];
  overallHealth: 'excellent' | 'good' | 'warning' | 'critical';
}

export interface SystemWarning {
  type: 'disk' | 'memory' | 'network';
  level: 'warning' | 'critical';
  message: string;
  timestamp: Date;
}

export interface SystemSuggestion {
  type: 'disk' | 'memory' | 'network' | 'download';
  action: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
}

export interface SystemResourceConfig {
  diskMonitorInterval?: number;
  memoryMonitorInterval?: number;
  networkMonitorInterval?: number;
  downloadPath?: string;
  enableAutoOptimization?: boolean;
}

export class SystemResourceManager extends EventEmitter {
  private diskMonitor: DiskSpaceMonitor;
  private memoryMonitor: MemoryMonitor;
  private networkMonitor: NetworkMonitor;
  private isMonitoring = false;
  private warnings: SystemWarning[] = [];
  private readonly maxWarningsHistory = 50;

  constructor(private config: SystemResourceConfig = {}) {
    super();
    
    this.diskMonitor = new DiskSpaceMonitor(config.diskMonitorInterval || 30000);
    this.memoryMonitor = new MemoryMonitor(config.memoryMonitorInterval || 10000);
    this.networkMonitor = new NetworkMonitor(config.networkMonitorInterval || 30000);

    this.setupEventListeners();
  }

  /**
   * 开始系统资源监控
   */
  async startMonitoring(): Promise<void> {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;

    // 启动各个监控器
    if (this.config.downloadPath) {
      this.diskMonitor.startMonitoring(this.config.downloadPath);
    }
    this.memoryMonitor.startMonitoring();
    await this.networkMonitor.startMonitoring();

    this.emit('monitoringStarted');
  }

  /**
   * 停止系统资源监控
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;

    this.diskMonitor.stopMonitoring();
    this.memoryMonitor.stopMonitoring();
    this.networkMonitor.stopMonitoring();

    this.emit('monitoringStopped');
  }

  /**
   * 获取当前系统资源状态
   */
  async getSystemStatus(): Promise<SystemResourceStatus> {
    const disk = this.config.downloadPath 
      ? await this.diskMonitor.getDiskSpaceInfo(this.config.downloadPath).catch(() => null)
      : null;
    
    const memory = this.memoryMonitor.getMemoryInfo();
    const network = this.networkMonitor.getCurrentStatus();

    const suggestions = this.generateSuggestions(disk, memory, network);
    const overallHealth = this.calculateOverallHealth(disk, memory, network);

    return {
      disk,
      memory,
      network,
      warnings: [...this.warnings],
      suggestions,
      overallHealth
    };
  }

  /**
   * 获取下载策略建议
   */
  getDownloadStrategy(): DownloadStrategy {
    const networkStrategy = this.networkMonitor.getDownloadStrategy();
    const memoryInfo = this.memoryMonitor.getMemoryInfo();

    // 根据内存使用情况调整策略
    if (memoryInfo.usagePercentage > 0.8) {
      networkStrategy.maxConcurrentDownloads = Math.min(
        networkStrategy.maxConcurrentDownloads, 
        2
      );
    }

    return networkStrategy;
  }

  /**
   * 执行系统优化
   */
  async optimizeSystem(): Promise<{
    success: boolean;
    actions: string[];
    errors: string[];
  }> {
    const actions: string[] = [];
    const errors: string[] = [];

    try {
      // 执行垃圾回收
      if (this.memoryMonitor.forceGarbageCollection()) {
        actions.push('执行了垃圾回收');
      }

      // 清理内存历史
      this.memoryMonitor.clearHistory();
      actions.push('清理了内存监控历史');

      // 清理警告历史
      this.clearWarnings();
      actions.push('清理了警告历史');

      this.emit('systemOptimized', { actions, errors });

      return {
        success: errors.length === 0,
        actions,
        errors
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      errors.push(`优化失败: ${errorMessage}`);
      return {
        success: false,
        actions,
        errors
      };
    }
  }

  /**
   * 检查是否应该暂停下载
   */
  shouldPauseDownloads(): boolean {
    const networkShouldPause = this.networkMonitor.shouldPauseDownloads();
    const memoryInfo = this.memoryMonitor.getMemoryInfo();
    const memoryShouldPause = memoryInfo.usagePercentage > 0.9;

    return networkShouldPause || memoryShouldPause;
  }

  /**
   * 检查磁盘空间是否足够
   */
  async hasEnoughDiskSpace(requiredBytes: number): Promise<boolean> {
    if (!this.config.downloadPath) {
      return true; // 无法检查，假设足够
    }

    return this.diskMonitor.hasEnoughSpace(this.config.downloadPath, requiredBytes);
  }

  /**
   * 获取系统清理建议
   */
  async getCleanupSuggestions(): Promise<string[]> {
    if (!this.config.downloadPath) {
      return [];
    }

    return this.diskMonitor.getCleanupSuggestions(this.config.downloadPath);
  }

  /**
   * 设置监控配置
   */
  updateConfig(newConfig: Partial<SystemResourceConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // 如果正在监控，重启监控以应用新配置
    if (this.isMonitoring) {
      this.stopMonitoring();
      this.startMonitoring();
    }
  }

  /**
   * 获取监控状态
   */
  isMonitoringActive(): boolean {
    return this.isMonitoring;
  }

  /**
   * 清理警告历史
   */
  clearWarnings(): void {
    this.warnings = [];
    this.emit('warningsCleared');
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 磁盘空间警告
    this.diskMonitor.on('diskSpaceWarning', (warning: DiskSpaceWarning) => {
      this.addWarning('disk', warning.level, warning.message);
    });

    // 内存警告
    this.memoryMonitor.on('memoryWarning', (warning: MemoryWarning) => {
      this.addWarning('memory', warning.level, warning.message);
    });

    // 内存泄漏警告
    this.memoryMonitor.on('memoryLeak', (info: any) => {
      this.addWarning('memory', 'critical', info.message);
    });

    // 网络状态更新
    this.networkMonitor.on('networkStatusUpdate', (status: NetworkStatus) => {
      if (!status.isOnline) {
        this.addWarning('network', 'critical', '网络连接已断开');
      }
      this.emit('networkStatusUpdate', status);
    });

    // 自动优化
    if (this.config.enableAutoOptimization) {
      this.memoryMonitor.on('memoryWarning', () => {
        this.optimizeSystem();
      });
    }
  }

  /**
   * 添加警告
   */
  private addWarning(type: 'disk' | 'memory' | 'network', level: 'warning' | 'critical', message: string): void {
    const warning: SystemWarning = {
      type,
      level,
      message,
      timestamp: new Date()
    };

    this.warnings.unshift(warning);
    
    // 限制警告历史数量
    if (this.warnings.length > this.maxWarningsHistory) {
      this.warnings = this.warnings.slice(0, this.maxWarningsHistory);
    }

    this.emit('systemWarning', warning);
  }

  /**
   * 生成系统建议
   */
  private generateSuggestions(
    disk: DiskSpaceInfo | null, 
    memory: MemoryInfo, 
    network: NetworkStatus
  ): SystemSuggestion[] {
    const suggestions: SystemSuggestion[] = [];

    // 磁盘建议
    if (disk && disk.usagePercentage > 0.8) {
      suggestions.push({
        type: 'disk',
        action: 'cleanup_disk',
        description: '磁盘空间不足，建议清理不需要的文件',
        priority: 'high'
      });
    }

    // 内存建议
    const memoryOptimizations = this.memoryMonitor.getOptimizationSuggestions();
    suggestions.push(...memoryOptimizations.map(opt => ({
      type: 'memory' as const,
      action: opt.action,
      description: opt.description,
      priority: opt.priority
    })));

    // 网络建议
    const networkSuggestions = this.networkMonitor.getNetworkSuggestions();
    networkSuggestions.forEach(suggestion => {
      suggestions.push({
        type: 'network',
        action: 'network_optimization',
        description: suggestion,
        priority: 'medium'
      });
    });

    // 下载策略建议
    const strategy = this.getDownloadStrategy();
    if (strategy.maxConcurrentDownloads < 3) {
      suggestions.push({
        type: 'download',
        action: 'reduce_concurrency',
        description: `建议并发下载数量: ${strategy.maxConcurrentDownloads}`,
        priority: 'medium'
      });
    }

    return suggestions;
  }

  /**
   * 计算系统整体健康状况
   */
  private calculateOverallHealth(
    disk: DiskSpaceInfo | null, 
    memory: MemoryInfo, 
    network: NetworkStatus
  ): 'excellent' | 'good' | 'warning' | 'critical' {
    let score = 100;

    // 磁盘评分
    if (disk) {
      if (disk.usagePercentage > 0.95) score -= 30;
      else if (disk.usagePercentage > 0.9) score -= 20;
      else if (disk.usagePercentage > 0.8) score -= 10;
    }

    // 内存评分
    if (memory.usagePercentage > 0.9) score -= 25;
    else if (memory.usagePercentage > 0.8) score -= 15;
    else if (memory.usagePercentage > 0.7) score -= 5;

    // 网络评分
    if (!network.isOnline) score -= 40;
    else {
      const networkQuality = this.networkMonitor.getNetworkQuality();
      switch (networkQuality) {
        case 'poor': score -= 20; break;
        case 'fair': score -= 10; break;
        case 'good': score -= 5; break;
      }
    }

    if (score >= 90) return 'excellent';
    if (score >= 70) return 'good';
    if (score >= 50) return 'warning';
    return 'critical';
  }
}